{"version": "2.0.0", "tasks": [{"label": "colcon: build package", "type": "shell", "group": "build", "command": ["colcon build", "--symlink-install", "--base-paths ${workspaceFolder}", "--packages-select pmac_controller", "--mixin debug ccache"]}, {"label": "colcon: build (debug)", "type": "shell", "group": "build", "command": ["colcon build", "--symlink-install", "--event-handlers console_cohesion+", "--base-paths ${workspaceFolder}", "--mixin debug compile-commands"]}, {"label": "colcon: clean", "type": "shell", "group": "build", "command": ["colcon clean workspace"]}, {"label": "colcon: test", "type": "shell", "group": "build", "command": ["cd project-workspace", "source /opt/ros/humble/setup.bash;", "source install/setup.bash;", "colcon test", "--packages-select pmac_controller", "--event-handlers console_direct+;"]}, {"label": "ament: cpplint", "type": "ament", "task": "cpplint", "path": "src/", "commandOptions": "", "envSetup": "", "problemMatcher": ["$ament_cpplint"]}]}