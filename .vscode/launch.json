{"version": "0.2.0", "configurations": [{"name": "GDP: launch", "type": "cppdbg", "request": "launch", "program": "enter program name, for example, ${workspaceFolder}/build/path_to_executable", "args": [], "stopAtEntry": false, "cwd": "${fileDirname}", "environment": [], "externalConsole": false, "MIMode": "gdb", "preLaunchTask": "add your build task here. e.g. colcon: build", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "Set Disassembly Flavor to Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}]}]}