{"editor.tabSize": 2, "editor.rulers": [100], "search.exclude": {"**/build": false, "**/install": false, "**/log": true}, "clangd.arguments": ["--compile-commands-dir=${workspaceFolder}/build", "--completion-style=detailed", "--clang-tidy", "--clang-tidy-checks=-*,modernize*", "--header-insertion=never", "--log=verbose"], "cmake.configureOnOpen": false, "C_Cpp_Runner.msvcBatchPath": "", "C_Cpp_Runner.cCompilerPath": "/usr/bin/clang", "C_Cpp_Runner.cppCompilerPath": "/usr/bin/clang++", "C_Cpp_Runner.debuggerPath": "gdb", "C_Cpp_Runner.cStandard": "c11", "C_Cpp_Runner.cppStandard": "c++14", "C_Cpp_Runner.useMsvc": false, "C_Cpp_Runner.warnings": ["-Wall", "-Wextra", "-Wpedantic", "-W<PERSON>dow", "-Wformat=2", "-Wcast-align", "-Wconversion", "-Wsign-conversion", "-W<PERSON><PERSON>-dereference"], "C_Cpp_Runner.msvcWarnings": ["/W4", "/permissive-", "/w14242", "/w14287", "/w14296", "/w14311", "/w14826", "/w44062", "/w44242", "/w14905", "/w14906", "/w14263", "/w44265", "/w14928"], "C_Cpp_Runner.enableWarnings": true, "C_Cpp_Runner.warningsAsError": false, "C_Cpp_Runner.compilerArgs": [], "C_Cpp_Runner.linkerArgs": [], "C_Cpp_Runner.includePaths": [], "C_Cpp_Runner.includeSearch": ["*", "**/*"], "C_Cpp_Runner.excludeSearch": ["**/build", "**/build/**", "**/.*", "**/.*/**", "**/.vscode", "**/.vscode/**"], "C_Cpp_Runner.useAddressSanitizer": false, "C_Cpp_Runner.useUndefinedSanitizer": false, "C_Cpp_Runner.useLeakSanitizer": false, "C_Cpp_Runner.showCompilationTime": false, "C_Cpp_Runner.useLinkTimeOptimization": false, "C_Cpp_Runner.msvcSecureNoWarnings": false}