#include <kdl/chainfksolverpos_recursive.hpp>
#include <kdl/chainiksolvervel_pinv.hpp>
#include <kdl/tree.hpp>
#include <kdl_parser/kdl_parser.hpp>
#include <kdl/jntarray.hpp>
#include <rclcpp/rclcpp.hpp>
#include <trajectory_msgs/msg/joint_trajectory.hpp>

#include "control_msgs/msg/joint_controller_state.hpp"
#include "control_msgs/msg/joint_jog.hpp"

using ControlMsgReference = control_msgs::msg::JointJog;

int main(int argc, char** argv) {
	rclcpp::init(argc, argv);

	auto node = std::make_shared<rclcpp::Node>("send_trajectory");
	auto pub = node->create_publisher<trajectory_msgs::msg::JointTrajectory>(
	    "/pmac_primitive_controller/reference", 10);
	// auto pub = node->create_publisher<control_msgs::msg::JointJog>(
	//     "/pmac_primitive_controller/reference", 10);

	auto pubJog = node->create_publisher<control_msgs::msg::JointJog>(
	    "/pmac_primitive_controller/reference", 10);

	// get robot description
	auto robot_param = rclcpp::Parameter();
	node->declare_parameter("robot_description", rclcpp::ParameterType::PARAMETER_STRING);
	node->get_parameter("robot_description", robot_param);
	auto robot_description = robot_param.as_string();

	// create kinematic chain
	KDL::Tree robot_tree;
	KDL::Chain chain;
	kdl_parser::treeFromString(robot_description, robot_tree);
	robot_tree.getChain("base_link", "tool0", chain);

	auto joint_positions = KDL::JntArray(chain.getNrOfJoints());
	auto joint_velocities = KDL::JntArray(chain.getNrOfJoints());
	auto twist = KDL::Twist();
	// create KDL solvers
	trajectory_msgs::msg::JointTrajectory traj_msg;

	traj_msg.header.stamp = node->now();
	for (size_t i = 0; i < chain.getNrOfSegments(); i++) {
		auto joint = chain.getSegment(i).getJoint();
		if (joint.getType() != KDL::Joint::Fixed) {
			traj_msg.joint_names.push_back(joint.getTypeName());
		}
	}

	// points
	trajectory_msgs::msg::JointTrajectoryPoint trajectory_point_msg;
	trajectory_point_msg.positions.resize(chain.getNrOfJoints());
	trajectory_point_msg.velocities.resize(chain.getNrOfJoints());

	double total_time = 5.0;
	int trajectory_len = 200;
	int loop_rate = trajectory_len / total_time;
	double dt = 1. / loop_rate;

	for (int i = 0; i < trajectory_len; i++) {
		traj_msg.points.push_back(trajectory_point_msg);
	}

	pub->publish(traj_msg);

	// jog msg
	control_msgs::msg::JointJog jog_msg;

	jog_msg.header.stamp = node->now();
	for (size_t i = 0; i < chain.getNrOfSegments(); i++) {
		auto joint = chain.getSegment(i).getJoint();
		if (joint.getType() != KDL::Joint::Fixed) {
			jog_msg.joint_names.push_back(joint.getTypeName());
		}
	}

	//points


	pubJog->publish(jog_msg);
	while (rclcpp::ok()) {
	}
}
