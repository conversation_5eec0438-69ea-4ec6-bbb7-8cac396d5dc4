controller_manager:
  ros__parameters:
    update_rate: 100 # Hz

    joint_state_broadcaster:
      type: joint_state_broadcaster/JointStateBroadcaster

    forward_position_controller: # delete entry if controller is not applicable
      type: forward_command_controller/ForwardCommandController

    forward_velocity_controller: # delete entry if controller is not applicable
      type: forward_command_controller/ForwardCommandController

    joint_trajectory_controller: # delete entry if controller is not applicable
      type: joint_trajectory_controller/JointTrajectoryController

    pmac_primitive_controller:
      type: pmac_primitive_controller/PmacPrimitiveController

pmac_primitive_controller:
  ros__parameters:
    joints:
      - joint1
      - joint2
      - joint3
      - joint4
      - joint5
      - joint6
    interface_name: position

    command_interfaces:
      - position
      - velocity
    state_interfaces:
      - position
      - velocity

forward_position_controller: # delete entry if controller is not applicable
  ros__parameters:
    joints:
      - joint1
      - joint2
      - joint3
      - joint4
      - joint5
      - joint6
    interface_name: position

forward_velocity_controller: # delete entry if controller is not applicable
  ros__parameters:
    joints:
      - joint1
      - joint2
      - joint3
      - joint4
      - joint5
      - joint6
    interface_name: velocity

joint_trajectory_controller: # delete entry if controller is not applicable
  ros__parameters:
    joints:
      - joint1
      - joint2
      - joint3
      - joint4
      - joint5
      - joint6

    state_publish_rate: 50.0 # Defaults to 50
    action_monitor_rate: 20.0 # Defaults to 20

    allow_partial_joints_goal: false # Defaults to false
    constraints:
      stopped_velocity_tolerance: 0.01 # Defaults to 0.01
      goal_time: 0.0 # Defaults to 0.0 (start immediately)
