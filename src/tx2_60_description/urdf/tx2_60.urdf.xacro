<?xml version="1.0" encoding="UTF-8"?>
<robot xmlns:xacro="http://wiki.ros.org/xacro" name="tx2_60">

  <!-- Use this if parameters are set from the launch file, otherwise delete -->
  <xacro:arg name="prefix" default="" />

  <xacro:arg name="use_mock_hardware" default="false" />
  <xacro:arg name="mock_sensor_commands" default="false" />
  <xacro:arg name="sim_gazebo_classic" default="false" />
  <xacro:arg name="sim_gazebo" default="false" />
  <xacro:arg name="simulation_controllers" default="" />

  <xacro:include filename="$(find tx2_60_description)/urdf/tx2_60/tx2_60_macro.xacro"/>
  <xacro:include filename="$(find tx2_60_description)/urdf/tx2_60/tx2_60_macro.ros2_control.xacro"/>

  <!-- create link fixed to the "world" -->
  <link name="world" />

  <!-- Load robot's macro with parameters -->
  <!-- set prefix if multiple robots are used -->
  <xacro:tx2_60 prefix="$(arg prefix)" parent="world" >
    <origin xyz="0 0 0" rpy="0 0 0" />          <!-- position robot in the world -->
  </xacro:tx2_60>

  <xacro:tx2_60_ros2_control
    name="tx2_60"
    prefix="$(arg prefix)"
    use_mock_hardware="$(arg use_mock_hardware)"
    mock_sensor_commands="$(arg mock_sensor_commands)"
    sim_gazebo_classic="$(arg sim_gazebo_classic)"
    sim_gazebo="$(arg sim_gazebo)"
    simulation_controllers="$(arg simulation_controllers)" />

</robot>
