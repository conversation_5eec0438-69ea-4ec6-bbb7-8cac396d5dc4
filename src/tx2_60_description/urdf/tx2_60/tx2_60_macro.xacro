<?xml version="1.0"?>
<robot xmlns:xacro="http://wiki.ros.org/xacro">
  <xacro:include filename="$(find staubli_resources)/urdf/common_materials.xacro" />
  <xacro:include filename="$(find tx2_60_description)/urdf/common.xacro" />

    <!-- links main serial chain -->
  <xacro:macro name="tx2_60" params="prefix parent *orgin">
    <link name="${prefix}base_link">
      <visual>
        <origin xyz="0 0 0" rpy="0 0 0" />
        <geometry>
          <mesh filename="package://staubli_tx2_60_support/meshes/tx2_60/visual/base_link.stl" />
        </geometry>
        <xacro:material_staubli_ral_melon_yellow />
      </visual>
      <collision>
        <origin xyz="0 0 0" rpy="0 0 0" />
        <geometry>
          <mesh filename="package://staubli_tx2_60_support/meshes/tx2_60/collision/base_link.stl" />
        </geometry>
      </collision>
    </link>
    <link name="${prefix}link_1">
      <visual>
        <origin xyz="0 0 0" rpy="0 0 0" />
        <geometry>
          <mesh filename="package://staubli_tx2_60_support/meshes/tx2_60/visual/link_1.stl" />
        </geometry>
        <xacro:material_staubli_ral_melon_yellow />
      </visual>
      <collision>
        <origin xyz="0 0 0" rpy="0 0 0" />
        <geometry>
          <mesh filename="package://staubli_tx2_60_support/meshes/tx2_60/collision/link_1.stl" />
        </geometry>
      </collision>
    </link>
    <link name="${prefix}link_2">
      <visual>
        <origin xyz="0 0 0" rpy="0 0 0" />
        <geometry>
          <mesh filename="package://staubli_tx2_60_support/meshes/tx2_60/visual/link_2.stl" />
        </geometry>
        <xacro:material_staubli_ral_melon_yellow />
      </visual>
      <collision>
        <origin xyz="0 0 0" rpy="0 0 0" />
        <geometry>
          <mesh filename="package://staubli_tx2_60_support/meshes/tx2_60/collision/link_2.stl" />
        </geometry>
      </collision>
    </link>
    <link name="${prefix}link_3">
      <visual>
        <origin xyz="0 0 0" rpy="0 0 0" />
        <geometry>
          <mesh filename="package://staubli_tx2_60_support/meshes/tx2_60/visual/link_3.stl" />
        </geometry>
        <xacro:material_staubli_ral_melon_yellow />
      </visual>
      <collision>
        <origin xyz="0 0 0" rpy="0 0 0" />
        <geometry>
          <mesh filename="package://staubli_tx2_60_support/meshes/tx2_60/collision/link_3.stl" />
        </geometry>
      </collision>
    </link>
    <link name="${prefix}link_4">
      <visual>
        <origin xyz="0 0 0" rpy="0 0 0" />
        <geometry>
          <mesh filename="package://staubli_tx2_60_support/meshes/tx2_60/visual/link_4.stl" />
        </geometry>
        <xacro:material_staubli_ral_melon_yellow />
      </visual>
      <collision>
        <origin xyz="0 0 0" rpy="0 0 0" />
        <geometry>
          <mesh filename="package://staubli_tx2_60_support/meshes/tx2_60/collision/link_4.stl" />
        </geometry>
      </collision>
    </link>
    <link name="${prefix}link_5">
      <visual>
        <origin xyz="0 0 0" rpy="0 0 0" />
        <geometry>
          <mesh filename="package://staubli_tx60_support/meshes/tx60/visual/link_5.stl" />
        </geometry>
        <xacro:material_staubli_ral_grey_aluminium />
      </visual>
      <collision>
        <origin xyz="0 0 0" rpy="0 0 0" />
        <geometry>
          <mesh filename="package://staubli_tx60_support/meshes/tx60/collision/link_5.stl" />
        </geometry>
      </collision>
    </link>
    <link name="${prefix}link_6">
      <visual>
        <origin xyz="0 0 0" rpy="0 0 0" />
        <geometry>
          <mesh filename="package://staubli_tx60_support/meshes/tx60/visual/link_6.stl" />
        </geometry>
        <xacro:material_staubli_ral_grey_aluminium />
      </visual>
      <collision>
        <origin xyz="0 0 0" rpy="0 0 0" />
        <geometry>
          <mesh filename="package://staubli_tx60_support/meshes/tx60/collision/link_6.stl" />
        </geometry>
      </collision>
    </link>

    <!-- base_joint fixes base_link to the environment -->
    <joint name="${prefix}base" type="fixed">
      <parent link="world" />
      <child link="${prefix}base_link" />
      <origin xyz="0 0 0" rpy="0 0 0" />
      <axis xyz="0 0 1" />
    </joint>

    <!-- joints main serial chain -->
    <joint name="${prefix}joint1" type="revolute">
      <origin xyz="0 0 0.375" rpy="0 0 0" />
      <parent link="${prefix}base_link" />
      <child link="${prefix}link_1" />
      <axis xyz="0 0 1" />
      <!-- from Staubli instruction manual
      (1) effort limit = 126 Nm if floor mounted
      (2) effort limit = 40 Nm if wall mounted -->
      <!-- nominal velocity limits are used -->
      <limit lower="${radians(-180)}" upper="${radians(180)}" effort="126.0"
        velocity="${radians(435)}" />
    </joint>
    <joint name="${prefix}joint2" type="revolute">
      <origin xyz="0 0 0" rpy="0 0 0" />
      <parent link="${prefix}link_1" />
      <child link="${prefix}link_2" />
      <axis xyz="0 1 0" />
      <!-- nominal velocity limits are used -->
      <limit lower="${radians(-127.5)}" upper="${radians(127.5)}" effort="47.0"
        velocity="${radians(385)}" />
    </joint>
    <joint name="${prefix}joint3" type="revolute">
      <origin xyz="0 0.02 0.29" rpy="0 0 0" />
      <parent link="${prefix}link_2" />
      <child link="${prefix}link_3" />
      <axis xyz="0 1 0" />
      <!-- nominal velocity limits are used -->
      <limit lower="${radians(-152.5)}" upper="${radians(152.5)}" effort="32.0"
        velocity="${radians(500)}" />
    </joint>
    <joint name="${prefix}joint4" type="revolute">
      <origin xyz="0 0 0" rpy="0 0 0" />
      <parent link="${prefix}link_3" />
      <child link="${prefix}link_4" />
      <axis xyz="0 0 1" />
      <!-- nominal velocity limits are used -->
      <limit lower="${radians(-270.0)}" upper="${radians(270.0)}" effort="7.0"
        velocity="${radians(995)}" />
    </joint>
    <joint name="${prefix}joint5" type="revolute">
      <origin xyz="0 0 0.31" rpy="0 0 0" />
      <parent link="${prefix}link_4" />
      <child link="${prefix}link_5" />
      <axis xyz="0 1 0" />
      <!-- nominal velocity limits are used -->
      <limit lower="${radians(-121.0)}" upper="${radians(132.5)}" effort="8.0"
        velocity="${radians(1065)}" />
    </joint>
    <joint name="${prefix}joint6" type="revolute">
      <origin xyz="0 0 0.07" rpy="0 0 0" />
      <parent link="${prefix}link_5" />
      <child link="${prefix}link_6" />
      <axis xyz="0 0 1" />
      <!-- nominal velocity limits are used -->
      <limit lower="${radians(-270.0)}" upper="${radians(270.0)}" effort="2.0"
        velocity="${radians(1445)}" />
    </joint>


    <!-- ROS-Industrial 'base' frame base_link to Staubli World Coordinates transform -->
    <link name="${prefix}base" />
    <joint name="${prefix}base_link-base" type="fixed">
      <origin xyz="0 0 0.375" rpy="0 0 0" />
      <parent link="${prefix}base_link" />
      <child link="${prefix}base" />
    </joint>

    <!-- ROS-Industrial 'flange' frame attachment point for EEF models -->
    <link name="${prefix}flange" />
    <joint name="${prefix}joint_6-flange" type="fixed">
      <origin xyz="0 0 0" rpy="0 ${radians(0.0)} 0" />
      <parent link="${prefix}link_6" />
      <child link="${prefix}flange" />
    </joint>

    <!-- ROS-Industrial 'tool0' frame all-zeros tool frame -->
    <link name="${prefix}tool0" />
    <joint name="${prefix}flange-tool0" type="fixed">
      <origin xyz="0.0 0 0.10" rpy="0 ${radians(0.0)} 0" />
      <parent link="${prefix}flange" />
      <child link="${prefix}tool0" />
    </joint>
  </xacro:macro>
</robot>