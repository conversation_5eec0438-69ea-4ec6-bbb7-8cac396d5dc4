<?xml version="1.0" encoding="UTF-8"?>
<robot xmlns:xacro="http://wiki.ros.org/xacro">

  <xacro:macro name="tx2_60_ros2_control" params="
               name
               prefix
               use_mock_hardware:=^|false
               mock_sensor_commands:=^|false
               sim_gazebo_classic:=^|false
               sim_gazebo:=^|false
               simulation_controllers"
               >

    <ros2_control name="${name}" type="system">
      <hardware>
        <xacro:if value="${use_mock_hardware}">
          <plugin>mock_components/GenericSystem</plugin>
          <param name="mock_sensor_commands">${mock_sensor_commands}</param>
        </xacro:if>
        <xacro:if value="${sim_gazebo_classic}">
          <plugin>gazebo_ros2_control/GazeboSystem</plugin>
        </xacro:if>
        <xacro:if value="${sim_gazebo}">
          <plugin>ign_ros2_control/IgnitionSystem</plugin>
        </xacro:if>
        <xacro:unless value="${use_mock_hardware or sim_gazebo_classic or sim_gazebo}">
          <!-- <plugin>robot_hardware_inteface/RobotHardwareInteface</plugin> -->
          <plugin>pmac_hardware_interface/Ck3mPrimitive</plugin>
        </xacro:unless>
      </hardware>


      <joint name="${prefix}joint1">
        <command_interface name="position">
          <param name="min">{-2*pi}</param>
          <param name="max">{2*pi}</param>
        </command_interface>
        <command_interface name="velocity">
          <param name="min">-3.15</param>
          <param name="max">3.15</param>
        </command_interface>
        <state_interface name="position">
          <param name="initial_value">0.0</param>
        </state_interface>
        <state_interface name="velocity" >
          <param name="initial_value">0.0</param>
        </state_interface>
      </joint>

      <joint name="${prefix}joint2">
        <command_interface name="position">
          <param name="min">{-2*pi}</param>
          <param name="max">{2*pi}</param>
        </command_interface>
        <command_interface name="velocity">
          <param name="min">-3.15</param>
          <param name="max">3.15</param>
        </command_interface>
        <state_interface name="position">
          <param name="initial_value">0.0</param>
        </state_interface>
        <state_interface name="velocity" >
          <param name="initial_value">0.0</param>
        </state_interface>
      </joint>

      <joint name="${prefix}joint3">
        <command_interface name="position">
          <param name="min">{-pi}</param>
          <param name="max">{pi}</param>
        </command_interface>
        <command_interface name="velocity">
          <param name="min">-3.15</param>
          <param name="max">3.15</param>
        </command_interface>
        <state_interface name="position">
          <param name="initial_value">0.0</param>
        </state_interface>
        <state_interface name="velocity" >
          <param name="initial_value">0.0</param>
        </state_interface>
      </joint>

      <joint name="${prefix}joint4">
        <command_interface name="position">
          <param name="min">{-2*pi}</param>
          <param name="max">{2*pi}</param>
        </command_interface>
        <command_interface name="velocity">
          <param name="min">-3.2</param>
          <param name="max">3.2</param>
        </command_interface>
        <state_interface name="position">
          <param name="initial_value">0.0</param>
        </state_interface>
        <state_interface name="velocity" >
          <param name="initial_value">0.0</param>
        </state_interface>
      </joint>

      <joint name="${prefix}joint5">
        <command_interface name="position">
          <param name="min">{-2*pi}</param>
          <param name="max">{2*pi}</param>
        </command_interface>
        <command_interface name="velocity">
          <param name="min">-3.2</param>
          <param name="max">3.2</param>
        </command_interface>
        <state_interface name="position">
          <param name="initial_value">0.0</param>
        </state_interface>
        <state_interface name="velocity" >
          <param name="initial_value">0.0</param>
        </state_interface>
      </joint>

      <joint name="${prefix}joint6">
        <command_interface name="position">
          <param name="min">{-2*pi}</param>
          <param name="max">{2*pi}</param>
        </command_interface>
        <command_interface name="velocity">
          <param name="min">-3.2</param>
          <param name="max">3.2</param>
        </command_interface>
        <state_interface name="position">
          <param name="initial_value">0.0</param>
        </state_interface>
        <state_interface name="velocity" >
          <param name="initial_value">0.0</param>
        </state_interface>
      </joint>
      <sensor name="tcp_fts_sensor">            <!-- Remove/change sensors as needed -->
        <state_interface name="fx"/>
        <state_interface name="fy"/>
        <state_interface name="fz"/>
        <state_interface name="tx"/>
        <state_interface name="ty"/>
        <state_interface name="tz"/>
        <param name="frame_id">tool0</param>    <!-- Change sensors as needed -->
        <param name="min_fx">-100</param>
        <param name="max_fx">100</param>
        <param name="min_fy">-100</param>
        <param name="max_fy">100</param>
        <param name="min_fz">-200</param>
        <param name="max_fz">200</param>
        <param name="min_tx">-10</param>
        <param name="max_tx">10</param>
        <param name="min_ty">-10</param>
        <param name="max_ty">10</param>
        <param name="min_tz">-15</param>
        <param name="max_tz">15</param>
      </sensor>
    </ros2_control>

    <xacro:if value="$(arg sim_gazebo_classic)">
      <!-- Gazebo plugins -->
      <gazebo reference="world">
      </gazebo>
      <gazebo>
        <plugin filename="libgazebo_ros2_control.so" name="gazebo_ros2_control">
          <parameters>${simulation_controllers}</parameters>
        </plugin>
      </gazebo>
    </xacro:if>

    <xacro:if value="$(arg sim_gazebo)">
      <!-- Gazebo plugins -->
      <gazebo reference="world">
      </gazebo>
      <gazebo>
        <plugin filename="libign_ros2_control-system.so" name="ign_ros2_control::IgnitionROS2ControlPlugin">
          <parameters>${simulation_controllers}</parameters>
          <controller_manager_node_name>${prefix}controller_manager</controller_manager_node_name>
        </plugin>
      </gazebo>
    </xacro:if>

  </xacro:macro>
</robot>
