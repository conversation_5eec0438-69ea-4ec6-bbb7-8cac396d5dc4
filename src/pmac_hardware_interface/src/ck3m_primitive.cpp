// Copyright (c) 2022, Stogl Robotics Consulting UG (haftungsbeschränkt) (template)
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include <limits>
#include <vector>

#include "pmac_hardware_interface/ck3m_primitive.hpp"
#include "hardware_interface/types/hardware_interface_type_values.hpp"
#include "rclcpp/rclcpp.hpp"

namespace pmac_hardware_interface {
hardware_interface::CallbackReturn
Ck3mPrimitive::on_init(const hardware_interface::HardwareInfo& info) {
	if (hardware_interface::SystemInterface::on_init(info) != CallbackReturn::SUCCESS) {
		return CallbackReturn::ERROR;
	}

	// TODO(anyone): read parameters and initialize the hardware
	hw_states_.resize(info_.joints.size(), std::numeric_limits<double>::quiet_NaN());
	hw_commands_.resize(info_.joints.size(), std::numeric_limits<double>::quiet_NaN());

	joint_position_.assign(info_.joints.size(), 0);
	joint_velocities_.assign(info_.joints.size(), 0);
	joint_position_command_.assign(6, 0);
	joint_velocities_command_.assign(6, 0);

	// force sensor has 6 readings
	ft_states_.assign(6, 0);
	ft_command_.assign(6, 0);

	for (const auto& joint : info_.joints) {
		for (const auto& interface : joint.state_interfaces) {
			joint_interfaces[interface.name].push_back(joint.name);
		}
	}

	return CallbackReturn::SUCCESS;
}

hardware_interface::CallbackReturn
Ck3mPrimitive::on_configure(const rclcpp_lifecycle::State& /*previous_state*/) {
	// TODO(anyone): prepare the robot to be ready for read calls and write calls of some
	// interfaces

	RCLCPP_INFO(rclcpp::get_logger(this->get_name()), "CK3M on configure...");
	return CallbackReturn::SUCCESS;
}

std::vector<hardware_interface::StateInterface> Ck3mPrimitive::export_state_interfaces() {
	std::vector<hardware_interface::StateInterface> state_interfaces;

	for (size_t i = 0; i < info_.joints.size(); ++i) {
		state_interfaces.emplace_back(hardware_interface::StateInterface(
		    // TODO(anyone): insert correct interfaces
		    info_.joints[i].name, hardware_interface::HW_IF_POSITION, &joint_position_[i]));
		state_interfaces.emplace_back(hardware_interface::StateInterface(
		    info_.joints[i].name, hardware_interface::HW_IF_VELOCITY, &joint_velocities_[i]));
	}

	state_interfaces.emplace_back("tcp_fts_sensor", "fx", &ft_states_[0]);
	state_interfaces.emplace_back("tcp_fts_sensor", "fy", &ft_states_[1]);
	state_interfaces.emplace_back("tcp_fts_sensor", "fz", &ft_states_[2]);
	state_interfaces.emplace_back("tcp_fts_sensor", "tx", &ft_states_[3]);
	state_interfaces.emplace_back("tcp_fts_sensor", "ty", &ft_states_[4]);
	state_interfaces.emplace_back("tcp_fts_sensor", "tz", &ft_states_[5]);

	return state_interfaces;
}

std::vector<hardware_interface::CommandInterface>
Ck3mPrimitive::export_command_interfaces() {
	std::vector<hardware_interface::CommandInterface> command_interfaces;
	for (size_t i = 0; i < info_.joints.size(); ++i) {
		command_interfaces.emplace_back(hardware_interface::CommandInterface(
		    // TODO(anyone): insert correct interfaces
		    // info_.joints[i].name, hardware_interface::HW_IF_POSITION, &hw_commands_[i]));
		    info_.joints[i].name, hardware_interface::HW_IF_POSITION,
		    &joint_position_command_[i]));

		command_interfaces.emplace_back(hardware_interface::CommandInterface(
		    info_.joints[i].name, hardware_interface::HW_IF_VELOCITY,
		    &joint_velocities_command_[i]));
	}

	return command_interfaces;
}

hardware_interface::CallbackReturn
Ck3mPrimitive::on_activate(const rclcpp_lifecycle::State& /*previous_state*/) {
	// TODO(anyone): prepare the robot to receive commands

	return CallbackReturn::SUCCESS;
}

hardware_interface::CallbackReturn
Ck3mPrimitive::on_deactivate(const rclcpp_lifecycle::State& /*previous_state*/) {
	// TODO(anyone): prepare the robot to stop receiving commands

	return CallbackReturn::SUCCESS;
}

hardware_interface::return_type Ck3mPrimitive::read(const rclcpp::Time& /*time*/,
                                                    const rclcpp::Duration& /*period*/) {
	// TODO(anyone): read robot states

	return hardware_interface::return_type::OK;
}

hardware_interface::return_type Ck3mPrimitive::write(const rclcpp::Time& /*time*/,
                                                     const rclcpp::Duration& /*period*/) {
	// TODO(anyone): write robot's commands'
	// RCLCPP_INFO(rclcpp::get_logger(this->get_name()), "ck3m write");

	return hardware_interface::return_type::OK;
}

}  // namespace pmac_hardware_interface

#include "pluginlib/class_list_macros.hpp"

PLUGINLIB_EXPORT_CLASS(pmac_hardware_interface::Ck3mPrimitive,
                       hardware_interface::SystemInterface)
