// Copyright (c) 2022, Stogl Robotics Consulting UG (haftungsbeschränkt) (template)
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef PMAC_HARDWARE_INTERFACE__CK3M_PRIMITIVE_HPP_
#define PMAC_HARDWARE_INTERFACE__CK3M_PRIMITIVE_HPP_

#include <string>
#include <vector>

#include "pmac_hardware_interface/visibility_control.h"
#include "hardware_interface/system_interface.hpp"
#include "hardware_interface/handle.hpp"
#include "hardware_interface/hardware_info.hpp"
#include "hardware_interface/types/hardware_interface_return_values.hpp"
#include "rclcpp/macros.hpp"
#include "rclcpp_lifecycle/state.hpp"

namespace pmac_hardware_interface {
class Ck3mPrimitive : public hardware_interface::SystemInterface
{
	public:
	TEMPLATES__ROS2_CONTROL__VISIBILITY_PUBLIC
	hardware_interface::CallbackReturn
	on_init(const hardware_interface::HardwareInfo& info) override;

	TEMPLATES__ROS2_CONTROL__VISIBILITY_PUBLIC
	hardware_interface::CallbackReturn
	on_configure(const rclcpp_lifecycle::State& previous_state) override;

	TEMPLATES__ROS2_CONTROL__VISIBILITY_PUBLIC
	std::vector<hardware_interface::StateInterface> export_state_interfaces() override;

	TEMPLATES__ROS2_CONTROL__VISIBILITY_PUBLIC
	std::vector<hardware_interface::CommandInterface> export_command_interfaces() override;

	TEMPLATES__ROS2_CONTROL__VISIBILITY_PUBLIC
	hardware_interface::CallbackReturn
	on_activate(const rclcpp_lifecycle::State& previous_state) override;

	TEMPLATES__ROS2_CONTROL__VISIBILITY_PUBLIC
	hardware_interface::CallbackReturn
	on_deactivate(const rclcpp_lifecycle::State& previous_state) override;

	TEMPLATES__ROS2_CONTROL__VISIBILITY_PUBLIC
	hardware_interface::return_type read(const rclcpp::Time& time,
	                                     const rclcpp::Duration& period) override;

	TEMPLATES__ROS2_CONTROL__VISIBILITY_PUBLIC
	hardware_interface::return_type write(const rclcpp::Time& time,
	                                      const rclcpp::Duration& period) override;

	private:
	std::vector<double> hw_commands_;
	std::vector<double> hw_states_;
	/// The size of this vector is (standard_interfaces_.size() x nr_joints)
	std::vector<double> joint_position_command_;
	std::vector<double> joint_velocities_command_;
	std::vector<double> joint_position_;
	std::vector<double> joint_velocities_;
	std::vector<double> ft_states_;
	std::vector<double> ft_command_;

	std::unordered_map<std::string, std::vector<std::string>> joint_interfaces = {
		{ "position", {} },
		{ "velocity", {} }
	};
};

}  // namespace pmac_hardware_interface

#endif  // PMAC_HARDWARE_INTERFACE__CK3M_PRIMITIVE_HPP_
