// Copyright (c) 2022, Stogl Robotics Consulting UG (haftungsbeschränkt) (template)
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef PMAC_PRIMITIVE_CONTROLLER__PMAC_PRIMITIVE_CONTROLLER_HPP_
#define PMAC_PRIMITIVE_CONTROLLER__PMAC_PRIMITIVE_CONTROLLER_HPP_

#include <memory>
#include <string>
#include <vector>

#include "controller_interface/controller_interface.hpp"
#include "pmac_primitive_controller_parameters.hpp"
#include "pmac_primitive_controller/visibility_control.h"
#include "rclcpp_lifecycle/node_interfaces/lifecycle_node_interface.hpp"
#include "rclcpp_lifecycle/state.hpp"
#include "realtime_tools/realtime_buffer.h"
#include "realtime_tools/realtime_publisher.h"
#include "std_srvs/srv/set_bool.hpp"
#include "hardware_interface/types/hardware_interface_type_values.hpp"

// TODO(anyone): Replace with controller specific messages
#include "control_msgs/msg/joint_controller_state.hpp"
#include "control_msgs/msg/joint_jog.hpp"
// #include <trajectory_msgs/msg/joint_trajectory.hpp>

namespace pmac_primitive_controller {
// name constants for state interfaces
static constexpr size_t STATE_MY_ITFS = 0;

// name constants for command interfaces
static constexpr size_t CMD_MY_ITFS = 0;

// TODO(anyone: example setup for control mode (usually you will use some enums defined in
// messages)
enum class control_mode_type : std::uint8_t
{
	FAST = 0,
	SLOW = 1,
};

class PmacPrimitiveController : public controller_interface::ControllerInterface
{
	public:
	PMAC_PRIMITIVE_CONTROLLER__VISIBILITY_PUBLIC
	PmacPrimitiveController();

	PMAC_PRIMITIVE_CONTROLLER__VISIBILITY_PUBLIC
	auto on_init() -> controller_interface::CallbackReturn override;

	PMAC_PRIMITIVE_CONTROLLER__VISIBILITY_PUBLIC
	auto command_interface_configuration() const
	    -> controller_interface::InterfaceConfiguration override;

	PMAC_PRIMITIVE_CONTROLLER__VISIBILITY_PUBLIC
	auto state_interface_configuration() const
	    -> controller_interface::InterfaceConfiguration override;

	PMAC_PRIMITIVE_CONTROLLER__VISIBILITY_PUBLIC
	auto on_configure(const rclcpp_lifecycle::State& previous_state)
	    -> controller_interface::CallbackReturn override;

	PMAC_PRIMITIVE_CONTROLLER__VISIBILITY_PUBLIC
	auto on_activate(const rclcpp_lifecycle::State& previous_state)
	    -> controller_interface::CallbackReturn override;

	PMAC_PRIMITIVE_CONTROLLER__VISIBILITY_PUBLIC
	auto on_deactivate(const rclcpp_lifecycle::State& previous_state)
	    -> controller_interface::CallbackReturn override;

	PMAC_PRIMITIVE_CONTROLLER__VISIBILITY_PUBLIC
	auto update(const rclcpp::Time& time, const rclcpp::Duration& period)
	    -> controller_interface::return_type override;

	// TODO(anyone): replace the state and command message types
	// using ControllerReferenceMsg = trajectory_msgs::msg::JointTrajectory;
	using ControllerReferenceMsg = control_msgs::msg::JointJog;
	using ControllerModeSrvType = std_srvs::srv::SetBool;
	using ControllerStateMsg = control_msgs::msg::JointControllerState;
	// using ControllerStateMsg = trajectory_msgs::msg::JointTrajectoryPoint;

	protected:
	std::shared_ptr<pmac_primitive_controller::ParamListener> param_listener_;
	pmac_primitive_controller::Params params_;

	std::vector<std::string> state_joints_;
	std::vector<std::string> command_interface_types_;
	std::vector<std::string> state_interface_types_;

	// Command subscribers and Controller State publisher
	rclcpp::Subscription<ControllerReferenceMsg>::SharedPtr ref_subscriber_ = nullptr;
	realtime_tools::RealtimeBuffer<std::shared_ptr<ControllerReferenceMsg>> input_ref_;

	rclcpp::Service<ControllerModeSrvType>::SharedPtr set_slow_control_mode_service_;
	realtime_tools::RealtimeBuffer<control_mode_type> control_mode_;

	using ControllerStatePublisher = realtime_tools::RealtimePublisher<ControllerStateMsg>;

	rclcpp::Publisher<ControllerStateMsg>::SharedPtr s_publisher_;
	std::unique_ptr<ControllerStatePublisher> state_publisher_;

	/*
	*****************************************************************************
	*/

	const std::vector<std::string> standard_interfaces_ = {
		hardware_interface::HW_IF_POSITION, hardware_interface::HW_IF_VELOCITY,
		hardware_interface::HW_IF_ACCELERATION, hardware_interface::HW_IF_EFFORT
	};

	std::vector<std::reference_wrapper<hardware_interface::LoanedCommandInterface>>
	    joint_position_command_interface_;
	std::vector<std::reference_wrapper<hardware_interface::LoanedCommandInterface>>
	    joint_velocity_command_interface_;
	std::vector<std::reference_wrapper<hardware_interface::LoanedStateInterface>>
	    joint_position_state_interface_;
	std::vector<std::reference_wrapper<hardware_interface::LoanedStateInterface>>
	    joint_velocity_state_interface_;

	std::unordered_map<
	    std::string,
	    std::vector<std::reference_wrapper<hardware_interface::LoanedCommandInterface>>*>
	    command_interface_map_ = { { "position", &joint_position_command_interface_ },
		                             { "velocity", &joint_velocity_command_interface_ } };

	std::unordered_map<
	    std::string,
	    std::vector<std::reference_wrapper<hardware_interface::LoanedStateInterface>>*>
	    state_interface_map_ = { { "position", &joint_position_state_interface_ },
		                           { "velocity", &joint_velocity_state_interface_ } };

	private:
	// callback for topic interface
	PMAC_PRIMITIVE_CONTROLLER__VISIBILITY_LOCAL
	void reference_callback(const std::shared_ptr<ControllerReferenceMsg> msg);
};

}  // namespace pmac_primitive_controller

#endif  // PMAC_PRIMITIVE_CONTROLLER__PMAC_PRIMITIVE_CONTROLLER_HPP_
