[0mCMake Deprecation Warning at CMakeLists.txt:1 (cmake_minimum_required):
  Compatibility with CMake < 3.10 will be removed from a future version of
  CMake.

  Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
  to tell CMake that the project requires at least <min> but has been updated
  to work with policies introduced by <max> or earlier.

[0m
In file included from [01m[K/home/<USER>/code/pmac_robot/src/pmac_primitive_controller/include/pmac_primitive_controller/pmac_primitive_controller.hpp:23[m[K,
                 from [01m[K/home/<USER>/code/pmac_robot/src/pmac_primitive_controller/src/pmac_primitive_controller.cpp:15[m[K:
[01m[K/home/<USER>/code/pmac_robot/build/pmac_primitive_controller/include/pmac_primitive_controller_parameters.hpp:1:178:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: #include "pmac_primitive_controller_parameters.hpp" is deprecated. Use #include <pmac_primitive_controller/pmac_primitive_controller_parameters.hpp> instead.[m[K’
    1 | #pragma message("#include \"pmac_primitive_controller_parameters.hpp\" is deprecated. Use #include <pmac_primitive_controller/pmac_primitive_controller_parameters.hpp> instead."[01;36m[K)[m[K
      |                                                                                                                                                                                  [01;36m[K^[m[K
In file included from [01m[K/home/<USER>/code/pmac_robot/build/pmac_primitive_controller/include/pmac_primitive_controller_parameters.hpp:31[m[K,
                 from [01m[K/home/<USER>/code/pmac_robot/src/pmac_primitive_controller/include/pmac_primitive_controller/pmac_primitive_controller.hpp:23[m[K,
                 from [01m[K/home/<USER>/code/pmac_robot/src/pmac_primitive_controller/src/pmac_primitive_controller.cpp:15[m[K:
[01m[K/home/<USER>/code/pmac_robot/build/pmac_primitive_controller/include/validate_pmac_primitive_controller_parameters.hpp:25:75:[m[K [01;35m[Kwarning: [m[K‘[01m[Kusing Result = class tl::expected<void, std::__cxx11::basic_string<char> >[m[K’ is deprecated: Use tl::expected<void, std::string> for return instead. `#include <tl_expected/expected.hpp>`. [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wdeprecated-declarations-Wdeprecated-declarations]8;;[m[K]
   25 | Result forbidden_interface_name_prefix(rclcpp::Parameter const & parameter[01;35m[K)[m[K
      |                                                                           [01;35m[K^[m[K
In file included from [01m[K/home/<USER>/code/pmac_robot/build/pmac_primitive_controller/include/pmac_primitive_controller_parameters.hpp:25[m[K,
                 from [01m[K/home/<USER>/code/pmac_robot/src/pmac_primitive_controller/include/pmac_primitive_controller/pmac_primitive_controller.hpp:23[m[K,
                 from [01m[K/home/<USER>/code/pmac_robot/src/pmac_primitive_controller/src/pmac_primitive_controller.cpp:15[m[K:
[01m[K/opt/ros/humble/include/parameter_traits/parameter_traits/parameter_traits.hpp:37:7:[m[K [01;36m[Knote: [m[Kdeclared here
   37 | using [01;36m[KResult[m[K
      |       [01;36m[K^~~~~~[m[K
In file included from [01m[K/home/<USER>/code/pmac_robot/src/pmac_primitive_controller/include/pmac_primitive_controller/pmac_primitive_controller.hpp:27[m[K,
                 from [01m[K/home/<USER>/code/pmac_robot/src/pmac_primitive_controller/src/pmac_primitive_controller.cpp:15[m[K:
[01m[K/home/<USER>/code/ros2_control/install/include/realtime_tools/realtime_tools/realtime_buffer.h:22:100:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: This header include is deprecated. Please update your code to use 'realtime_buffer.hpp' header.[m[K’
   22 |   "This header include is deprecated. Please update your code to use 'realtime_buffer.hpp' header."[01;36m[K)[m[K  //NOLINT
      |                                                                                                    [01;36m[K^[m[K
In file included from [01m[K/home/<USER>/code/pmac_robot/src/pmac_primitive_controller/include/pmac_primitive_controller/pmac_primitive_controller.hpp:28[m[K,
                 from [01m[K/home/<USER>/code/pmac_robot/src/pmac_primitive_controller/src/pmac_primitive_controller.cpp:15[m[K:
[01m[K/home/<USER>/code/ros2_control/install/include/realtime_tools/realtime_tools/realtime_publisher.h:22:103:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: This header include is deprecated. Please update your code to use 'realtime_publisher.hpp' header.[m[K’
   22 |   "This header include is deprecated. Please update your code to use 'realtime_publisher.hpp' header."[01;36m[K)[m[K  //NOLINT
      |                                                                                                       [01;36m[K^[m[K
