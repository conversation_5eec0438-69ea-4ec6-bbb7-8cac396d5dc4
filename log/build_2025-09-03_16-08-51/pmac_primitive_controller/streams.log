[0.035s] Invoking command in '/home/<USER>/code/pmac_robot/build/pmac_primitive_controller': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake /home/<USER>/code/pmac_robot/src/pmac_primitive_controller -DCMAKE_BUILD_TYPE=RelWithDebInfo -DBUILD_TESTING=0 -DCMAKE_EXPORT_COMPILE_COMMANDS=On -Wall -Wextra -Wpedantic -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/pmac_robot/install
[0.047s] [0mCMake Deprecation Warning at CMakeLists.txt:1 (cmake_minimum_required):
[0.047s]   Compatibility with CMake < 3.10 will be removed from a future version of
[0.047s]   CMake.
[0.047s] 
[0.047s]   Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
[0.047s]   to tell CMake that the project requires at least <min> but has been updated
[0.047s]   to work with policies introduced by <max> or earlier.
[0.047s] 
[0.047s] [0m
[0.138s] -- The C compiler identification is GNU 11.4.0
[0.266s] -- The CXX compiler identification is GNU 11.4.0
[0.281s] -- Detecting C compiler ABI info
[0.377s] -- Detecting C compiler ABI info - done
[0.406s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.406s] -- Detecting C compile features
[0.407s] -- Detecting C compile features - done
[0.425s] -- Detecting CXX compiler ABI info
[0.525s] -- Detecting CXX compiler ABI info - done
[0.556s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.556s] -- Detecting CXX compile features
[0.557s] -- Detecting CXX compile features - done
[0.570s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.777s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter
[0.857s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.996s] -- Found generate_parameter_library: 0.5.0 (/opt/ros/humble/share/generate_parameter_library/cmake)
[1.015s] -- Found parameter_traits: 0.5.0 (/opt/ros/humble/share/parameter_traits/cmake)
[1.186s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[1.206s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[1.240s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[1.284s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[1.342s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[1.546s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[1.559s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[1.782s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")
[1.850s] -- Found FastRTPS: /opt/ros/humble/include
[1.921s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[1.942s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[2.024s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[2.025s] -- Found Threads: TRUE
[2.204s] -- Found rclcpp_lifecycle: 16.0.14 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)
[2.244s] -- Found control_msgs: 4.8.0 (/home/<USER>/code/ros2_control/install/share/control_msgs/cmake)
[2.441s] -- Found controller_interface: 2.51.0 (/home/<USER>/code/ros2_control/install/share/controller_interface/cmake)
[2.521s] -- Found realtime_tools: 2.14.0 (/home/<USER>/code/ros2_control/install/share/realtime_tools/cmake)
[2.562s] -- Found std_srvs: 4.9.0 (/opt/ros/humble/share/std_srvs/cmake)
[2.705s] -- Configuring done (2.7s)
[2.729s] -- Generating done (0.0s)
[2.733s] -- Build files have been written to: /home/<USER>/code/pmac_robot/build/pmac_primitive_controller
[2.747s] Invoked command in '/home/<USER>/code/pmac_robot/build/pmac_primitive_controller' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake /home/<USER>/code/pmac_robot/src/pmac_primitive_controller -DCMAKE_BUILD_TYPE=RelWithDebInfo -DBUILD_TESTING=0 -DCMAKE_EXPORT_COMPILE_COMMANDS=On -Wall -Wextra -Wpedantic -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/pmac_robot/install
[2.750s] Invoking command in '/home/<USER>/code/pmac_robot/build/pmac_primitive_controller': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake --build /home/<USER>/code/pmac_robot/build/pmac_primitive_controller -- -j8 -l8
[2.790s] [ 25%] [1m[34mRunning `/opt/ros/humble/bin/generate_parameter_library_cpp /home/<USER>/code/pmac_robot/build/pmac_primitive_controller/include/pmac_primitive_controller/pmac_primitive_controller_parameters.hpp /home/<USER>/code/pmac_robot/src/pmac_primitive_controller/src/pmac_primitive_controller.yaml validate_pmac_primitive_controller_parameters.hpp`[0m
[3.050s] [ 50%] [1m[34mCreating deprecated header file /home/<USER>/code/pmac_robot/build/pmac_primitive_controller/include/pmac_primitive_controller_parameters.hpp[0m
[3.061s] [ 50%] Built target pmac_primitive_controller_parameters
[3.077s] [ 75%] [32mBuilding CXX object CMakeFiles/pmac_primitive_controller.dir/src/pmac_primitive_controller.cpp.o[0m
[6.325s] In file included from [01m[K/home/<USER>/code/pmac_robot/src/pmac_primitive_controller/include/pmac_primitive_controller/pmac_primitive_controller.hpp:23[m[K,
[6.326s]                  from [01m[K/home/<USER>/code/pmac_robot/src/pmac_primitive_controller/src/pmac_primitive_controller.cpp:15[m[K:
[6.326s] [01m[K/home/<USER>/code/pmac_robot/build/pmac_primitive_controller/include/pmac_primitive_controller_parameters.hpp:1:178:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: #include "pmac_primitive_controller_parameters.hpp" is deprecated. Use #include <pmac_primitive_controller/pmac_primitive_controller_parameters.hpp> instead.[m[K’
[6.326s]     1 | #pragma message("#include \"pmac_primitive_controller_parameters.hpp\" is deprecated. Use #include <pmac_primitive_controller/pmac_primitive_controller_parameters.hpp> instead."[01;36m[K)[m[K
[6.326s]       |                                                                                                                                                                                  [01;36m[K^[m[K
[6.497s] In file included from [01m[K/home/<USER>/code/pmac_robot/build/pmac_primitive_controller/include/pmac_primitive_controller_parameters.hpp:31[m[K,
[6.497s]                  from [01m[K/home/<USER>/code/pmac_robot/src/pmac_primitive_controller/include/pmac_primitive_controller/pmac_primitive_controller.hpp:23[m[K,
[6.497s]                  from [01m[K/home/<USER>/code/pmac_robot/src/pmac_primitive_controller/src/pmac_primitive_controller.cpp:15[m[K:
[6.497s] [01m[K/home/<USER>/code/pmac_robot/build/pmac_primitive_controller/include/validate_pmac_primitive_controller_parameters.hpp:25:75:[m[K [01;35m[Kwarning: [m[K‘[01m[Kusing Result = class tl::expected<void, std::__cxx11::basic_string<char> >[m[K’ is deprecated: Use tl::expected<void, std::string> for return instead. `#include <tl_expected/expected.hpp>`. [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wdeprecated-declarations-Wdeprecated-declarations]8;;[m[K]
[6.498s]    25 | Result forbidden_interface_name_prefix(rclcpp::Parameter const & parameter[01;35m[K)[m[K
[6.498s]       |                                                                           [01;35m[K^[m[K
[6.498s] In file included from [01m[K/home/<USER>/code/pmac_robot/build/pmac_primitive_controller/include/pmac_primitive_controller_parameters.hpp:25[m[K,
[6.498s]                  from [01m[K/home/<USER>/code/pmac_robot/src/pmac_primitive_controller/include/pmac_primitive_controller/pmac_primitive_controller.hpp:23[m[K,
[6.498s]                  from [01m[K/home/<USER>/code/pmac_robot/src/pmac_primitive_controller/src/pmac_primitive_controller.cpp:15[m[K:
[6.498s] [01m[K/opt/ros/humble/include/parameter_traits/parameter_traits/parameter_traits.hpp:37:7:[m[K [01;36m[Knote: [m[Kdeclared here
[6.498s]    37 | using [01;36m[KResult[m[K
[6.498s]       |       [01;36m[K^~~~~~[m[K
[6.521s] In file included from [01m[K/home/<USER>/code/pmac_robot/src/pmac_primitive_controller/include/pmac_primitive_controller/pmac_primitive_controller.hpp:27[m[K,
[6.522s]                  from [01m[K/home/<USER>/code/pmac_robot/src/pmac_primitive_controller/src/pmac_primitive_controller.cpp:15[m[K:
[6.522s] [01m[K/home/<USER>/code/ros2_control/install/include/realtime_tools/realtime_tools/realtime_buffer.h:22:100:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: This header include is deprecated. Please update your code to use 'realtime_buffer.hpp' header.[m[K’
[6.522s]    22 |   "This header include is deprecated. Please update your code to use 'realtime_buffer.hpp' header."[01;36m[K)[m[K  //NOLINT
[6.522s]       |                                                                                                    [01;36m[K^[m[K
[6.523s] In file included from [01m[K/home/<USER>/code/pmac_robot/src/pmac_primitive_controller/include/pmac_primitive_controller/pmac_primitive_controller.hpp:28[m[K,
[6.523s]                  from [01m[K/home/<USER>/code/pmac_robot/src/pmac_primitive_controller/src/pmac_primitive_controller.cpp:15[m[K:
[6.523s] [01m[K/home/<USER>/code/ros2_control/install/include/realtime_tools/realtime_tools/realtime_publisher.h:22:103:[m[K [01;36m[Knote: [m[K‘[01m[K#pragma message: This header include is deprecated. Please update your code to use 'realtime_publisher.hpp' header.[m[K’
[6.523s]    22 |   "This header include is deprecated. Please update your code to use 'realtime_publisher.hpp' header."[01;36m[K)[m[K  //NOLINT
[6.523s]       |                                                                                                       [01;36m[K^[m[K
[26.937s] [100%] [1m[32mLinking CXX shared library libpmac_primitive_controller.so[0m
[27.404s] [100%] Built target pmac_primitive_controller
[27.472s] Invoked command in '/home/<USER>/code/pmac_robot/build/pmac_primitive_controller' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake --build /home/<USER>/code/pmac_robot/build/pmac_primitive_controller -- -j8 -l8
[27.475s] Invoking command in '/home/<USER>/code/pmac_robot/build/pmac_primitive_controller': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake --install /home/<USER>/code/pmac_robot/build/pmac_primitive_controller
[27.478s] -- Install configuration: "RelWithDebInfo"
[27.479s] -- Execute custom install script
[27.479s] -- Symlinking: /home/<USER>/code/pmac_robot/install/include/pmac_primitive_controller/pmac_primitive_controller_parameters.hpp
[27.484s] -- Symlinking: /home/<USER>/code/pmac_robot/install/include/pmac_primitive_controller/validate_pmac_primitive_controller_parameters.hpp
[27.489s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller//pmac_primitive_controller.xml
[27.495s] -- Symlinking: /home/<USER>/code/pmac_robot/install/lib/libpmac_primitive_controller.so
[27.499s] -- Symlinking: /home/<USER>/code/pmac_robot/install/include/pmac_primitive_controller/pmac_primitive_controller/pmac_primitive_controller.hpp
[27.504s] -- Symlinking: /home/<USER>/code/pmac_robot/install/include/pmac_primitive_controller/pmac_primitive_controller/validate_pmac_primitive_controller_parameters.hpp
[27.510s] -- Symlinking: /home/<USER>/code/pmac_robot/install/include/pmac_primitive_controller/pmac_primitive_controller/visibility_control.h
[27.516s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/environment/library_path.sh
[27.520s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/environment/library_path.dsv
[27.526s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/package_run_dependencies/pmac_primitive_controller
[27.531s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/parent_prefix_path/pmac_primitive_controller
[27.536s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/environment/ament_prefix_path.sh
[27.541s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/environment/ament_prefix_path.dsv
[27.546s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/environment/path.sh
[27.551s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/environment/path.dsv
[27.557s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/local_setup.bash
[27.562s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/local_setup.sh
[27.567s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/local_setup.zsh
[27.572s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/local_setup.dsv
[27.577s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/package.dsv
[27.582s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/packages/pmac_primitive_controller
[27.587s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/controller_interface__pluginlib__plugin/pmac_primitive_controller
[27.592s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/cmake/ament_cmake_export_dependencies-extras.cmake
[27.597s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/cmake/ament_cmake_export_include_directories-extras.cmake
[27.602s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/cmake/ament_cmake_export_libraries-extras.cmake
[27.607s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/cmake/pmac_primitive_controllerConfig.cmake
[27.612s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/cmake/pmac_primitive_controllerConfig-version.cmake
[27.617s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/package.xml
[27.624s] Invoked command in '/home/<USER>/code/pmac_robot/build/pmac_primitive_controller' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake --install /home/<USER>/code/pmac_robot/build/pmac_primitive_controller
