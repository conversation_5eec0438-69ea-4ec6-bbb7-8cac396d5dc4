-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found generate_parameter_library: 0.5.0 (/opt/ros/humble/share/generate_parameter_library/cmake)
-- Found parameter_traits: 0.5.0 (/opt/ros/humble/share/parameter_traits/cmake)
-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")
-- Found FastRTPS: /opt/ros/humble/include
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- Found rclcpp_lifecycle: 16.0.14 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)
-- Found control_msgs: 4.8.0 (/home/<USER>/code/ros2_control/install/share/control_msgs/cmake)
-- Found controller_interface: 2.51.0 (/home/<USER>/code/ros2_control/install/share/controller_interface/cmake)
-- Found realtime_tools: 2.14.0 (/home/<USER>/code/ros2_control/install/share/realtime_tools/cmake)
-- Found std_srvs: 4.9.0 (/opt/ros/humble/share/std_srvs/cmake)
-- Configuring done (2.7s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/code/pmac_robot/build/pmac_primitive_controller
[ 25%] [1m[34mRunning `/opt/ros/humble/bin/generate_parameter_library_cpp /home/<USER>/code/pmac_robot/build/pmac_primitive_controller/include/pmac_primitive_controller/pmac_primitive_controller_parameters.hpp /home/<USER>/code/pmac_robot/src/pmac_primitive_controller/src/pmac_primitive_controller.yaml validate_pmac_primitive_controller_parameters.hpp`[0m
[ 50%] [1m[34mCreating deprecated header file /home/<USER>/code/pmac_robot/build/pmac_primitive_controller/include/pmac_primitive_controller_parameters.hpp[0m
[ 50%] Built target pmac_primitive_controller_parameters
[ 75%] [32mBuilding CXX object CMakeFiles/pmac_primitive_controller.dir/src/pmac_primitive_controller.cpp.o[0m
[100%] [1m[32mLinking CXX shared library libpmac_primitive_controller.so[0m
[100%] Built target pmac_primitive_controller
-- Install configuration: "RelWithDebInfo"
-- Execute custom install script
-- Symlinking: /home/<USER>/code/pmac_robot/install/include/pmac_primitive_controller/pmac_primitive_controller_parameters.hpp
-- Symlinking: /home/<USER>/code/pmac_robot/install/include/pmac_primitive_controller/validate_pmac_primitive_controller_parameters.hpp
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller//pmac_primitive_controller.xml
-- Symlinking: /home/<USER>/code/pmac_robot/install/lib/libpmac_primitive_controller.so
-- Symlinking: /home/<USER>/code/pmac_robot/install/include/pmac_primitive_controller/pmac_primitive_controller/pmac_primitive_controller.hpp
-- Symlinking: /home/<USER>/code/pmac_robot/install/include/pmac_primitive_controller/pmac_primitive_controller/validate_pmac_primitive_controller_parameters.hpp
-- Symlinking: /home/<USER>/code/pmac_robot/install/include/pmac_primitive_controller/pmac_primitive_controller/visibility_control.h
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/environment/library_path.sh
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/environment/library_path.dsv
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/package_run_dependencies/pmac_primitive_controller
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/parent_prefix_path/pmac_primitive_controller
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/environment/ament_prefix_path.sh
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/environment/ament_prefix_path.dsv
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/environment/path.sh
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/environment/path.dsv
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/local_setup.bash
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/local_setup.sh
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/local_setup.zsh
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/local_setup.dsv
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/package.dsv
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/packages/pmac_primitive_controller
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/controller_interface__pluginlib__plugin/pmac_primitive_controller
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/cmake/ament_cmake_export_dependencies-extras.cmake
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/cmake/ament_cmake_export_include_directories-extras.cmake
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/cmake/ament_cmake_export_libraries-extras.cmake
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/cmake/pmac_primitive_controllerConfig.cmake
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/cmake/pmac_primitive_controllerConfig-version.cmake
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/package.xml
