[0mCMake Deprecation Warning at CMakeLists.txt:1 (cmake_minimum_required):
  Compatibility with CMake < 3.10 will be removed from a future version of
  CMake.

  Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
  to tell CMake that the project requires at least <min> but has been updated
  to work with policies introduced by <max> or earlier.

[0m
-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Configuring done (0.8s)
[33mCMake Warning:
  Manually-specified variables were not used by the project:

    CMAKE_EXPORT_COMPILE_COMMANDS

[0m
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/code/pmac_robot/build/pmac_bringup
-- Install configuration: "RelWithDebInfo"
-- Execute custom install script
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/config/test_goal_publishers_config.yaml
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/config/tx2_60_controllers.yaml
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/test_forward_position_controller.launch.py
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/test_joint_trajectory_controller.launch.py
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/tx2_60.launch.py
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/package_run_dependencies/pmac_bringup
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/parent_prefix_path/pmac_bringup
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/environment/ament_prefix_path.sh
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/environment/ament_prefix_path.dsv
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/environment/path.sh
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/environment/path.dsv
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/local_setup.bash
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/local_setup.sh
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/local_setup.zsh
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/local_setup.dsv
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/package.dsv
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/packages/pmac_bringup
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/cmake/pmac_bringupConfig.cmake
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/cmake/pmac_bringupConfig-version.cmake
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/package.xml
