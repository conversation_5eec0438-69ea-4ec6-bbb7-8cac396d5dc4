[0.022s] Invoking command in '/home/<USER>/code/pmac_robot/build/pmac_bringup': AMENT_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description LD_LIBRARY_PATH=/home/<USER>/code/pmac_robot/install/lib:/home/<USER>/code/ros2_control/install/lib:/home/<USER>/code/ecat_ws/install/ethercat_manager/lib:/home/<USER>/code/ecat_ws/install/ethercat_msgs/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/lib:/home/<USER>/code/ecat_ws/install/ethercat_driver/lib:/home/<USER>/code/ecat_ws/install/ethercat_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake /home/<USER>/code/pmac_robot/src/pmac_bringup -DCMAKE_BUILD_TYPE=RelWithDebInfo -DBUILD_TESTING=0 -DCMAKE_EXPORT_COMPILE_COMMANDS=On -Wall -Wextra -Wpedantic -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/pmac_robot/install
[0.027s] [0mCMake Deprecation Warning at CMakeLists.txt:1 (cmake_minimum_required):
[0.027s]   Compatibility with CMake < 3.10 will be removed from a future version of
[0.027s]   CMake.
[0.027s] 
[0.028s]   Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
[0.028s]   to tell CMake that the project requires at least <min> but has been updated
[0.028s]   to work with policies introduced by <max> or earlier.
[0.028s] 
[0.028s] [0m
[0.086s] -- The C compiler identification is GNU 11.4.0
[0.169s] -- The CXX compiler identification is GNU 11.4.0
[0.179s] -- Detecting C compiler ABI info
[0.252s] -- Detecting C compiler ABI info - done
[0.270s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.270s] -- Detecting C compile features
[0.270s] -- Detecting C compile features - done
[0.285s] -- Detecting CXX compiler ABI info
[0.374s] -- Detecting CXX compiler ABI info - done
[0.396s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.396s] -- Detecting CXX compile features
[0.397s] -- Detecting CXX compile features - done
[0.411s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.602s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter
[0.682s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.870s] -- Configuring done (0.8s)
[0.875s] [33mCMake Warning:
[0.876s]   Manually-specified variables were not used by the project:
[0.876s] 
[0.876s]     CMAKE_EXPORT_COMPILE_COMMANDS
[0.876s] 
[0.876s] [0m
[0.876s] -- Generating done (0.0s)
[0.877s] -- Build files have been written to: /home/<USER>/code/pmac_robot/build/pmac_bringup
[0.884s] Invoked command in '/home/<USER>/code/pmac_robot/build/pmac_bringup' returned '0': AMENT_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description LD_LIBRARY_PATH=/home/<USER>/code/pmac_robot/install/lib:/home/<USER>/code/ros2_control/install/lib:/home/<USER>/code/ecat_ws/install/ethercat_manager/lib:/home/<USER>/code/ecat_ws/install/ethercat_msgs/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/lib:/home/<USER>/code/ecat_ws/install/ethercat_driver/lib:/home/<USER>/code/ecat_ws/install/ethercat_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake /home/<USER>/code/pmac_robot/src/pmac_bringup -DCMAKE_BUILD_TYPE=RelWithDebInfo -DBUILD_TESTING=0 -DCMAKE_EXPORT_COMPILE_COMMANDS=On -Wall -Wextra -Wpedantic -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/pmac_robot/install
[0.885s] Invoking command in '/home/<USER>/code/pmac_robot/build/pmac_bringup': AMENT_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description LD_LIBRARY_PATH=/home/<USER>/code/pmac_robot/install/lib:/home/<USER>/code/ros2_control/install/lib:/home/<USER>/code/ecat_ws/install/ethercat_manager/lib:/home/<USER>/code/ecat_ws/install/ethercat_msgs/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/lib:/home/<USER>/code/ecat_ws/install/ethercat_driver/lib:/home/<USER>/code/ecat_ws/install/ethercat_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake --build /home/<USER>/code/pmac_robot/build/pmac_bringup -- -j8 -l8
[0.910s] Invoked command in '/home/<USER>/code/pmac_robot/build/pmac_bringup' returned '0': AMENT_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description LD_LIBRARY_PATH=/home/<USER>/code/pmac_robot/install/lib:/home/<USER>/code/ros2_control/install/lib:/home/<USER>/code/ecat_ws/install/ethercat_manager/lib:/home/<USER>/code/ecat_ws/install/ethercat_msgs/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/lib:/home/<USER>/code/ecat_ws/install/ethercat_driver/lib:/home/<USER>/code/ecat_ws/install/ethercat_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake --build /home/<USER>/code/pmac_robot/build/pmac_bringup -- -j8 -l8
[0.913s] Invoking command in '/home/<USER>/code/pmac_robot/build/pmac_bringup': AMENT_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description LD_LIBRARY_PATH=/home/<USER>/code/pmac_robot/install/lib:/home/<USER>/code/ros2_control/install/lib:/home/<USER>/code/ecat_ws/install/ethercat_manager/lib:/home/<USER>/code/ecat_ws/install/ethercat_msgs/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/lib:/home/<USER>/code/ecat_ws/install/ethercat_driver/lib:/home/<USER>/code/ecat_ws/install/ethercat_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake --install /home/<USER>/code/pmac_robot/build/pmac_bringup
[0.917s] -- Install configuration: "RelWithDebInfo"
[0.917s] -- Execute custom install script
[0.917s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/config/test_goal_publishers_config.yaml
[0.922s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/config/tx2_60_controllers.yaml
[0.927s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/test_forward_position_controller.launch.py
[0.932s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/test_joint_trajectory_controller.launch.py
[0.937s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/tx2_60.launch.py
[0.942s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/package_run_dependencies/pmac_bringup
[0.947s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/parent_prefix_path/pmac_bringup
[0.952s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/environment/ament_prefix_path.sh
[0.957s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/environment/ament_prefix_path.dsv
[0.962s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/environment/path.sh
[0.967s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/environment/path.dsv
[0.972s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/local_setup.bash
[0.977s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/local_setup.sh
[0.982s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/local_setup.zsh
[0.987s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/local_setup.dsv
[0.992s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/package.dsv
[0.997s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/packages/pmac_bringup
[1.002s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/cmake/pmac_bringupConfig.cmake
[1.007s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/cmake/pmac_bringupConfig-version.cmake
[1.011s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/package.xml
[1.019s] Invoked command in '/home/<USER>/code/pmac_robot/build/pmac_bringup' returned '0': AMENT_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description LD_LIBRARY_PATH=/home/<USER>/code/pmac_robot/install/lib:/home/<USER>/code/ros2_control/install/lib:/home/<USER>/code/ecat_ws/install/ethercat_manager/lib:/home/<USER>/code/ecat_ws/install/ethercat_msgs/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/lib:/home/<USER>/code/ecat_ws/install/ethercat_driver/lib:/home/<USER>/code/ecat_ws/install/ethercat_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake --install /home/<USER>/code/pmac_robot/build/pmac_bringup
