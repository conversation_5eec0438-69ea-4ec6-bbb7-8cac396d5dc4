Invoking command in '/home/<USER>/code/pmac_robot/build/pmac_bringup': AMENT_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description LD_LIBRARY_PATH=/home/<USER>/code/pmac_robot/install/lib:/home/<USER>/code/ros2_control/install/lib:/home/<USER>/code/ecat_ws/install/ethercat_manager/lib:/home/<USER>/code/ecat_ws/install/ethercat_msgs/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/lib:/home/<USER>/code/ecat_ws/install/ethercat_driver/lib:/home/<USER>/code/ecat_ws/install/ethercat_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake /home/<USER>/code/pmac_robot/src/pmac_bringup -DCMAKE_BUILD_TYPE=RelWithDebInfo -DBUILD_TESTING=0 -DCMAKE_EXPORT_COMPILE_COMMANDS=On -Wall -Wextra -Wpedantic -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/pmac_robot/install
Invoked command in '/home/<USER>/code/pmac_robot/build/pmac_bringup' returned '0': AMENT_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description LD_LIBRARY_PATH=/home/<USER>/code/pmac_robot/install/lib:/home/<USER>/code/ros2_control/install/lib:/home/<USER>/code/ecat_ws/install/ethercat_manager/lib:/home/<USER>/code/ecat_ws/install/ethercat_msgs/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/lib:/home/<USER>/code/ecat_ws/install/ethercat_driver/lib:/home/<USER>/code/ecat_ws/install/ethercat_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake /home/<USER>/code/pmac_robot/src/pmac_bringup -DCMAKE_BUILD_TYPE=RelWithDebInfo -DBUILD_TESTING=0 -DCMAKE_EXPORT_COMPILE_COMMANDS=On -Wall -Wextra -Wpedantic -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/pmac_robot/install
Invoking command in '/home/<USER>/code/pmac_robot/build/pmac_bringup': AMENT_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description LD_LIBRARY_PATH=/home/<USER>/code/pmac_robot/install/lib:/home/<USER>/code/ros2_control/install/lib:/home/<USER>/code/ecat_ws/install/ethercat_manager/lib:/home/<USER>/code/ecat_ws/install/ethercat_msgs/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/lib:/home/<USER>/code/ecat_ws/install/ethercat_driver/lib:/home/<USER>/code/ecat_ws/install/ethercat_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake --build /home/<USER>/code/pmac_robot/build/pmac_bringup -- -j8 -l8
Invoked command in '/home/<USER>/code/pmac_robot/build/pmac_bringup' returned '0': AMENT_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description LD_LIBRARY_PATH=/home/<USER>/code/pmac_robot/install/lib:/home/<USER>/code/ros2_control/install/lib:/home/<USER>/code/ecat_ws/install/ethercat_manager/lib:/home/<USER>/code/ecat_ws/install/ethercat_msgs/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/lib:/home/<USER>/code/ecat_ws/install/ethercat_driver/lib:/home/<USER>/code/ecat_ws/install/ethercat_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake --build /home/<USER>/code/pmac_robot/build/pmac_bringup -- -j8 -l8
Invoking command in '/home/<USER>/code/pmac_robot/build/pmac_bringup': AMENT_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description LD_LIBRARY_PATH=/home/<USER>/code/pmac_robot/install/lib:/home/<USER>/code/ros2_control/install/lib:/home/<USER>/code/ecat_ws/install/ethercat_manager/lib:/home/<USER>/code/ecat_ws/install/ethercat_msgs/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/lib:/home/<USER>/code/ecat_ws/install/ethercat_driver/lib:/home/<USER>/code/ecat_ws/install/ethercat_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake --install /home/<USER>/code/pmac_robot/build/pmac_bringup
Invoked command in '/home/<USER>/code/pmac_robot/build/pmac_bringup' returned '0': AMENT_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description LD_LIBRARY_PATH=/home/<USER>/code/pmac_robot/install/lib:/home/<USER>/code/ros2_control/install/lib:/home/<USER>/code/ecat_ws/install/ethercat_manager/lib:/home/<USER>/code/ecat_ws/install/ethercat_msgs/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/lib:/home/<USER>/code/ecat_ws/install/ethercat_driver/lib:/home/<USER>/code/ecat_ws/install/ethercat_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake --install /home/<USER>/code/pmac_robot/build/pmac_bringup
