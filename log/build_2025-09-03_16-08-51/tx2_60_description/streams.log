[0.030s] Invoking command in '/home/<USER>/code/pmac_robot/build/tx2_60_description': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake /home/<USER>/code/pmac_robot/src/tx2_60_description -DCMAKE_BUILD_TYPE=RelWithDebInfo -DBUILD_TESTING=0 -DCMAKE_EXPORT_COMPILE_COMMANDS=On -Wall -Wextra -Wpedantic -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/pmac_robot/install
[0.039s] [0mCMake Deprecation Warning at CMakeLists.txt:1 (cmake_minimum_required):
[0.039s]   Compatibility with CMake < 3.10 will be removed from a future version of
[0.039s]   CMake.
[0.039s] 
[0.039s]   Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
[0.039s]   to tell CMake that the project requires at least <min> but has been updated
[0.039s]   to work with policies introduced by <max> or earlier.
[0.039s] 
[0.039s] [0m
[0.129s] -- The C compiler identification is GNU 11.4.0
[0.221s] -- The CXX compiler identification is GNU 11.4.0
[0.233s] -- Detecting C compiler ABI info
[0.320s] -- Detecting C compiler ABI info - done
[0.341s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.342s] -- Detecting C compile features
[0.342s] -- Detecting C compile features - done
[0.362s] -- Detecting CXX compiler ABI info
[0.460s] -- Detecting CXX compiler ABI info - done
[0.495s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.495s] -- Detecting CXX compile features
[0.496s] -- Detecting CXX compile features - done
[0.510s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.709s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter
[0.798s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[1.031s] -- Configuring done (1.0s)
[1.035s] -- Generating done (0.0s)
[1.035s] [33mCMake Warning:
[1.035s]   Manually-specified variables were not used by the project:
[1.035s] 
[1.035s]     CMAKE_EXPORT_COMPILE_COMMANDS
[1.035s] 
[1.035s] [0m
[1.036s] -- Build files have been written to: /home/<USER>/code/pmac_robot/build/tx2_60_description
[1.041s] Invoked command in '/home/<USER>/code/pmac_robot/build/tx2_60_description' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake /home/<USER>/code/pmac_robot/src/tx2_60_description -DCMAKE_BUILD_TYPE=RelWithDebInfo -DBUILD_TESTING=0 -DCMAKE_EXPORT_COMPILE_COMMANDS=On -Wall -Wextra -Wpedantic -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/pmac_robot/install
[1.043s] Invoking command in '/home/<USER>/code/pmac_robot/build/tx2_60_description': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake --build /home/<USER>/code/pmac_robot/build/tx2_60_description -- -j8 -l8
[1.068s] Invoked command in '/home/<USER>/code/pmac_robot/build/tx2_60_description' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake --build /home/<USER>/code/pmac_robot/build/tx2_60_description -- -j8 -l8
[1.076s] Invoking command in '/home/<USER>/code/pmac_robot/build/tx2_60_description': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake --install /home/<USER>/code/pmac_robot/build/tx2_60_description
[1.079s] -- Install configuration: "RelWithDebInfo"
[1.080s] -- Execute custom install script
[1.080s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/config/.gitkeep
[1.086s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/view_tx2_60.launch.py
[1.091s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/meshes/tx2_60/collision/.gitkeep
[1.096s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/meshes/tx2_60/visual/.gitkeep
[1.101s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/rviz/tx2_60.rviz
[1.106s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/urdf/common.xacro
[1.111s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/urdf/tx2_60.urdf.xacro
[1.116s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/urdf/tx2_60/tx2_60_macro.bak.xacro
[1.121s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/urdf/tx2_60/tx2_60_macro.ros2_control.xacro
[1.125s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/urdf/tx2_60/tx2_60_macro.xacro
[1.130s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/package_run_dependencies/tx2_60_description
[1.136s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/parent_prefix_path/tx2_60_description
[1.140s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/environment/ament_prefix_path.sh
[1.145s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/environment/ament_prefix_path.dsv
[1.150s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/environment/path.sh
[1.155s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/environment/path.dsv
[1.160s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/local_setup.bash
[1.165s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/local_setup.sh
[1.170s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/local_setup.zsh
[1.175s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/local_setup.dsv
[1.185s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/package.dsv
[1.191s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/packages/tx2_60_description
[1.196s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/cmake/tx2_60_descriptionConfig.cmake
[1.201s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/cmake/tx2_60_descriptionConfig-version.cmake
[1.205s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/package.xml
[1.212s] Invoked command in '/home/<USER>/code/pmac_robot/build/tx2_60_description' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake --install /home/<USER>/code/pmac_robot/build/tx2_60_description
