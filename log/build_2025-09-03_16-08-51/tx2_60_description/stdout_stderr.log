[0mCMake Deprecation Warning at CMakeLists.txt:1 (cmake_minimum_required):
  Compatibility with CMake < 3.10 will be removed from a future version of
  CMake.

  Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
  to tell CMake that the project requires at least <min> but has been updated
  to work with policies introduced by <max> or earlier.

[0m
-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Configuring done (1.0s)
-- Generating done (0.0s)
[33mCMake Warning:
  Manually-specified variables were not used by the project:

    CMAKE_EXPORT_COMPILE_COMMANDS

[0m
-- Build files have been written to: /home/<USER>/code/pmac_robot/build/tx2_60_description
-- Install configuration: "RelWithDebInfo"
-- Execute custom install script
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/config/.gitkeep
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/view_tx2_60.launch.py
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/meshes/tx2_60/collision/.gitkeep
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/meshes/tx2_60/visual/.gitkeep
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/rviz/tx2_60.rviz
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/urdf/common.xacro
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/urdf/tx2_60.urdf.xacro
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/urdf/tx2_60/tx2_60_macro.bak.xacro
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/urdf/tx2_60/tx2_60_macro.ros2_control.xacro
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/urdf/tx2_60/tx2_60_macro.xacro
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/package_run_dependencies/tx2_60_description
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/parent_prefix_path/tx2_60_description
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/environment/ament_prefix_path.sh
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/environment/ament_prefix_path.dsv
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/environment/path.sh
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/environment/path.dsv
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/local_setup.bash
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/local_setup.sh
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/local_setup.zsh
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/local_setup.dsv
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/package.dsv
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/packages/tx2_60_description
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/cmake/tx2_60_descriptionConfig.cmake
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/cmake/tx2_60_descriptionConfig-version.cmake
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/package.xml
