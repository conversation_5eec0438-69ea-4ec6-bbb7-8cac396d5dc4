-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler AB<PERSON> info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found hardware_interface: 2.51.0 (/home/<USER>/code/ros2_control/install/share/hardware_interface/cmake)
-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")
-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
-- Found FastRTPS: /opt/ros/humble/include
-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- Configuring done (2.2s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/code/pmac_robot/build/pmac_hardware_interface
[ 50%] [32mBuilding CXX object CMakeFiles/pmac_hardware_interface.dir/src/ck3m_primitive.cpp.o[0m
[100%] [1m[32mLinking CXX shared library libpmac_hardware_interface.so[0m
[100%] Built target pmac_hardware_interface
-- Install configuration: "RelWithDebInfo"
-- Execute custom install script
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface//pmac_hardware_interface.xml
-- Symlinking: /home/<USER>/code/pmac_robot/install/include/pmac_hardware_interface/ck3m_primitive.hpp
-- Symlinking: /home/<USER>/code/pmac_robot/install/include/pmac_hardware_interface/visibility_control.h
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/environment/library_path.sh
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/environment/library_path.dsv
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/package_run_dependencies/pmac_hardware_interface
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/parent_prefix_path/pmac_hardware_interface
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/environment/ament_prefix_path.sh
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/environment/ament_prefix_path.dsv
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/environment/path.sh
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/environment/path.dsv
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/local_setup.bash
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/local_setup.sh
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/local_setup.zsh
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/local_setup.dsv
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/package.dsv
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/packages/pmac_hardware_interface
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/hardware_interface__pluginlib__plugin/pmac_hardware_interface
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/cmake/ament_cmake_export_include_directories-extras.cmake
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/cmake/ament_cmake_export_libraries-extras.cmake
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/cmake/ament_cmake_export_dependencies-extras.cmake
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/cmake/pmac_hardware_interfaceConfig.cmake
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/cmake/pmac_hardware_interfaceConfig-version.cmake
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/package.xml
-- Installing: /home/<USER>/code/pmac_robot/install/lib/libpmac_hardware_interface.so
-- Set non-toolchain portion of runtime path of "/home/<USER>/code/pmac_robot/install/lib/libpmac_hardware_interface.so" to ""
