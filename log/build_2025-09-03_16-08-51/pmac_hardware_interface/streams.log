[0.040s] Invoking command in '/home/<USER>/code/pmac_robot/build/pmac_hardware_interface': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake /home/<USER>/code/pmac_robot/src/pmac_hardware_interface -DCMAKE_BUILD_TYPE=RelWithDebInfo -DBUILD_TESTING=0 -DCMAKE_EXPORT_COMPILE_COMMANDS=On -Wall -Wextra -Wpedantic -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/pmac_robot/install
[0.052s] [0mCMake Deprecation Warning at CMakeLists.txt:1 (cmake_minimum_required):
[0.053s]   Compatibility with CMake < 3.10 will be removed from a future version of
[0.053s]   CMake.
[0.053s] 
[0.054s]   Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
[0.054s]   to tell CMake that the project requires at least <min> but has been updated
[0.054s]   to work with policies introduced by <max> or earlier.
[0.055s] 
[0.057s] [0m
[0.149s] -- The C compiler identification is GNU 11.4.0
[0.271s] -- The CXX compiler identification is GNU 11.4.0
[0.283s] -- Detecting C compiler ABI info
[0.364s] -- Detecting C compiler ABI info - done
[0.394s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.394s] -- Detecting C compile features
[0.395s] -- Detecting C compile features - done
[0.415s] -- Detecting CXX compiler ABI info
[0.528s] -- Detecting CXX compiler ABI info - done
[0.559s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.560s] -- Detecting CXX compile features
[0.561s] -- Detecting CXX compile features - done
[0.575s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.777s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter
[0.868s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.957s] -- Found hardware_interface: 2.51.0 (/home/<USER>/code/ros2_control/install/share/hardware_interface/cmake)
[1.053s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[1.066s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[1.088s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[1.114s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[1.151s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[1.662s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[1.670s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[1.725s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[1.802s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")
[1.807s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[1.843s] -- Found FastRTPS: /opt/ros/humble/include
[1.851s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[1.862s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[1.887s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[1.920s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[2.036s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[2.038s] -- Found Threads: TRUE
[2.258s] -- Configuring done (2.2s)
[2.289s] -- Generating done (0.0s)
[2.292s] -- Build files have been written to: /home/<USER>/code/pmac_robot/build/pmac_hardware_interface
[2.309s] Invoked command in '/home/<USER>/code/pmac_robot/build/pmac_hardware_interface' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake /home/<USER>/code/pmac_robot/src/pmac_hardware_interface -DCMAKE_BUILD_TYPE=RelWithDebInfo -DBUILD_TESTING=0 -DCMAKE_EXPORT_COMPILE_COMMANDS=On -Wall -Wextra -Wpedantic -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/pmac_robot/install
[2.312s] Invoking command in '/home/<USER>/code/pmac_robot/build/pmac_hardware_interface': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake --build /home/<USER>/code/pmac_robot/build/pmac_hardware_interface -- -j8 -l8
[2.352s] [ 50%] [32mBuilding CXX object CMakeFiles/pmac_hardware_interface.dir/src/ck3m_primitive.cpp.o[0m
[10.774s] [100%] [1m[32mLinking CXX shared library libpmac_hardware_interface.so[0m
[10.932s] [100%] Built target pmac_hardware_interface
[10.999s] Invoked command in '/home/<USER>/code/pmac_robot/build/pmac_hardware_interface' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake --build /home/<USER>/code/pmac_robot/build/pmac_hardware_interface -- -j8 -l8
[11.002s] Invoking command in '/home/<USER>/code/pmac_robot/build/pmac_hardware_interface': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake --install /home/<USER>/code/pmac_robot/build/pmac_hardware_interface
[11.006s] -- Install configuration: "RelWithDebInfo"
[11.006s] -- Execute custom install script
[11.007s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface//pmac_hardware_interface.xml
[11.012s] -- Symlinking: /home/<USER>/code/pmac_robot/install/include/pmac_hardware_interface/ck3m_primitive.hpp
[11.017s] -- Symlinking: /home/<USER>/code/pmac_robot/install/include/pmac_hardware_interface/visibility_control.h
[11.022s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/environment/library_path.sh
[11.027s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/environment/library_path.dsv
[11.032s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/package_run_dependencies/pmac_hardware_interface
[11.037s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/parent_prefix_path/pmac_hardware_interface
[11.042s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/environment/ament_prefix_path.sh
[11.047s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/environment/ament_prefix_path.dsv
[11.052s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/environment/path.sh
[11.057s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/environment/path.dsv
[11.062s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/local_setup.bash
[11.067s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/local_setup.sh
[11.072s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/local_setup.zsh
[11.078s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/local_setup.dsv
[11.083s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/package.dsv
[11.088s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/packages/pmac_hardware_interface
[11.094s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/hardware_interface__pluginlib__plugin/pmac_hardware_interface
[11.099s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/cmake/ament_cmake_export_include_directories-extras.cmake
[11.104s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/cmake/ament_cmake_export_libraries-extras.cmake
[11.109s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/cmake/ament_cmake_export_dependencies-extras.cmake
[11.114s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/cmake/pmac_hardware_interfaceConfig.cmake
[11.119s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/cmake/pmac_hardware_interfaceConfig-version.cmake
[11.124s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/package.xml
[11.129s] -- Installing: /home/<USER>/code/pmac_robot/install/lib/libpmac_hardware_interface.so
[11.134s] -- Set non-toolchain portion of runtime path of "/home/<USER>/code/pmac_robot/install/lib/libpmac_hardware_interface.so" to ""
[11.136s] Invoked command in '/home/<USER>/code/pmac_robot/build/pmac_hardware_interface' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake --install /home/<USER>/code/pmac_robot/build/pmac_hardware_interface
