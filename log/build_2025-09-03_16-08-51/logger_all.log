[0.172s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--merge-install', '--symlink-install', '--cmake-args', '-DCMAKE_BUILD_TYPE=RelWithDebInfo', '-DBUILD_TESTING=0', '-DCMAKE_EXPORT_COMPILE_COMMANDS=On', '-Wall', '-Wextra', '-Wpedantic']
[0.173s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=True, symlink_install=True, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=8, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=['-DCMAKE_BUILD_TYPE=RelWithDebInfo', '-DBUILD_TESTING=0', '-DCMAKE_EXPORT_COMPILE_COMMANDS=On', '-Wall', '-Wextra', '-Wpedantic'], cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x7cb5a3035c30>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7cb5a3035690>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7cb5a3035690>>, mixin_verb=('build',))
[0.527s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.527s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.527s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.527s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.527s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.527s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.527s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/code/pmac_robot'
[0.527s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.527s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.527s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.527s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.527s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.528s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.528s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.528s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.528s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.542s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.542s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.542s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.542s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.542s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.542s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.543s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.544s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.544s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.544s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.544s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.544s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.544s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.544s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.544s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.544s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmac_bringup) by extensions ['ignore', 'ignore_ament_install']
[0.544s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmac_bringup) by extension 'ignore'
[0.544s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmac_bringup) by extension 'ignore_ament_install'
[0.544s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmac_bringup) by extensions ['colcon_pkg']
[0.544s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmac_bringup) by extension 'colcon_pkg'
[0.544s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmac_bringup) by extensions ['colcon_meta']
[0.544s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmac_bringup) by extension 'colcon_meta'
[0.544s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmac_bringup) by extensions ['ros']
[0.544s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmac_bringup) by extension 'ros'
[0.547s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmac_bringup' with type 'ros.ament_cmake' and name 'pmac_bringup'
[0.547s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmac_hardware_interface) by extensions ['ignore', 'ignore_ament_install']
[0.547s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmac_hardware_interface) by extension 'ignore'
[0.547s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmac_hardware_interface) by extension 'ignore_ament_install'
[0.547s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmac_hardware_interface) by extensions ['colcon_pkg']
[0.547s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmac_hardware_interface) by extension 'colcon_pkg'
[0.547s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmac_hardware_interface) by extensions ['colcon_meta']
[0.548s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmac_hardware_interface) by extension 'colcon_meta'
[0.548s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmac_hardware_interface) by extensions ['ros']
[0.548s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmac_hardware_interface) by extension 'ros'
[0.549s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmac_hardware_interface' with type 'ros.ament_cmake' and name 'pmac_hardware_interface'
[0.549s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmac_primitive_controller) by extensions ['ignore', 'ignore_ament_install']
[0.549s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmac_primitive_controller) by extension 'ignore'
[0.549s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmac_primitive_controller) by extension 'ignore_ament_install'
[0.549s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmac_primitive_controller) by extensions ['colcon_pkg']
[0.549s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmac_primitive_controller) by extension 'colcon_pkg'
[0.549s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmac_primitive_controller) by extensions ['colcon_meta']
[0.549s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmac_primitive_controller) by extension 'colcon_meta'
[0.549s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmac_primitive_controller) by extensions ['ros']
[0.549s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmac_primitive_controller) by extension 'ros'
[0.551s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmac_primitive_controller' with type 'ros.ament_cmake' and name 'pmac_primitive_controller'
[0.551s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_trajectory) by extensions ['ignore', 'ignore_ament_install']
[0.552s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_trajectory) by extension 'ignore'
[0.552s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_trajectory) by extension 'ignore_ament_install'
[0.552s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_trajectory) by extensions ['colcon_pkg']
[0.552s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_trajectory) by extension 'colcon_pkg'
[0.552s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_trajectory) by extensions ['colcon_meta']
[0.552s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_trajectory) by extension 'colcon_meta'
[0.552s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_trajectory) by extensions ['ros']
[0.552s] Level 1:colcon.colcon_core.package_identification:_identify(src/send_trajectory) by extension 'ros'
[0.553s] DEBUG:colcon.colcon_core.package_identification:Package 'src/send_trajectory' with type 'ros.ament_cmake' and name 'send_trajectory'
[0.553s] Level 1:colcon.colcon_core.package_identification:_identify(src/tx2_60_description) by extensions ['ignore', 'ignore_ament_install']
[0.553s] Level 1:colcon.colcon_core.package_identification:_identify(src/tx2_60_description) by extension 'ignore'
[0.553s] Level 1:colcon.colcon_core.package_identification:_identify(src/tx2_60_description) by extension 'ignore_ament_install'
[0.553s] Level 1:colcon.colcon_core.package_identification:_identify(src/tx2_60_description) by extensions ['colcon_pkg']
[0.553s] Level 1:colcon.colcon_core.package_identification:_identify(src/tx2_60_description) by extension 'colcon_pkg'
[0.553s] Level 1:colcon.colcon_core.package_identification:_identify(src/tx2_60_description) by extensions ['colcon_meta']
[0.553s] Level 1:colcon.colcon_core.package_identification:_identify(src/tx2_60_description) by extension 'colcon_meta'
[0.553s] Level 1:colcon.colcon_core.package_identification:_identify(src/tx2_60_description) by extensions ['ros']
[0.553s] Level 1:colcon.colcon_core.package_identification:_identify(src/tx2_60_description) by extension 'ros'
[0.554s] DEBUG:colcon.colcon_core.package_identification:Package 'src/tx2_60_description' with type 'ros.ament_cmake' and name 'tx2_60_description'
[0.554s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.554s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.554s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.554s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.554s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.573s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.573s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.575s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /home/<USER>/code/ws_moveit2/install
[0.577s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 62 installed packages in /home/<USER>/code/ros2_control/install
[0.578s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 11 installed packages in /home/<USER>/code/ecat_ws/install
[0.578s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /home/<USER>/code/ws_tools/install
[0.579s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 5 installed packages in /home/<USER>/code/staubli_experimental/install
[0.579s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /home/<USER>/code/Universal_Robots_ROS2_Driver/install
[0.581s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 369 installed packages in /opt/ros/humble
[0.584s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.646s] Level 5:colcon.colcon_core.verb:set package 'pmac_hardware_interface' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=RelWithDebInfo', '-DBUILD_TESTING=0', '-DCMAKE_EXPORT_COMPILE_COMMANDS=On', '-Wall', '-Wextra', '-Wpedantic']'
[0.646s] Level 5:colcon.colcon_core.verb:set package 'pmac_hardware_interface' build argument 'cmake_target' from command line to 'None'
[0.646s] Level 5:colcon.colcon_core.verb:set package 'pmac_hardware_interface' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.646s] Level 5:colcon.colcon_core.verb:set package 'pmac_hardware_interface' build argument 'cmake_clean_cache' from command line to 'False'
[0.646s] Level 5:colcon.colcon_core.verb:set package 'pmac_hardware_interface' build argument 'cmake_clean_first' from command line to 'False'
[0.646s] Level 5:colcon.colcon_core.verb:set package 'pmac_hardware_interface' build argument 'cmake_force_configure' from command line to 'False'
[0.646s] Level 5:colcon.colcon_core.verb:set package 'pmac_hardware_interface' build argument 'ament_cmake_args' from command line to 'None'
[0.646s] Level 5:colcon.colcon_core.verb:set package 'pmac_hardware_interface' build argument 'catkin_cmake_args' from command line to 'None'
[0.646s] Level 5:colcon.colcon_core.verb:set package 'pmac_hardware_interface' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.646s] DEBUG:colcon.colcon_core.verb:Building package 'pmac_hardware_interface' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/code/pmac_robot/build/pmac_hardware_interface', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=RelWithDebInfo', '-DBUILD_TESTING=0', '-DCMAKE_EXPORT_COMPILE_COMMANDS=On', '-Wall', '-Wextra', '-Wpedantic'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/code/pmac_robot/install', 'merge_install': True, 'path': '/home/<USER>/code/pmac_robot/src/pmac_hardware_interface', 'symlink_install': True, 'test_result_base': None}
[0.647s] Level 5:colcon.colcon_core.verb:set package 'pmac_primitive_controller' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=RelWithDebInfo', '-DBUILD_TESTING=0', '-DCMAKE_EXPORT_COMPILE_COMMANDS=On', '-Wall', '-Wextra', '-Wpedantic']'
[0.647s] Level 5:colcon.colcon_core.verb:set package 'pmac_primitive_controller' build argument 'cmake_target' from command line to 'None'
[0.647s] Level 5:colcon.colcon_core.verb:set package 'pmac_primitive_controller' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.647s] Level 5:colcon.colcon_core.verb:set package 'pmac_primitive_controller' build argument 'cmake_clean_cache' from command line to 'False'
[0.647s] Level 5:colcon.colcon_core.verb:set package 'pmac_primitive_controller' build argument 'cmake_clean_first' from command line to 'False'
[0.647s] Level 5:colcon.colcon_core.verb:set package 'pmac_primitive_controller' build argument 'cmake_force_configure' from command line to 'False'
[0.647s] Level 5:colcon.colcon_core.verb:set package 'pmac_primitive_controller' build argument 'ament_cmake_args' from command line to 'None'
[0.647s] Level 5:colcon.colcon_core.verb:set package 'pmac_primitive_controller' build argument 'catkin_cmake_args' from command line to 'None'
[0.647s] Level 5:colcon.colcon_core.verb:set package 'pmac_primitive_controller' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.647s] DEBUG:colcon.colcon_core.verb:Building package 'pmac_primitive_controller' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/code/pmac_robot/build/pmac_primitive_controller', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=RelWithDebInfo', '-DBUILD_TESTING=0', '-DCMAKE_EXPORT_COMPILE_COMMANDS=On', '-Wall', '-Wextra', '-Wpedantic'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/code/pmac_robot/install', 'merge_install': True, 'path': '/home/<USER>/code/pmac_robot/src/pmac_primitive_controller', 'symlink_install': True, 'test_result_base': None}
[0.647s] Level 5:colcon.colcon_core.verb:set package 'send_trajectory' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=RelWithDebInfo', '-DBUILD_TESTING=0', '-DCMAKE_EXPORT_COMPILE_COMMANDS=On', '-Wall', '-Wextra', '-Wpedantic']'
[0.648s] Level 5:colcon.colcon_core.verb:set package 'send_trajectory' build argument 'cmake_target' from command line to 'None'
[0.648s] Level 5:colcon.colcon_core.verb:set package 'send_trajectory' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.648s] Level 5:colcon.colcon_core.verb:set package 'send_trajectory' build argument 'cmake_clean_cache' from command line to 'False'
[0.648s] Level 5:colcon.colcon_core.verb:set package 'send_trajectory' build argument 'cmake_clean_first' from command line to 'False'
[0.648s] Level 5:colcon.colcon_core.verb:set package 'send_trajectory' build argument 'cmake_force_configure' from command line to 'False'
[0.648s] Level 5:colcon.colcon_core.verb:set package 'send_trajectory' build argument 'ament_cmake_args' from command line to 'None'
[0.648s] Level 5:colcon.colcon_core.verb:set package 'send_trajectory' build argument 'catkin_cmake_args' from command line to 'None'
[0.648s] Level 5:colcon.colcon_core.verb:set package 'send_trajectory' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.648s] DEBUG:colcon.colcon_core.verb:Building package 'send_trajectory' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/code/pmac_robot/build/send_trajectory', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=RelWithDebInfo', '-DBUILD_TESTING=0', '-DCMAKE_EXPORT_COMPILE_COMMANDS=On', '-Wall', '-Wextra', '-Wpedantic'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/code/pmac_robot/install', 'merge_install': True, 'path': '/home/<USER>/code/pmac_robot/src/send_trajectory', 'symlink_install': True, 'test_result_base': None}
[0.648s] Level 5:colcon.colcon_core.verb:set package 'tx2_60_description' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=RelWithDebInfo', '-DBUILD_TESTING=0', '-DCMAKE_EXPORT_COMPILE_COMMANDS=On', '-Wall', '-Wextra', '-Wpedantic']'
[0.648s] Level 5:colcon.colcon_core.verb:set package 'tx2_60_description' build argument 'cmake_target' from command line to 'None'
[0.648s] Level 5:colcon.colcon_core.verb:set package 'tx2_60_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.648s] Level 5:colcon.colcon_core.verb:set package 'tx2_60_description' build argument 'cmake_clean_cache' from command line to 'False'
[0.648s] Level 5:colcon.colcon_core.verb:set package 'tx2_60_description' build argument 'cmake_clean_first' from command line to 'False'
[0.648s] Level 5:colcon.colcon_core.verb:set package 'tx2_60_description' build argument 'cmake_force_configure' from command line to 'False'
[0.648s] Level 5:colcon.colcon_core.verb:set package 'tx2_60_description' build argument 'ament_cmake_args' from command line to 'None'
[0.648s] Level 5:colcon.colcon_core.verb:set package 'tx2_60_description' build argument 'catkin_cmake_args' from command line to 'None'
[0.648s] Level 5:colcon.colcon_core.verb:set package 'tx2_60_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.648s] DEBUG:colcon.colcon_core.verb:Building package 'tx2_60_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/code/pmac_robot/build/tx2_60_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=RelWithDebInfo', '-DBUILD_TESTING=0', '-DCMAKE_EXPORT_COMPILE_COMMANDS=On', '-Wall', '-Wextra', '-Wpedantic'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/code/pmac_robot/install', 'merge_install': True, 'path': '/home/<USER>/code/pmac_robot/src/tx2_60_description', 'symlink_install': True, 'test_result_base': None}
[0.649s] Level 5:colcon.colcon_core.verb:set package 'pmac_bringup' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=RelWithDebInfo', '-DBUILD_TESTING=0', '-DCMAKE_EXPORT_COMPILE_COMMANDS=On', '-Wall', '-Wextra', '-Wpedantic']'
[0.649s] Level 5:colcon.colcon_core.verb:set package 'pmac_bringup' build argument 'cmake_target' from command line to 'None'
[0.649s] Level 5:colcon.colcon_core.verb:set package 'pmac_bringup' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.649s] Level 5:colcon.colcon_core.verb:set package 'pmac_bringup' build argument 'cmake_clean_cache' from command line to 'False'
[0.649s] Level 5:colcon.colcon_core.verb:set package 'pmac_bringup' build argument 'cmake_clean_first' from command line to 'False'
[0.649s] Level 5:colcon.colcon_core.verb:set package 'pmac_bringup' build argument 'cmake_force_configure' from command line to 'False'
[0.649s] Level 5:colcon.colcon_core.verb:set package 'pmac_bringup' build argument 'ament_cmake_args' from command line to 'None'
[0.649s] Level 5:colcon.colcon_core.verb:set package 'pmac_bringup' build argument 'catkin_cmake_args' from command line to 'None'
[0.649s] Level 5:colcon.colcon_core.verb:set package 'pmac_bringup' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.649s] DEBUG:colcon.colcon_core.verb:Building package 'pmac_bringup' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/code/pmac_robot/build/pmac_bringup', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=RelWithDebInfo', '-DBUILD_TESTING=0', '-DCMAKE_EXPORT_COMPILE_COMMANDS=On', '-Wall', '-Wextra', '-Wpedantic'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/code/pmac_robot/install', 'merge_install': True, 'path': '/home/<USER>/code/pmac_robot/src/pmac_bringup', 'symlink_install': True, 'test_result_base': None}
[0.649s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.650s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.650s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/code/pmac_robot/src/pmac_hardware_interface' with build type 'ament_cmake'
[0.651s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/code/pmac_robot/src/pmac_hardware_interface'
[0.653s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.653s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.653s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.662s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/code/pmac_robot/src/pmac_primitive_controller' with build type 'ament_cmake'
[0.662s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/code/pmac_robot/src/pmac_primitive_controller'
[0.662s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.662s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.670s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/code/pmac_robot/src/tx2_60_description' with build type 'ament_cmake'
[0.670s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/code/pmac_robot/src/tx2_60_description'
[0.670s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.670s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.679s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/code/pmac_robot/src/send_trajectory' with build type 'ament_cmake'
[0.680s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/code/pmac_robot/src/send_trajectory'
[0.680s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.680s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.692s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/pmac_robot/build/pmac_hardware_interface': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake /home/<USER>/code/pmac_robot/src/pmac_hardware_interface -DCMAKE_BUILD_TYPE=RelWithDebInfo -DBUILD_TESTING=0 -DCMAKE_EXPORT_COMPILE_COMMANDS=On -Wall -Wextra -Wpedantic -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/pmac_robot/install
[0.697s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/pmac_robot/build/pmac_primitive_controller': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake /home/<USER>/code/pmac_robot/src/pmac_primitive_controller -DCMAKE_BUILD_TYPE=RelWithDebInfo -DBUILD_TESTING=0 -DCMAKE_EXPORT_COMPILE_COMMANDS=On -Wall -Wextra -Wpedantic -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/pmac_robot/install
[0.701s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/pmac_robot/build/tx2_60_description': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake /home/<USER>/code/pmac_robot/src/tx2_60_description -DCMAKE_BUILD_TYPE=RelWithDebInfo -DBUILD_TESTING=0 -DCMAKE_EXPORT_COMPILE_COMMANDS=On -Wall -Wextra -Wpedantic -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/pmac_robot/install
[0.711s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/pmac_robot/build/send_trajectory': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake /home/<USER>/code/pmac_robot/src/send_trajectory -DCMAKE_BUILD_TYPE=RelWithDebInfo -DBUILD_TESTING=0 -DCMAKE_EXPORT_COMPILE_COMMANDS=On -Wall -Wextra -Wpedantic -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/pmac_robot/install
[1.712s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/pmac_robot/build/tx2_60_description' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake /home/<USER>/code/pmac_robot/src/tx2_60_description -DCMAKE_BUILD_TYPE=RelWithDebInfo -DBUILD_TESTING=0 -DCMAKE_EXPORT_COMPILE_COMMANDS=On -Wall -Wextra -Wpedantic -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/pmac_robot/install
[1.714s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/pmac_robot/build/tx2_60_description': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake --build /home/<USER>/code/pmac_robot/build/tx2_60_description -- -j8 -l8
[1.739s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/pmac_robot/build/tx2_60_description' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake --build /home/<USER>/code/pmac_robot/build/tx2_60_description -- -j8 -l8
[1.747s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/pmac_robot/build/tx2_60_description': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake --install /home/<USER>/code/pmac_robot/build/tx2_60_description
[1.882s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(tx2_60_description)
[1.883s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/pmac_robot/build/tx2_60_description' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake --install /home/<USER>/code/pmac_robot/build/tx2_60_description
[1.886s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install' for CMake module files
[1.886s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install' for CMake config files
[1.887s] Level 1:colcon.colcon_core.shell:create_environment_hook('tx2_60_description', 'cmake_prefix_path')
[1.887s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/tx2_60_description/hook/cmake_prefix_path.ps1'
[1.888s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/pmac_robot/install/share/tx2_60_description/hook/cmake_prefix_path.dsv'
[1.888s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/tx2_60_description/hook/cmake_prefix_path.sh'
[1.889s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/bin'
[1.889s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/lib/pkgconfig/tx2_60_description.pc'
[1.890s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/lib/python3.10/site-packages'
[1.890s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/bin'
[1.890s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/tx2_60_description/package.ps1'
[1.891s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/pmac_robot/install/share/tx2_60_description/package.dsv'
[1.892s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/tx2_60_description/package.sh'
[1.892s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/tx2_60_description/package.bash'
[1.893s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/tx2_60_description/package.zsh'
[1.894s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/pmac_robot/install/share/colcon-core/packages/tx2_60_description)
[1.894s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(tx2_60_description)
[1.894s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install' for CMake module files
[1.895s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install' for CMake config files
[1.895s] Level 1:colcon.colcon_core.shell:create_environment_hook('tx2_60_description', 'cmake_prefix_path')
[1.895s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/tx2_60_description/hook/cmake_prefix_path.ps1'
[1.896s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/pmac_robot/install/share/tx2_60_description/hook/cmake_prefix_path.dsv'
[1.896s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/tx2_60_description/hook/cmake_prefix_path.sh'
[1.897s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/bin'
[1.897s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/lib/pkgconfig/tx2_60_description.pc'
[1.897s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/lib/python3.10/site-packages'
[1.897s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/bin'
[1.898s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/tx2_60_description/package.ps1'
[1.898s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/pmac_robot/install/share/tx2_60_description/package.dsv'
[1.899s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/tx2_60_description/package.sh'
[1.899s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/tx2_60_description/package.bash'
[1.900s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/tx2_60_description/package.zsh'
[1.900s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/pmac_robot/install/share/colcon-core/packages/tx2_60_description)
[2.961s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/pmac_robot/build/pmac_hardware_interface' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake /home/<USER>/code/pmac_robot/src/pmac_hardware_interface -DCMAKE_BUILD_TYPE=RelWithDebInfo -DBUILD_TESTING=0 -DCMAKE_EXPORT_COMPILE_COMMANDS=On -Wall -Wextra -Wpedantic -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/pmac_robot/install
[2.964s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/pmac_robot/build/pmac_hardware_interface': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake --build /home/<USER>/code/pmac_robot/build/pmac_hardware_interface -- -j8 -l8
[3.234s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/pmac_robot/build/send_trajectory' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake /home/<USER>/code/pmac_robot/src/send_trajectory -DCMAKE_BUILD_TYPE=RelWithDebInfo -DBUILD_TESTING=0 -DCMAKE_EXPORT_COMPILE_COMMANDS=On -Wall -Wextra -Wpedantic -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/pmac_robot/install
[3.238s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/pmac_robot/build/send_trajectory': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake --build /home/<USER>/code/pmac_robot/build/send_trajectory -- -j8 -l8
[3.409s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/pmac_robot/build/pmac_primitive_controller' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake /home/<USER>/code/pmac_robot/src/pmac_primitive_controller -DCMAKE_BUILD_TYPE=RelWithDebInfo -DBUILD_TESTING=0 -DCMAKE_EXPORT_COMPILE_COMMANDS=On -Wall -Wextra -Wpedantic -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/pmac_robot/install
[3.412s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/pmac_robot/build/pmac_primitive_controller': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake --build /home/<USER>/code/pmac_robot/build/pmac_primitive_controller -- -j8 -l8
[11.651s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/pmac_robot/build/pmac_hardware_interface' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake --build /home/<USER>/code/pmac_robot/build/pmac_hardware_interface -- -j8 -l8
[11.653s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/pmac_robot/build/pmac_hardware_interface': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake --install /home/<USER>/code/pmac_robot/build/pmac_hardware_interface
[11.786s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(pmac_hardware_interface)
[11.787s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install' for CMake module files
[11.788s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/pmac_robot/build/pmac_hardware_interface' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake --install /home/<USER>/code/pmac_robot/build/pmac_hardware_interface
[11.788s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install' for CMake config files
[11.789s] Level 1:colcon.colcon_core.shell:create_environment_hook('pmac_hardware_interface', 'cmake_prefix_path')
[11.789s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/hook/cmake_prefix_path.ps1'
[11.790s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/hook/cmake_prefix_path.dsv'
[11.790s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/hook/cmake_prefix_path.sh'
[11.791s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/lib'
[11.791s] Level 1:colcon.colcon_core.shell:create_environment_hook('pmac_hardware_interface', 'ld_library_path_lib')
[11.791s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/hook/ld_library_path_lib.ps1'
[11.792s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/hook/ld_library_path_lib.dsv'
[11.792s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/hook/ld_library_path_lib.sh'
[11.792s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/bin'
[11.793s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/lib/pkgconfig/pmac_hardware_interface.pc'
[11.793s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/lib/python3.10/site-packages'
[11.793s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/bin'
[11.793s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/package.ps1'
[11.794s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/package.dsv'
[11.794s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/package.sh'
[11.795s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/package.bash'
[11.795s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/package.zsh'
[11.795s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/pmac_robot/install/share/colcon-core/packages/pmac_hardware_interface)
[11.796s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(pmac_hardware_interface)
[11.796s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install' for CMake module files
[11.797s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install' for CMake config files
[11.797s] Level 1:colcon.colcon_core.shell:create_environment_hook('pmac_hardware_interface', 'cmake_prefix_path')
[11.798s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/hook/cmake_prefix_path.ps1'
[11.798s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/hook/cmake_prefix_path.dsv'
[11.798s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/hook/cmake_prefix_path.sh'
[11.799s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/lib'
[11.799s] Level 1:colcon.colcon_core.shell:create_environment_hook('pmac_hardware_interface', 'ld_library_path_lib')
[11.800s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/hook/ld_library_path_lib.ps1'
[11.800s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/hook/ld_library_path_lib.dsv'
[11.800s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/hook/ld_library_path_lib.sh'
[11.801s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/bin'
[11.801s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/lib/pkgconfig/pmac_hardware_interface.pc'
[11.801s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/lib/python3.10/site-packages'
[11.801s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/bin'
[11.802s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/package.ps1'
[11.802s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/package.dsv'
[11.803s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/package.sh'
[11.804s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/package.bash'
[11.804s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/package.zsh'
[11.804s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/pmac_robot/install/share/colcon-core/packages/pmac_hardware_interface)
[15.622s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/pmac_robot/build/send_trajectory' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake --build /home/<USER>/code/pmac_robot/build/send_trajectory -- -j8 -l8
[15.624s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/pmac_robot/build/send_trajectory': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake --install /home/<USER>/code/pmac_robot/build/send_trajectory
[15.721s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(send_trajectory)
[15.722s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install' for CMake module files
[15.723s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/pmac_robot/build/send_trajectory' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake --install /home/<USER>/code/pmac_robot/build/send_trajectory
[15.723s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install' for CMake config files
[15.723s] Level 1:colcon.colcon_core.shell:create_environment_hook('send_trajectory', 'cmake_prefix_path')
[15.723s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/send_trajectory/hook/cmake_prefix_path.ps1'
[15.724s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/pmac_robot/install/share/send_trajectory/hook/cmake_prefix_path.dsv'
[15.725s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/send_trajectory/hook/cmake_prefix_path.sh'
[15.725s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/lib'
[15.725s] Level 1:colcon.colcon_core.shell:create_environment_hook('send_trajectory', 'ld_library_path_lib')
[15.726s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/send_trajectory/hook/ld_library_path_lib.ps1'
[15.726s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/pmac_robot/install/share/send_trajectory/hook/ld_library_path_lib.dsv'
[15.727s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/send_trajectory/hook/ld_library_path_lib.sh'
[15.727s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/bin'
[15.727s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/lib/pkgconfig/send_trajectory.pc'
[15.728s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/lib/python3.10/site-packages'
[15.728s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/bin'
[15.728s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/send_trajectory/package.ps1'
[15.729s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/pmac_robot/install/share/send_trajectory/package.dsv'
[15.730s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/send_trajectory/package.sh'
[15.731s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/send_trajectory/package.bash'
[15.731s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/send_trajectory/package.zsh'
[15.732s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/pmac_robot/install/share/colcon-core/packages/send_trajectory)
[15.733s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(send_trajectory)
[15.733s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install' for CMake module files
[15.733s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install' for CMake config files
[15.733s] Level 1:colcon.colcon_core.shell:create_environment_hook('send_trajectory', 'cmake_prefix_path')
[15.734s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/send_trajectory/hook/cmake_prefix_path.ps1'
[15.734s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/pmac_robot/install/share/send_trajectory/hook/cmake_prefix_path.dsv'
[15.735s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/send_trajectory/hook/cmake_prefix_path.sh'
[15.736s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/lib'
[15.736s] Level 1:colcon.colcon_core.shell:create_environment_hook('send_trajectory', 'ld_library_path_lib')
[15.736s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/send_trajectory/hook/ld_library_path_lib.ps1'
[15.736s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/pmac_robot/install/share/send_trajectory/hook/ld_library_path_lib.dsv'
[15.737s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/send_trajectory/hook/ld_library_path_lib.sh'
[15.739s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/bin'
[15.739s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/lib/pkgconfig/send_trajectory.pc'
[15.739s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/lib/python3.10/site-packages'
[15.739s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/bin'
[15.740s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/send_trajectory/package.ps1'
[15.741s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/pmac_robot/install/share/send_trajectory/package.dsv'
[15.741s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/send_trajectory/package.sh'
[15.742s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/send_trajectory/package.bash'
[15.743s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/send_trajectory/package.zsh'
[15.743s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/pmac_robot/install/share/colcon-core/packages/send_trajectory)
[28.134s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/pmac_robot/build/pmac_primitive_controller' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake --build /home/<USER>/code/pmac_robot/build/pmac_primitive_controller -- -j8 -l8
[28.136s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/pmac_robot/build/pmac_primitive_controller': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake --install /home/<USER>/code/pmac_robot/build/pmac_primitive_controller
[28.285s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(pmac_primitive_controller)
[28.285s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install' for CMake module files
[28.286s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/pmac_robot/build/pmac_primitive_controller' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake --install /home/<USER>/code/pmac_robot/build/pmac_primitive_controller
[28.287s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install' for CMake config files
[28.287s] Level 1:colcon.colcon_core.shell:create_environment_hook('pmac_primitive_controller', 'cmake_prefix_path')
[28.287s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/hook/cmake_prefix_path.ps1'
[28.288s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/hook/cmake_prefix_path.dsv'
[28.288s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/hook/cmake_prefix_path.sh'
[28.289s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/lib'
[28.289s] Level 1:colcon.colcon_core.shell:create_environment_hook('pmac_primitive_controller', 'ld_library_path_lib')
[28.289s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/hook/ld_library_path_lib.ps1'
[28.289s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/hook/ld_library_path_lib.dsv'
[28.290s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/hook/ld_library_path_lib.sh'
[28.290s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/bin'
[28.290s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/lib/pkgconfig/pmac_primitive_controller.pc'
[28.290s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/lib/python3.10/site-packages'
[28.291s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/bin'
[28.291s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/package.ps1'
[28.292s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/package.dsv'
[28.292s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/package.sh'
[28.292s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/package.bash'
[28.293s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/package.zsh'
[28.293s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/pmac_robot/install/share/colcon-core/packages/pmac_primitive_controller)
[28.294s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(pmac_primitive_controller)
[28.294s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install' for CMake module files
[28.294s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install' for CMake config files
[28.295s] Level 1:colcon.colcon_core.shell:create_environment_hook('pmac_primitive_controller', 'cmake_prefix_path')
[28.295s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/hook/cmake_prefix_path.ps1'
[28.295s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/hook/cmake_prefix_path.dsv'
[28.296s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/hook/cmake_prefix_path.sh'
[28.296s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/lib'
[28.296s] Level 1:colcon.colcon_core.shell:create_environment_hook('pmac_primitive_controller', 'ld_library_path_lib')
[28.296s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/hook/ld_library_path_lib.ps1'
[28.297s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/hook/ld_library_path_lib.dsv'
[28.297s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/hook/ld_library_path_lib.sh'
[28.298s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/bin'
[28.298s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/lib/pkgconfig/pmac_primitive_controller.pc'
[28.298s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/lib/python3.10/site-packages'
[28.298s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/bin'
[28.299s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/package.ps1'
[28.299s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/package.dsv'
[28.300s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/package.sh'
[28.300s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/package.bash'
[28.301s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/package.zsh'
[28.301s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/pmac_robot/install/share/colcon-core/packages/pmac_primitive_controller)
[28.302s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/code/pmac_robot/src/pmac_bringup' with build type 'ament_cmake'
[28.302s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/code/pmac_robot/src/pmac_bringup'
[28.303s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[28.303s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[28.326s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/pmac_robot/build/pmac_bringup': AMENT_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description LD_LIBRARY_PATH=/home/<USER>/code/pmac_robot/install/lib:/home/<USER>/code/ros2_control/install/lib:/home/<USER>/code/ecat_ws/install/ethercat_manager/lib:/home/<USER>/code/ecat_ws/install/ethercat_msgs/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/lib:/home/<USER>/code/ecat_ws/install/ethercat_driver/lib:/home/<USER>/code/ecat_ws/install/ethercat_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake /home/<USER>/code/pmac_robot/src/pmac_bringup -DCMAKE_BUILD_TYPE=RelWithDebInfo -DBUILD_TESTING=0 -DCMAKE_EXPORT_COMPILE_COMMANDS=On -Wall -Wextra -Wpedantic -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/pmac_robot/install
[29.188s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/pmac_robot/build/pmac_bringup' returned '0': AMENT_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description LD_LIBRARY_PATH=/home/<USER>/code/pmac_robot/install/lib:/home/<USER>/code/ros2_control/install/lib:/home/<USER>/code/ecat_ws/install/ethercat_manager/lib:/home/<USER>/code/ecat_ws/install/ethercat_msgs/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/lib:/home/<USER>/code/ecat_ws/install/ethercat_driver/lib:/home/<USER>/code/ecat_ws/install/ethercat_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake /home/<USER>/code/pmac_robot/src/pmac_bringup -DCMAKE_BUILD_TYPE=RelWithDebInfo -DBUILD_TESTING=0 -DCMAKE_EXPORT_COMPILE_COMMANDS=On -Wall -Wextra -Wpedantic -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/pmac_robot/install
[29.189s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/pmac_robot/build/pmac_bringup': AMENT_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description LD_LIBRARY_PATH=/home/<USER>/code/pmac_robot/install/lib:/home/<USER>/code/ros2_control/install/lib:/home/<USER>/code/ecat_ws/install/ethercat_manager/lib:/home/<USER>/code/ecat_ws/install/ethercat_msgs/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/lib:/home/<USER>/code/ecat_ws/install/ethercat_driver/lib:/home/<USER>/code/ecat_ws/install/ethercat_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake --build /home/<USER>/code/pmac_robot/build/pmac_bringup -- -j8 -l8
[29.214s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/pmac_robot/build/pmac_bringup' returned '0': AMENT_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description LD_LIBRARY_PATH=/home/<USER>/code/pmac_robot/install/lib:/home/<USER>/code/ros2_control/install/lib:/home/<USER>/code/ecat_ws/install/ethercat_manager/lib:/home/<USER>/code/ecat_ws/install/ethercat_msgs/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/lib:/home/<USER>/code/ecat_ws/install/ethercat_driver/lib:/home/<USER>/code/ecat_ws/install/ethercat_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake --build /home/<USER>/code/pmac_robot/build/pmac_bringup -- -j8 -l8
[29.217s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/pmac_robot/build/pmac_bringup': AMENT_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description LD_LIBRARY_PATH=/home/<USER>/code/pmac_robot/install/lib:/home/<USER>/code/ros2_control/install/lib:/home/<USER>/code/ecat_ws/install/ethercat_manager/lib:/home/<USER>/code/ecat_ws/install/ethercat_msgs/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/lib:/home/<USER>/code/ecat_ws/install/ethercat_driver/lib:/home/<USER>/code/ecat_ws/install/ethercat_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake --install /home/<USER>/code/pmac_robot/build/pmac_bringup
[29.322s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(pmac_bringup)
[29.322s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install' for CMake module files
[29.323s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/pmac_robot/build/pmac_bringup' returned '0': AMENT_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/code/pmac_robot/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description LD_LIBRARY_PATH=/home/<USER>/code/pmac_robot/install/lib:/home/<USER>/code/ros2_control/install/lib:/home/<USER>/code/ecat_ws/install/ethercat_manager/lib:/home/<USER>/code/ecat_ws/install/ethercat_msgs/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/lib:/home/<USER>/code/ecat_ws/install/ethercat_driver/lib:/home/<USER>/code/ecat_ws/install/ethercat_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins /usr/bin/cmake --install /home/<USER>/code/pmac_robot/build/pmac_bringup
[29.323s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install' for CMake config files
[29.324s] Level 1:colcon.colcon_core.shell:create_environment_hook('pmac_bringup', 'cmake_prefix_path')
[29.324s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/pmac_bringup/hook/cmake_prefix_path.ps1'
[29.325s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/pmac_robot/install/share/pmac_bringup/hook/cmake_prefix_path.dsv'
[29.325s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/pmac_bringup/hook/cmake_prefix_path.sh'
[29.326s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/lib'
[29.326s] Level 1:colcon.colcon_core.shell:create_environment_hook('pmac_bringup', 'ld_library_path_lib')
[29.326s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/pmac_bringup/hook/ld_library_path_lib.ps1'
[29.326s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/pmac_robot/install/share/pmac_bringup/hook/ld_library_path_lib.dsv'
[29.327s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/pmac_bringup/hook/ld_library_path_lib.sh'
[29.327s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/bin'
[29.327s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/lib/pkgconfig/pmac_bringup.pc'
[29.328s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/lib/python3.10/site-packages'
[29.328s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/bin'
[29.328s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/pmac_bringup/package.ps1'
[29.329s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/pmac_robot/install/share/pmac_bringup/package.dsv'
[29.329s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/pmac_bringup/package.sh'
[29.330s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/pmac_bringup/package.bash'
[29.330s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/pmac_bringup/package.zsh'
[29.331s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/pmac_robot/install/share/colcon-core/packages/pmac_bringup)
[29.331s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(pmac_bringup)
[29.331s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install' for CMake module files
[29.332s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install' for CMake config files
[29.332s] Level 1:colcon.colcon_core.shell:create_environment_hook('pmac_bringup', 'cmake_prefix_path')
[29.332s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/pmac_bringup/hook/cmake_prefix_path.ps1'
[29.333s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/pmac_robot/install/share/pmac_bringup/hook/cmake_prefix_path.dsv'
[29.334s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/pmac_bringup/hook/cmake_prefix_path.sh'
[29.334s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/lib'
[29.334s] Level 1:colcon.colcon_core.shell:create_environment_hook('pmac_bringup', 'ld_library_path_lib')
[29.335s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/pmac_bringup/hook/ld_library_path_lib.ps1'
[29.335s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/pmac_robot/install/share/pmac_bringup/hook/ld_library_path_lib.dsv'
[29.336s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/pmac_robot/install/share/pmac_bringup/hook/ld_library_path_lib.sh'
[29.337s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/bin'
[29.337s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/lib/pkgconfig/pmac_bringup.pc'
[29.337s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/lib/python3.10/site-packages'
[29.337s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/pmac_robot/install/bin'
[29.338s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/pmac_bringup/package.ps1'
[29.338s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/pmac_robot/install/share/pmac_bringup/package.dsv'
[29.339s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/pmac_bringup/package.sh'
[29.339s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/pmac_bringup/package.bash'
[29.340s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/pmac_robot/install/share/pmac_bringup/package.zsh'
[29.340s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/pmac_robot/install/share/colcon-core/packages/pmac_bringup)
[29.341s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[29.341s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[29.341s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[29.341s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[29.347s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[29.347s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[29.348s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[29.360s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[29.360s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/pmac_robot/install/local_setup.ps1'
[29.362s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/code/pmac_robot/install/_local_setup_util_ps1.py'
[29.364s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/pmac_robot/install/setup.ps1'
[29.367s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/pmac_robot/install/local_setup.sh'
[29.368s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/code/pmac_robot/install/_local_setup_util_sh.py'
[29.369s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/pmac_robot/install/setup.sh'
[29.371s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/pmac_robot/install/local_setup.bash'
[29.371s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/pmac_robot/install/setup.bash'
[29.373s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/pmac_robot/install/local_setup.zsh'
[29.374s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/pmac_robot/install/setup.zsh'
