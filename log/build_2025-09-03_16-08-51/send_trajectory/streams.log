[0.030s] Invoking command in '/home/<USER>/code/pmac_robot/build/send_trajectory': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake /home/<USER>/code/pmac_robot/src/send_trajectory -DCMAKE_BUILD_TYPE=RelWithDebInfo -DBUILD_TESTING=0 -DCMAKE_EXPORT_COMPILE_COMMANDS=On -Wall -Wextra -Wpedantic -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/pmac_robot/install
[0.034s] [0mCMake Deprecation Warning at CMakeLists.txt:1 (cmake_minimum_required):
[0.034s]   Compatibility with CMake < 3.10 will be removed from a future version of
[0.035s]   CMake.
[0.035s] 
[0.035s]   Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
[0.035s]   to tell CMake that the project requires at least <min> but has been updated
[0.035s]   to work with policies introduced by <max> or earlier.
[0.035s] 
[0.035s] [0m
[0.120s] -- The C compiler identification is GNU 11.4.0
[0.212s] -- The CXX compiler identification is GNU 11.4.0
[0.223s] -- Detecting C compiler ABI info
[0.307s] -- Detecting C compiler ABI info - done
[0.336s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.337s] -- Detecting C compile features
[0.338s] -- Detecting C compile features - done
[0.356s] -- Detecting CXX compiler ABI info
[0.457s] -- Detecting CXX compiler ABI info - done
[0.478s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.478s] -- Detecting CXX compile features
[0.479s] -- Detecting CXX compile features - done
[0.495s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.691s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter
[0.780s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.978s] -- Found kdl_parser: 2.6.4 (/opt/ros/humble/share/kdl_parser/cmake)
[1.048s] -- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
[1.051s] -- Found Eigen3: TRUE (found version "3.4.0")
[1.051s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[1.090s] -- Found trajectory_msgs: 4.9.0 (/opt/ros/humble/share/trajectory_msgs/cmake)
[1.187s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[1.206s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[1.241s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[1.283s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[1.341s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[1.518s] -- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
[1.665s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[1.675s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[1.873s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")
[1.942s] -- Found FastRTPS: /opt/ros/humble/include
[2.033s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[2.057s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[2.140s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[2.142s] -- Found Threads: TRUE
[2.232s] -- Found control_msgs: 4.8.0 (/home/<USER>/code/ros2_control/install/share/control_msgs/cmake)
[2.494s] -- Configuring done (2.5s)
[2.531s] -- Generating done (0.0s)
[2.537s] -- Build files have been written to: /home/<USER>/code/pmac_robot/build/send_trajectory
[2.553s] Invoked command in '/home/<USER>/code/pmac_robot/build/send_trajectory' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake /home/<USER>/code/pmac_robot/src/send_trajectory -DCMAKE_BUILD_TYPE=RelWithDebInfo -DBUILD_TESTING=0 -DCMAKE_EXPORT_COMPILE_COMMANDS=On -Wall -Wextra -Wpedantic -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/pmac_robot/install
[2.557s] Invoking command in '/home/<USER>/code/pmac_robot/build/send_trajectory': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake --build /home/<USER>/code/pmac_robot/build/send_trajectory -- -j8 -l8
[2.594s] [ 50%] [32mBuilding CXX object CMakeFiles/send_trajectory.dir/src/send_trajctory.cpp.o[0m
[7.431s] [01m[K/home/<USER>/code/pmac_robot/src/send_trajectory/src/send_trajctory.cpp:[m[K In function ‘[01m[Kint main(int, char**)[m[K’:
[7.431s] [01m[K/home/<USER>/code/pmac_robot/src/send_trajectory/src/send_trajctory.cpp:40:14:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Ktwist[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[7.431s]    40 |         auto [01;35m[Ktwist[m[K = KDL::Twist();
[7.431s]       |              [01;35m[K^~~~~[m[K
[7.431s] [01m[K/home/<USER>/code/pmac_robot/src/send_trajectory/src/send_trajctory.cpp:60:16:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kdt[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[7.431s]    60 |         double [01;35m[Kdt[m[K = 1. / loop_rate;
[7.431s]       |                [01;35m[K^~[m[K
[14.617s] [100%] [1m[32mLinking CXX executable send_trajectory[0m
[14.934s] [100%] Built target send_trajectory
[14.942s] Invoked command in '/home/<USER>/code/pmac_robot/build/send_trajectory' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake --build /home/<USER>/code/pmac_robot/build/send_trajectory -- -j8 -l8
[14.943s] Invoking command in '/home/<USER>/code/pmac_robot/build/send_trajectory': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake --install /home/<USER>/code/pmac_robot/build/send_trajectory
[14.950s] -- Install configuration: "RelWithDebInfo"
[14.951s] -- Execute custom install script
[14.951s] -- Symlinking: /home/<USER>/code/pmac_robot/install/lib/send_trajectory/send_trajectory
[14.957s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/launch/send_trajectory.launch.py
[14.962s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/package_run_dependencies/send_trajectory
[14.968s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/parent_prefix_path/send_trajectory
[14.973s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/environment/ament_prefix_path.sh
[14.979s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/environment/ament_prefix_path.dsv
[14.984s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/environment/path.sh
[14.989s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/environment/path.dsv
[14.995s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/local_setup.bash
[15.000s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/local_setup.sh
[15.005s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/local_setup.zsh
[15.010s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/local_setup.dsv
[15.015s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/package.dsv
[15.020s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/packages/send_trajectory
[15.025s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/cmake/send_trajectoryConfig.cmake
[15.030s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/cmake/send_trajectoryConfig-version.cmake
[15.035s] -- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/package.xml
[15.042s] Invoked command in '/home/<USER>/code/pmac_robot/build/send_trajectory' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description /usr/bin/cmake --install /home/<USER>/code/pmac_robot/build/send_trajectory
