[0mCMake Deprecation Warning at CMakeLists.txt:1 (cmake_minimum_required):
  Compatibility with CMake < 3.10 will be removed from a future version of
  CMake.

  Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
  to tell CMake that the project requires at least <min> but has been updated
  to work with policies introduced by <max> or earlier.

[0m
-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found kdl_parser: 2.6.4 (/opt/ros/humble/share/kdl_parser/cmake)
-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
-- Found Eigen3: TRUE (found version "3.4.0")
-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
-- Found trajectory_msgs: 4.9.0 (/opt/ros/humble/share/trajectory_msgs/cmake)
-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")
-- Found FastRTPS: /opt/ros/humble/include
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- Found control_msgs: 4.8.0 (/home/<USER>/code/ros2_control/install/share/control_msgs/cmake)
-- Configuring done (2.5s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/code/pmac_robot/build/send_trajectory
[ 50%] [32mBuilding CXX object CMakeFiles/send_trajectory.dir/src/send_trajctory.cpp.o[0m
[01m[K/home/<USER>/code/pmac_robot/src/send_trajectory/src/send_trajctory.cpp:[m[K In function ‘[01m[Kint main(int, char**)[m[K’:
[01m[K/home/<USER>/code/pmac_robot/src/send_trajectory/src/send_trajctory.cpp:40:14:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Ktwist[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
   40 |         auto [01;35m[Ktwist[m[K = KDL::Twist();
      |              [01;35m[K^~~~~[m[K
[01m[K/home/<USER>/code/pmac_robot/src/send_trajectory/src/send_trajctory.cpp:60:16:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kdt[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
   60 |         double [01;35m[Kdt[m[K = 1. / loop_rate;
      |                [01;35m[K^~[m[K
[100%] [1m[32mLinking CXX executable send_trajectory[0m
[100%] Built target send_trajectory
-- Install configuration: "RelWithDebInfo"
-- Execute custom install script
-- Symlinking: /home/<USER>/code/pmac_robot/install/lib/send_trajectory/send_trajectory
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/launch/send_trajectory.launch.py
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/package_run_dependencies/send_trajectory
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/parent_prefix_path/send_trajectory
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/environment/ament_prefix_path.sh
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/environment/ament_prefix_path.dsv
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/environment/path.sh
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/environment/path.dsv
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/local_setup.bash
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/local_setup.sh
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/local_setup.zsh
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/local_setup.dsv
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/package.dsv
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/packages/send_trajectory
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/cmake/send_trajectoryConfig.cmake
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/cmake/send_trajectoryConfig-version.cmake
-- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/package.xml
