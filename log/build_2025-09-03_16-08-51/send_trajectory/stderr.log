[0mCMake Deprecation Warning at CMakeLists.txt:1 (cmake_minimum_required):
  Compatibility with CMake < 3.10 will be removed from a future version of
  CMake.

  Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
  to tell CMake that the project requires at least <min> but has been updated
  to work with policies introduced by <max> or earlier.

[0m
[01m[K/home/<USER>/code/pmac_robot/src/send_trajectory/src/send_trajctory.cpp:[m[K In function ‘[01m[Kint main(int, char**)[m[K’:
[01m[K/home/<USER>/code/pmac_robot/src/send_trajectory/src/send_trajctory.cpp:40:14:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Ktwist[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
   40 |         auto [01;35m[Ktwist[m[K = KDL::Twist();
      |              [01;35m[K^~~~~[m[K
[01m[K/home/<USER>/code/pmac_robot/src/send_trajectory/src/send_trajctory.cpp:60:16:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kdt[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
   60 |         double [01;35m[Kdt[m[K = 1. / loop_rate;
      |                [01;35m[K^~[m[K
