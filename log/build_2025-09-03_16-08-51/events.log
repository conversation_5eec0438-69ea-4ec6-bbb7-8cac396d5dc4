[0.000000] (-) TimerEvent: {}
[0.000254] (pmac_hardware_interface) JobQueued: {'identifier': 'pmac_hardware_interface', 'dependencies': OrderedDict()}
[0.000538] (pmac_primitive_controller) JobQueued: {'identifier': 'pmac_primitive_controller', 'dependencies': OrderedDict()}
[0.000932] (send_trajectory) JobQueued: {'identifier': 'send_trajectory', 'dependencies': OrderedDict()}
[0.000995] (tx2_60_description) JobQueued: {'identifier': 'tx2_60_description', 'dependencies': OrderedDict()}
[0.001033] (pmac_bringup) JobQueued: {'identifier': 'pmac_bringup', 'dependencies': OrderedDict([('pmac_hardware_interface', '/home/<USER>/code/pmac_robot/install'), ('pmac_primitive_controller', '/home/<USER>/code/pmac_robot/install'), ('tx2_60_description', '/home/<USER>/code/pmac_robot/install')])}
[0.001075] (pmac_hardware_interface) JobStarted: {'identifier': 'pmac_hardware_interface'}
[0.011634] (pmac_primitive_controller) JobStarted: {'identifier': 'pmac_primitive_controller'}
[0.020258] (tx2_60_description) JobStarted: {'identifier': 'tx2_60_description'}
[0.029548] (send_trajectory) JobStarted: {'identifier': 'send_trajectory'}
[0.039356] (pmac_hardware_interface) JobProgress: {'identifier': 'pmac_hardware_interface', 'progress': 'cmake'}
[0.040005] (pmac_hardware_interface) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/code/pmac_robot/src/pmac_hardware_interface', '-DCMAKE_BUILD_TYPE=RelWithDebInfo', '-DBUILD_TESTING=0', '-DCMAKE_EXPORT_COMPILE_COMMANDS=On', '-Wall', '-Wextra', '-Wpedantic', '-DAMENT_CMAKE_SYMLINK_INSTALL=1', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/code/pmac_robot/install'], 'cwd': '/home/<USER>/code/pmac_robot/build/pmac_hardware_interface', 'env': OrderedDict([('RAW_TERMINAL_COLOR_LIGHT_CYAN', '\x1b[1;36m'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HISTFILESIZE', '2147450879'), ('XMAKE_PROGRAM_DIR', '/home/<USER>/.local/share/xmake'), ('LANGUAGE', 'en_US:en'), ('USER', 'hq'), ('TERMINAL_BG_COLOR_GREEN', '\\e[42m'), ('LC_TIME', 'en_US.UTF-8'), ('TERMINAL_COLOR_USER_INPUT_DECISION', '\\e[0;35m'), ('XDG_SESSION_TYPE', 'wayland'), ('FZF_DEFAULT_OPTS', "--height 40% --layout=reverse --preview '(highlight -O ansi {} || cat {}) 2> /dev/null | head -500'"), ('TERMINAL_COLOR_GREEN', '\\e[0;32m'), ('RAW_TERMINAL_COLOR_WHITE', '\x1b[1;37m'), ('RAW_TERMINAL_COLOR_GRAY', '\x1b[1;30m'), ('TERMINAL_BG_COLOR_LIGHT_GREEN', '\\e[1;42m'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/code/ros2_control/install/lib:/home/<USER>/code/ecat_ws/install/ethercat_manager/lib:/home/<USER>/code/ecat_ws/install/ethercat_msgs/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/lib:/home/<USER>/code/ecat_ws/install/ethercat_driver/lib:/home/<USER>/code/ecat_ws/install/ethercat_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib::/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('LESS', '-R'), ('AMENT_CPPCHECK_ALLOW_SLOW_VERSIONS', 'true'), ('HOME', '/home/<USER>'), ('XMAKE_ROOTDIR', '/home/<USER>/.local/bin'), ('ROS_WS_CACHE_SOURCED_TIME', '0'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('TERMINAL_BG_COLOR_BLUE', '\\e[44m'), ('TERMINAL_COLOR_LIGHT_GREEN', '\\e[1;32m'), ('LSCOLORS', 'Gxfxcxdxdxegedabagacad'), ('RAW_TERMINAL_COLOR_BLACK', '\x1b[0;30m'), ('RAW_TERMINAL_COLOR_LIGHT_GRAY', '\x1b[0;37m'), ('TERMINAL_BG_COLOR_RED', '\\e[41m'), ('RTI_LICENSE_FILE', '/opt/rti.com/rti_connext_dds-5.3.1/rti_license.dat'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('TERMINAL_COLOR_BLUE', '\\e[0;34m'), ('PAGER', 'less'), ('TERMINAL_COLOR_RED', '\\e[0;31m'), ('LC_MONETARY', 'en_US.UTF-8'), ('TERMINAL_BG_COLOR_CYAN', '\\e[46m'), ('MANAGERPID', '2382'), ('TERMINAL_BG_COLOR_LIGHT_BLUE', '\\e[1;44m'), ('SYSTEMD_EXEC_PID', '2933'), ('TERMINAL_BG_COLOR_LIGHT_RED', '\\e[1;41m'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINAL_COLOR_LIGHT_BLUE', '\\e[1;34m'), ('TERMINAL_COLOR_CYAN', '\\e[0;36m'), ('RAW_TERMINAL_COLOR_BROWN', '\x1b[0;33m'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '104852'), ('TERMINAL_COLOR_LIGHT_RED', '\\e[1;31m'), ('TERMINAL_BG_COLOR_LIGHT_CYAN', '\\e[1;46m'), ('RAW_TERMINAL_COLOR_PURPLE', '\x1b[0;35m'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/code/ws_moveit2/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install:/home/<USER>/code/ws_tools/install:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install'), ('ROS_DISTRO', 'humble'), ('TERMINAL_COLOR_NC', '\\e[0m'), ('ARCHFLAGS', '-arch x86_64'), ('TERMINAL_COLOR_LIGHT_CYAN', '\\e[1;36m'), ('LOGNAME', 'hq'), ('OSH', '/home/<USER>/.oh-my-bash'), ('JOURNAL_STREAM', '8:39227'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('RAW_TERMINAL_COLOR_LIGHT_PURPLE', '\x1b[1;35m'), ('RAW_TERMINAL_COLOR_YELLOW', '\x1b[1;33m'), ('TERMINAL_COLOR_USER_CONFIRMATION', '\\e[0;34m'), ('USERNAME', 'hq'), ('TERMINAL_BG_COLOR_WHITE', '\\e[1;47m'), ('TERMINAL_BG_COLOR_GRAY', '\\e[1;40m'), ('TERM', 'xterm-kitty'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models:'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('TERMINAL_COLOR_WHITE', '\\e[1;37m'), ('TERMINAL_COLOR_GRAY', '\\e[1;30m'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.fzf/bin'), ('SESSION_MANAGER', 'local/ubuntu2204:@/tmp/.ICE-unix/2600,unix/ubuntu2204:/tmp/.ICE-unix/2600'), ('INVOCATION_ID', 'd66a9f60f2014cd8b9df27ca687295ae'), ('TERMINAL_BG_COLOR_LIGHT_GRAY', '\\e[47m'), ('PAPERSIZE', 'letter'), ('TERMINAL_BG_COLOR_BLACK', '\\e[40m'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'en_US.UTF-8'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('RTW_COLOR_NOTIFY_USER', '\\e[1;33m'), ('TERMINAL_COLOR_LIGHT_GRAY', '\\e[0;37m'), ('TERMINAL_COLOR_BLACK', '\\e[0;30m'), ('DISPLAY', ':0'), ('HISTSIZE', '2147450879'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'en_US.UTF-8'), ('TERMINAL_BG_COLOR_BROWN', '\\e[43m'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.CWO0B3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '0'), ('AMENT_PREFIX_PATH', '/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description:/opt/ros/humble'), ('SHELL', '/usr/bin/bash'), ('LC_NAME', 'en_US.UTF-8'), ('XMAKE_PROGRAM_FILE', '/home/<USER>/.local/bin/xmake'), ('TERMINAL_COLOR_BROWN', '\\e[0;33m'), ('TERMINAL_BG_COLOR_PURPLE', '\\e[45m'), ('ROS_WS', '/home/<USER>/code/pmac_robot/'), ('QT_ACCESSIBILITY', '1'), ('SSH_KEY_PATH', '~/.ssh/rsa_id'), ('RAW_TERMINAL_COLOR_GREEN', '\x1b[0;32m'), ('GDMSESSION', 'ubuntu'), ('KITTY_WINDOW_ID', '1'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('TERMINAL_COLOR_PURPLE', '\\e[0;35m'), ('TERMINAL_BG_COLOR_LIGHT_PURPLE', '\\e[1;45m'), ('LC_MEASUREMENT', 'en_US.UTF-8'), ('XMAKE_SHELL', 'bash'), ('RAW_TERMINAL_COLOR_LIGHT_GREEN', '\x1b[1;32m'), ('TERMINAL_COLOR_USER_NOTICE', '\\e[1;33m'), ('TERMINAL_BG_COLOR_YELLOW', '\\e[1;43m'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'en_US.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'ibus'), ('TERMINAL_COLOR_LIGHT_PURPLE', '\\e[1;35m'), ('TERMINAL_COLOR_YELLOW', '\\e[1;33m'), ('PWD', '/home/<USER>/code/pmac_robot/build/pmac_hardware_interface'), ('FZF_DEFAULT_COMMAND', 'ag --hidden --ignore .git -l -g ""'), ('RAW_TERMINAL_COLOR_BLUE', '\x1b[0;34m'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('RAW_TERMINAL_COLOR_RED', '\x1b[0;31m'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('RCUTILS_COLORIZED_OUTPUT', '1'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/code/ros2_control/build/rqt_joint_trajectory_controller:/home/<USER>/code/ros2_control/build/rqt_controller_manager:/home/<USER>/code/ros2_control/build/ros2controlcli:/home/<USER>/code/ros2_control/build/ros2_controllers_test_nodes:/home/<USER>/code/ros2_control/install/local/lib/python3.10/dist-packages:/home/<USER>/code/ros2_control/install/lib/python3.10/site-packages:/home/<USER>/code/ecat_ws/install/ethercat_msgs/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'en_US.UTF-8'), ('LC_PAPER', 'en_US.UTF-8'), ('RAW_TERMINAL_COLOR_CYAN', '\x1b[0;36m'), ('RAW_TERMINAL_COLOR_LIGHT_BLUE', '\x1b[1;34m'), ('RTW_COLOR_ERROR', '\\e[0;31m'), ('COLCON', '1'), ('RAW_TERMINAL_COLOR_LIGHT_RED', '\x1b[1;31m'), ('EDITOR', 'mvim'), ('CMAKE_PREFIX_PATH', '/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description'), ('RAW_TERMINAL_COLOR_NC', '\x1b[0m')]), 'shell': False}
[0.044008] (pmac_primitive_controller) JobProgress: {'identifier': 'pmac_primitive_controller', 'progress': 'cmake'}
[0.044402] (pmac_primitive_controller) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/code/pmac_robot/src/pmac_primitive_controller', '-DCMAKE_BUILD_TYPE=RelWithDebInfo', '-DBUILD_TESTING=0', '-DCMAKE_EXPORT_COMPILE_COMMANDS=On', '-Wall', '-Wextra', '-Wpedantic', '-DAMENT_CMAKE_SYMLINK_INSTALL=1', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/code/pmac_robot/install'], 'cwd': '/home/<USER>/code/pmac_robot/build/pmac_primitive_controller', 'env': OrderedDict([('RAW_TERMINAL_COLOR_LIGHT_CYAN', '\x1b[1;36m'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HISTFILESIZE', '2147450879'), ('XMAKE_PROGRAM_DIR', '/home/<USER>/.local/share/xmake'), ('LANGUAGE', 'en_US:en'), ('USER', 'hq'), ('TERMINAL_BG_COLOR_GREEN', '\\e[42m'), ('LC_TIME', 'en_US.UTF-8'), ('TERMINAL_COLOR_USER_INPUT_DECISION', '\\e[0;35m'), ('XDG_SESSION_TYPE', 'wayland'), ('FZF_DEFAULT_OPTS', "--height 40% --layout=reverse --preview '(highlight -O ansi {} || cat {}) 2> /dev/null | head -500'"), ('TERMINAL_COLOR_GREEN', '\\e[0;32m'), ('RAW_TERMINAL_COLOR_WHITE', '\x1b[1;37m'), ('RAW_TERMINAL_COLOR_GRAY', '\x1b[1;30m'), ('TERMINAL_BG_COLOR_LIGHT_GREEN', '\\e[1;42m'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/code/ros2_control/install/lib:/home/<USER>/code/ecat_ws/install/ethercat_manager/lib:/home/<USER>/code/ecat_ws/install/ethercat_msgs/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/lib:/home/<USER>/code/ecat_ws/install/ethercat_driver/lib:/home/<USER>/code/ecat_ws/install/ethercat_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib::/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('LESS', '-R'), ('AMENT_CPPCHECK_ALLOW_SLOW_VERSIONS', 'true'), ('HOME', '/home/<USER>'), ('XMAKE_ROOTDIR', '/home/<USER>/.local/bin'), ('ROS_WS_CACHE_SOURCED_TIME', '0'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('TERMINAL_BG_COLOR_BLUE', '\\e[44m'), ('TERMINAL_COLOR_LIGHT_GREEN', '\\e[1;32m'), ('LSCOLORS', 'Gxfxcxdxdxegedabagacad'), ('RAW_TERMINAL_COLOR_BLACK', '\x1b[0;30m'), ('RAW_TERMINAL_COLOR_LIGHT_GRAY', '\x1b[0;37m'), ('TERMINAL_BG_COLOR_RED', '\\e[41m'), ('RTI_LICENSE_FILE', '/opt/rti.com/rti_connext_dds-5.3.1/rti_license.dat'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('TERMINAL_COLOR_BLUE', '\\e[0;34m'), ('PAGER', 'less'), ('TERMINAL_COLOR_RED', '\\e[0;31m'), ('LC_MONETARY', 'en_US.UTF-8'), ('TERMINAL_BG_COLOR_CYAN', '\\e[46m'), ('MANAGERPID', '2382'), ('TERMINAL_BG_COLOR_LIGHT_BLUE', '\\e[1;44m'), ('SYSTEMD_EXEC_PID', '2933'), ('TERMINAL_BG_COLOR_LIGHT_RED', '\\e[1;41m'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINAL_COLOR_LIGHT_BLUE', '\\e[1;34m'), ('TERMINAL_COLOR_CYAN', '\\e[0;36m'), ('RAW_TERMINAL_COLOR_BROWN', '\x1b[0;33m'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '104852'), ('TERMINAL_COLOR_LIGHT_RED', '\\e[1;31m'), ('TERMINAL_BG_COLOR_LIGHT_CYAN', '\\e[1;46m'), ('RAW_TERMINAL_COLOR_PURPLE', '\x1b[0;35m'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/code/ws_moveit2/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install:/home/<USER>/code/ws_tools/install:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install'), ('ROS_DISTRO', 'humble'), ('TERMINAL_COLOR_NC', '\\e[0m'), ('ARCHFLAGS', '-arch x86_64'), ('TERMINAL_COLOR_LIGHT_CYAN', '\\e[1;36m'), ('LOGNAME', 'hq'), ('OSH', '/home/<USER>/.oh-my-bash'), ('JOURNAL_STREAM', '8:39227'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('RAW_TERMINAL_COLOR_LIGHT_PURPLE', '\x1b[1;35m'), ('RAW_TERMINAL_COLOR_YELLOW', '\x1b[1;33m'), ('TERMINAL_COLOR_USER_CONFIRMATION', '\\e[0;34m'), ('USERNAME', 'hq'), ('TERMINAL_BG_COLOR_WHITE', '\\e[1;47m'), ('TERMINAL_BG_COLOR_GRAY', '\\e[1;40m'), ('TERM', 'xterm-kitty'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models:'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('TERMINAL_COLOR_WHITE', '\\e[1;37m'), ('TERMINAL_COLOR_GRAY', '\\e[1;30m'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.fzf/bin'), ('SESSION_MANAGER', 'local/ubuntu2204:@/tmp/.ICE-unix/2600,unix/ubuntu2204:/tmp/.ICE-unix/2600'), ('INVOCATION_ID', 'd66a9f60f2014cd8b9df27ca687295ae'), ('TERMINAL_BG_COLOR_LIGHT_GRAY', '\\e[47m'), ('PAPERSIZE', 'letter'), ('TERMINAL_BG_COLOR_BLACK', '\\e[40m'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'en_US.UTF-8'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('RTW_COLOR_NOTIFY_USER', '\\e[1;33m'), ('TERMINAL_COLOR_LIGHT_GRAY', '\\e[0;37m'), ('TERMINAL_COLOR_BLACK', '\\e[0;30m'), ('DISPLAY', ':0'), ('HISTSIZE', '2147450879'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'en_US.UTF-8'), ('TERMINAL_BG_COLOR_BROWN', '\\e[43m'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.CWO0B3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '0'), ('AMENT_PREFIX_PATH', '/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description:/opt/ros/humble'), ('SHELL', '/usr/bin/bash'), ('LC_NAME', 'en_US.UTF-8'), ('XMAKE_PROGRAM_FILE', '/home/<USER>/.local/bin/xmake'), ('TERMINAL_COLOR_BROWN', '\\e[0;33m'), ('TERMINAL_BG_COLOR_PURPLE', '\\e[45m'), ('ROS_WS', '/home/<USER>/code/pmac_robot/'), ('QT_ACCESSIBILITY', '1'), ('SSH_KEY_PATH', '~/.ssh/rsa_id'), ('RAW_TERMINAL_COLOR_GREEN', '\x1b[0;32m'), ('GDMSESSION', 'ubuntu'), ('KITTY_WINDOW_ID', '1'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('TERMINAL_COLOR_PURPLE', '\\e[0;35m'), ('TERMINAL_BG_COLOR_LIGHT_PURPLE', '\\e[1;45m'), ('LC_MEASUREMENT', 'en_US.UTF-8'), ('XMAKE_SHELL', 'bash'), ('RAW_TERMINAL_COLOR_LIGHT_GREEN', '\x1b[1;32m'), ('TERMINAL_COLOR_USER_NOTICE', '\\e[1;33m'), ('TERMINAL_BG_COLOR_YELLOW', '\\e[1;43m'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'en_US.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'ibus'), ('TERMINAL_COLOR_LIGHT_PURPLE', '\\e[1;35m'), ('TERMINAL_COLOR_YELLOW', '\\e[1;33m'), ('PWD', '/home/<USER>/code/pmac_robot/build/pmac_primitive_controller'), ('FZF_DEFAULT_COMMAND', 'ag --hidden --ignore .git -l -g ""'), ('RAW_TERMINAL_COLOR_BLUE', '\x1b[0;34m'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('RAW_TERMINAL_COLOR_RED', '\x1b[0;31m'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('RCUTILS_COLORIZED_OUTPUT', '1'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/code/ros2_control/build/rqt_joint_trajectory_controller:/home/<USER>/code/ros2_control/build/rqt_controller_manager:/home/<USER>/code/ros2_control/build/ros2controlcli:/home/<USER>/code/ros2_control/build/ros2_controllers_test_nodes:/home/<USER>/code/ros2_control/install/local/lib/python3.10/dist-packages:/home/<USER>/code/ros2_control/install/lib/python3.10/site-packages:/home/<USER>/code/ecat_ws/install/ethercat_msgs/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'en_US.UTF-8'), ('LC_PAPER', 'en_US.UTF-8'), ('RAW_TERMINAL_COLOR_CYAN', '\x1b[0;36m'), ('RAW_TERMINAL_COLOR_LIGHT_BLUE', '\x1b[1;34m'), ('RTW_COLOR_ERROR', '\\e[0;31m'), ('COLCON', '1'), ('RAW_TERMINAL_COLOR_LIGHT_RED', '\x1b[1;31m'), ('EDITOR', 'mvim'), ('CMAKE_PREFIX_PATH', '/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description'), ('RAW_TERMINAL_COLOR_NC', '\x1b[0m')]), 'shell': False}
[0.047000] (tx2_60_description) JobProgress: {'identifier': 'tx2_60_description', 'progress': 'cmake'}
[0.047503] (tx2_60_description) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/code/pmac_robot/src/tx2_60_description', '-DCMAKE_BUILD_TYPE=RelWithDebInfo', '-DBUILD_TESTING=0', '-DCMAKE_EXPORT_COMPILE_COMMANDS=On', '-Wall', '-Wextra', '-Wpedantic', '-DAMENT_CMAKE_SYMLINK_INSTALL=1', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/code/pmac_robot/install'], 'cwd': '/home/<USER>/code/pmac_robot/build/tx2_60_description', 'env': OrderedDict([('RAW_TERMINAL_COLOR_LIGHT_CYAN', '\x1b[1;36m'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HISTFILESIZE', '2147450879'), ('XMAKE_PROGRAM_DIR', '/home/<USER>/.local/share/xmake'), ('LANGUAGE', 'en_US:en'), ('USER', 'hq'), ('TERMINAL_BG_COLOR_GREEN', '\\e[42m'), ('LC_TIME', 'en_US.UTF-8'), ('TERMINAL_COLOR_USER_INPUT_DECISION', '\\e[0;35m'), ('XDG_SESSION_TYPE', 'wayland'), ('FZF_DEFAULT_OPTS', "--height 40% --layout=reverse --preview '(highlight -O ansi {} || cat {}) 2> /dev/null | head -500'"), ('TERMINAL_COLOR_GREEN', '\\e[0;32m'), ('RAW_TERMINAL_COLOR_WHITE', '\x1b[1;37m'), ('RAW_TERMINAL_COLOR_GRAY', '\x1b[1;30m'), ('TERMINAL_BG_COLOR_LIGHT_GREEN', '\\e[1;42m'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/code/ros2_control/install/lib:/home/<USER>/code/ecat_ws/install/ethercat_manager/lib:/home/<USER>/code/ecat_ws/install/ethercat_msgs/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/lib:/home/<USER>/code/ecat_ws/install/ethercat_driver/lib:/home/<USER>/code/ecat_ws/install/ethercat_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib::/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('LESS', '-R'), ('AMENT_CPPCHECK_ALLOW_SLOW_VERSIONS', 'true'), ('HOME', '/home/<USER>'), ('XMAKE_ROOTDIR', '/home/<USER>/.local/bin'), ('ROS_WS_CACHE_SOURCED_TIME', '0'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('TERMINAL_BG_COLOR_BLUE', '\\e[44m'), ('TERMINAL_COLOR_LIGHT_GREEN', '\\e[1;32m'), ('LSCOLORS', 'Gxfxcxdxdxegedabagacad'), ('RAW_TERMINAL_COLOR_BLACK', '\x1b[0;30m'), ('RAW_TERMINAL_COLOR_LIGHT_GRAY', '\x1b[0;37m'), ('TERMINAL_BG_COLOR_RED', '\\e[41m'), ('RTI_LICENSE_FILE', '/opt/rti.com/rti_connext_dds-5.3.1/rti_license.dat'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('TERMINAL_COLOR_BLUE', '\\e[0;34m'), ('PAGER', 'less'), ('TERMINAL_COLOR_RED', '\\e[0;31m'), ('LC_MONETARY', 'en_US.UTF-8'), ('TERMINAL_BG_COLOR_CYAN', '\\e[46m'), ('MANAGERPID', '2382'), ('TERMINAL_BG_COLOR_LIGHT_BLUE', '\\e[1;44m'), ('SYSTEMD_EXEC_PID', '2933'), ('TERMINAL_BG_COLOR_LIGHT_RED', '\\e[1;41m'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINAL_COLOR_LIGHT_BLUE', '\\e[1;34m'), ('TERMINAL_COLOR_CYAN', '\\e[0;36m'), ('RAW_TERMINAL_COLOR_BROWN', '\x1b[0;33m'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '104852'), ('TERMINAL_COLOR_LIGHT_RED', '\\e[1;31m'), ('TERMINAL_BG_COLOR_LIGHT_CYAN', '\\e[1;46m'), ('RAW_TERMINAL_COLOR_PURPLE', '\x1b[0;35m'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/code/ws_moveit2/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install:/home/<USER>/code/ws_tools/install:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install'), ('ROS_DISTRO', 'humble'), ('TERMINAL_COLOR_NC', '\\e[0m'), ('ARCHFLAGS', '-arch x86_64'), ('TERMINAL_COLOR_LIGHT_CYAN', '\\e[1;36m'), ('LOGNAME', 'hq'), ('OSH', '/home/<USER>/.oh-my-bash'), ('JOURNAL_STREAM', '8:39227'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('RAW_TERMINAL_COLOR_LIGHT_PURPLE', '\x1b[1;35m'), ('RAW_TERMINAL_COLOR_YELLOW', '\x1b[1;33m'), ('TERMINAL_COLOR_USER_CONFIRMATION', '\\e[0;34m'), ('USERNAME', 'hq'), ('TERMINAL_BG_COLOR_WHITE', '\\e[1;47m'), ('TERMINAL_BG_COLOR_GRAY', '\\e[1;40m'), ('TERM', 'xterm-kitty'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models:'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('TERMINAL_COLOR_WHITE', '\\e[1;37m'), ('TERMINAL_COLOR_GRAY', '\\e[1;30m'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.fzf/bin'), ('SESSION_MANAGER', 'local/ubuntu2204:@/tmp/.ICE-unix/2600,unix/ubuntu2204:/tmp/.ICE-unix/2600'), ('INVOCATION_ID', 'd66a9f60f2014cd8b9df27ca687295ae'), ('TERMINAL_BG_COLOR_LIGHT_GRAY', '\\e[47m'), ('PAPERSIZE', 'letter'), ('TERMINAL_BG_COLOR_BLACK', '\\e[40m'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'en_US.UTF-8'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('RTW_COLOR_NOTIFY_USER', '\\e[1;33m'), ('TERMINAL_COLOR_LIGHT_GRAY', '\\e[0;37m'), ('TERMINAL_COLOR_BLACK', '\\e[0;30m'), ('DISPLAY', ':0'), ('HISTSIZE', '2147450879'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'en_US.UTF-8'), ('TERMINAL_BG_COLOR_BROWN', '\\e[43m'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.CWO0B3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '0'), ('AMENT_PREFIX_PATH', '/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description:/opt/ros/humble'), ('SHELL', '/usr/bin/bash'), ('LC_NAME', 'en_US.UTF-8'), ('XMAKE_PROGRAM_FILE', '/home/<USER>/.local/bin/xmake'), ('TERMINAL_COLOR_BROWN', '\\e[0;33m'), ('TERMINAL_BG_COLOR_PURPLE', '\\e[45m'), ('ROS_WS', '/home/<USER>/code/pmac_robot/'), ('QT_ACCESSIBILITY', '1'), ('SSH_KEY_PATH', '~/.ssh/rsa_id'), ('RAW_TERMINAL_COLOR_GREEN', '\x1b[0;32m'), ('GDMSESSION', 'ubuntu'), ('KITTY_WINDOW_ID', '1'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('TERMINAL_COLOR_PURPLE', '\\e[0;35m'), ('TERMINAL_BG_COLOR_LIGHT_PURPLE', '\\e[1;45m'), ('LC_MEASUREMENT', 'en_US.UTF-8'), ('XMAKE_SHELL', 'bash'), ('RAW_TERMINAL_COLOR_LIGHT_GREEN', '\x1b[1;32m'), ('TERMINAL_COLOR_USER_NOTICE', '\\e[1;33m'), ('TERMINAL_BG_COLOR_YELLOW', '\\e[1;43m'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'en_US.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'ibus'), ('TERMINAL_COLOR_LIGHT_PURPLE', '\\e[1;35m'), ('TERMINAL_COLOR_YELLOW', '\\e[1;33m'), ('PWD', '/home/<USER>/code/pmac_robot/build/tx2_60_description'), ('FZF_DEFAULT_COMMAND', 'ag --hidden --ignore .git -l -g ""'), ('RAW_TERMINAL_COLOR_BLUE', '\x1b[0;34m'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('RAW_TERMINAL_COLOR_RED', '\x1b[0;31m'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('RCUTILS_COLORIZED_OUTPUT', '1'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/code/ros2_control/build/rqt_joint_trajectory_controller:/home/<USER>/code/ros2_control/build/rqt_controller_manager:/home/<USER>/code/ros2_control/build/ros2controlcli:/home/<USER>/code/ros2_control/build/ros2_controllers_test_nodes:/home/<USER>/code/ros2_control/install/local/lib/python3.10/dist-packages:/home/<USER>/code/ros2_control/install/lib/python3.10/site-packages:/home/<USER>/code/ecat_ws/install/ethercat_msgs/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'en_US.UTF-8'), ('LC_PAPER', 'en_US.UTF-8'), ('RAW_TERMINAL_COLOR_CYAN', '\x1b[0;36m'), ('RAW_TERMINAL_COLOR_LIGHT_BLUE', '\x1b[1;34m'), ('RTW_COLOR_ERROR', '\\e[0;31m'), ('COLCON', '1'), ('RAW_TERMINAL_COLOR_LIGHT_RED', '\x1b[1;31m'), ('EDITOR', 'mvim'), ('CMAKE_PREFIX_PATH', '/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description'), ('RAW_TERMINAL_COLOR_NC', '\x1b[0m')]), 'shell': False}
[0.051677] (pmac_hardware_interface) StderrLine: {'line': b'\x1b[0mCMake Deprecation Warning at CMakeLists.txt:1 (cmake_minimum_required):\n'}
[0.053505] (pmac_hardware_interface) StderrLine: {'line': b'  Compatibility with CMake < 3.10 will be removed from a future version of\n'}
[0.053898] (pmac_hardware_interface) StderrLine: {'line': b'  CMake.\n'}
[0.054374] (pmac_hardware_interface) StderrLine: {'line': b'\n'}
[0.054714] (pmac_hardware_interface) StderrLine: {'line': b'  Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax\n'}
[0.054976] (pmac_hardware_interface) StderrLine: {'line': b'  to tell CMake that the project requires at least <min> but has been updated\n'}
[0.055256] (pmac_hardware_interface) StderrLine: {'line': b'  to work with policies introduced by <max> or earlier.\n'}
[0.055468] (pmac_hardware_interface) StderrLine: {'line': b'\n'}
[0.055728] (pmac_hardware_interface) StderrLine: {'line': b'\x1b[0m\n'}
[0.057852] (pmac_primitive_controller) StderrLine: {'line': b'\x1b[0mCMake Deprecation Warning at CMakeLists.txt:1 (cmake_minimum_required):\n'}
[0.058529] (pmac_primitive_controller) StderrLine: {'line': b'  Compatibility with CMake < 3.10 will be removed from a future version of\n'}
[0.058664] (pmac_primitive_controller) StderrLine: {'line': b'  CMake.\n'}
[0.058740] (pmac_primitive_controller) StderrLine: {'line': b'\n'}
[0.058807] (pmac_primitive_controller) StderrLine: {'line': b'  Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax\n'}
[0.058873] (pmac_primitive_controller) StderrLine: {'line': b'  to tell CMake that the project requires at least <min> but has been updated\n'}
[0.058937] (pmac_primitive_controller) StderrLine: {'line': b'  to work with policies introduced by <max> or earlier.\n'}
[0.059002] (pmac_primitive_controller) StderrLine: {'line': b'\n'}
[0.059065] (pmac_primitive_controller) StderrLine: {'line': b'\x1b[0m\n'}
[0.059130] (send_trajectory) JobProgress: {'identifier': 'send_trajectory', 'progress': 'cmake'}
[0.059155] (tx2_60_description) StderrLine: {'line': b'\x1b[0mCMake Deprecation Warning at CMakeLists.txt:1 (cmake_minimum_required):\n'}
[0.059241] (tx2_60_description) StderrLine: {'line': b'  Compatibility with CMake < 3.10 will be removed from a future version of\n'}
[0.059306] (tx2_60_description) StderrLine: {'line': b'  CMake.\n'}
[0.059369] (tx2_60_description) StderrLine: {'line': b'\n'}
[0.059431] (tx2_60_description) StderrLine: {'line': b'  Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax\n'}
[0.059494] (tx2_60_description) StderrLine: {'line': b'  to tell CMake that the project requires at least <min> but has been updated\n'}
[0.059565] (tx2_60_description) StderrLine: {'line': b'  to work with policies introduced by <max> or earlier.\n'}
[0.059654] (tx2_60_description) StderrLine: {'line': b'\n'}
[0.059719] (tx2_60_description) StderrLine: {'line': b'\x1b[0m\n'}
[0.059784] (send_trajectory) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/code/pmac_robot/src/send_trajectory', '-DCMAKE_BUILD_TYPE=RelWithDebInfo', '-DBUILD_TESTING=0', '-DCMAKE_EXPORT_COMPILE_COMMANDS=On', '-Wall', '-Wextra', '-Wpedantic', '-DAMENT_CMAKE_SYMLINK_INSTALL=1', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/code/pmac_robot/install'], 'cwd': '/home/<USER>/code/pmac_robot/build/send_trajectory', 'env': OrderedDict([('RAW_TERMINAL_COLOR_LIGHT_CYAN', '\x1b[1;36m'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HISTFILESIZE', '2147450879'), ('XMAKE_PROGRAM_DIR', '/home/<USER>/.local/share/xmake'), ('LANGUAGE', 'en_US:en'), ('USER', 'hq'), ('TERMINAL_BG_COLOR_GREEN', '\\e[42m'), ('LC_TIME', 'en_US.UTF-8'), ('TERMINAL_COLOR_USER_INPUT_DECISION', '\\e[0;35m'), ('XDG_SESSION_TYPE', 'wayland'), ('FZF_DEFAULT_OPTS', "--height 40% --layout=reverse --preview '(highlight -O ansi {} || cat {}) 2> /dev/null | head -500'"), ('TERMINAL_COLOR_GREEN', '\\e[0;32m'), ('RAW_TERMINAL_COLOR_WHITE', '\x1b[1;37m'), ('RAW_TERMINAL_COLOR_GRAY', '\x1b[1;30m'), ('TERMINAL_BG_COLOR_LIGHT_GREEN', '\\e[1;42m'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/code/ros2_control/install/lib:/home/<USER>/code/ecat_ws/install/ethercat_manager/lib:/home/<USER>/code/ecat_ws/install/ethercat_msgs/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/lib:/home/<USER>/code/ecat_ws/install/ethercat_driver/lib:/home/<USER>/code/ecat_ws/install/ethercat_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib::/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('LESS', '-R'), ('AMENT_CPPCHECK_ALLOW_SLOW_VERSIONS', 'true'), ('HOME', '/home/<USER>'), ('XMAKE_ROOTDIR', '/home/<USER>/.local/bin'), ('ROS_WS_CACHE_SOURCED_TIME', '0'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('TERMINAL_BG_COLOR_BLUE', '\\e[44m'), ('TERMINAL_COLOR_LIGHT_GREEN', '\\e[1;32m'), ('LSCOLORS', 'Gxfxcxdxdxegedabagacad'), ('RAW_TERMINAL_COLOR_BLACK', '\x1b[0;30m'), ('RAW_TERMINAL_COLOR_LIGHT_GRAY', '\x1b[0;37m'), ('TERMINAL_BG_COLOR_RED', '\\e[41m'), ('RTI_LICENSE_FILE', '/opt/rti.com/rti_connext_dds-5.3.1/rti_license.dat'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('TERMINAL_COLOR_BLUE', '\\e[0;34m'), ('PAGER', 'less'), ('TERMINAL_COLOR_RED', '\\e[0;31m'), ('LC_MONETARY', 'en_US.UTF-8'), ('TERMINAL_BG_COLOR_CYAN', '\\e[46m'), ('MANAGERPID', '2382'), ('TERMINAL_BG_COLOR_LIGHT_BLUE', '\\e[1;44m'), ('SYSTEMD_EXEC_PID', '2933'), ('TERMINAL_BG_COLOR_LIGHT_RED', '\\e[1;41m'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINAL_COLOR_LIGHT_BLUE', '\\e[1;34m'), ('TERMINAL_COLOR_CYAN', '\\e[0;36m'), ('RAW_TERMINAL_COLOR_BROWN', '\x1b[0;33m'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '104852'), ('TERMINAL_COLOR_LIGHT_RED', '\\e[1;31m'), ('TERMINAL_BG_COLOR_LIGHT_CYAN', '\\e[1;46m'), ('RAW_TERMINAL_COLOR_PURPLE', '\x1b[0;35m'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/code/ws_moveit2/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install:/home/<USER>/code/ws_tools/install:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install'), ('ROS_DISTRO', 'humble'), ('TERMINAL_COLOR_NC', '\\e[0m'), ('ARCHFLAGS', '-arch x86_64'), ('TERMINAL_COLOR_LIGHT_CYAN', '\\e[1;36m'), ('LOGNAME', 'hq'), ('OSH', '/home/<USER>/.oh-my-bash'), ('JOURNAL_STREAM', '8:39227'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('RAW_TERMINAL_COLOR_LIGHT_PURPLE', '\x1b[1;35m'), ('RAW_TERMINAL_COLOR_YELLOW', '\x1b[1;33m'), ('TERMINAL_COLOR_USER_CONFIRMATION', '\\e[0;34m'), ('USERNAME', 'hq'), ('TERMINAL_BG_COLOR_WHITE', '\\e[1;47m'), ('TERMINAL_BG_COLOR_GRAY', '\\e[1;40m'), ('TERM', 'xterm-kitty'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models:'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('TERMINAL_COLOR_WHITE', '\\e[1;37m'), ('TERMINAL_COLOR_GRAY', '\\e[1;30m'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.fzf/bin'), ('SESSION_MANAGER', 'local/ubuntu2204:@/tmp/.ICE-unix/2600,unix/ubuntu2204:/tmp/.ICE-unix/2600'), ('INVOCATION_ID', 'd66a9f60f2014cd8b9df27ca687295ae'), ('TERMINAL_BG_COLOR_LIGHT_GRAY', '\\e[47m'), ('PAPERSIZE', 'letter'), ('TERMINAL_BG_COLOR_BLACK', '\\e[40m'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'en_US.UTF-8'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('RTW_COLOR_NOTIFY_USER', '\\e[1;33m'), ('TERMINAL_COLOR_LIGHT_GRAY', '\\e[0;37m'), ('TERMINAL_COLOR_BLACK', '\\e[0;30m'), ('DISPLAY', ':0'), ('HISTSIZE', '2147450879'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'en_US.UTF-8'), ('TERMINAL_BG_COLOR_BROWN', '\\e[43m'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.CWO0B3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '0'), ('AMENT_PREFIX_PATH', '/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description:/opt/ros/humble'), ('SHELL', '/usr/bin/bash'), ('LC_NAME', 'en_US.UTF-8'), ('XMAKE_PROGRAM_FILE', '/home/<USER>/.local/bin/xmake'), ('TERMINAL_COLOR_BROWN', '\\e[0;33m'), ('TERMINAL_BG_COLOR_PURPLE', '\\e[45m'), ('ROS_WS', '/home/<USER>/code/pmac_robot/'), ('QT_ACCESSIBILITY', '1'), ('SSH_KEY_PATH', '~/.ssh/rsa_id'), ('RAW_TERMINAL_COLOR_GREEN', '\x1b[0;32m'), ('GDMSESSION', 'ubuntu'), ('KITTY_WINDOW_ID', '1'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('TERMINAL_COLOR_PURPLE', '\\e[0;35m'), ('TERMINAL_BG_COLOR_LIGHT_PURPLE', '\\e[1;45m'), ('LC_MEASUREMENT', 'en_US.UTF-8'), ('XMAKE_SHELL', 'bash'), ('RAW_TERMINAL_COLOR_LIGHT_GREEN', '\x1b[1;32m'), ('TERMINAL_COLOR_USER_NOTICE', '\\e[1;33m'), ('TERMINAL_BG_COLOR_YELLOW', '\\e[1;43m'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'en_US.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'ibus'), ('TERMINAL_COLOR_LIGHT_PURPLE', '\\e[1;35m'), ('TERMINAL_COLOR_YELLOW', '\\e[1;33m'), ('PWD', '/home/<USER>/code/pmac_robot/build/send_trajectory'), ('FZF_DEFAULT_COMMAND', 'ag --hidden --ignore .git -l -g ""'), ('RAW_TERMINAL_COLOR_BLUE', '\x1b[0;34m'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('RAW_TERMINAL_COLOR_RED', '\x1b[0;31m'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('RCUTILS_COLORIZED_OUTPUT', '1'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/code/ros2_control/build/rqt_joint_trajectory_controller:/home/<USER>/code/ros2_control/build/rqt_controller_manager:/home/<USER>/code/ros2_control/build/ros2controlcli:/home/<USER>/code/ros2_control/build/ros2_controllers_test_nodes:/home/<USER>/code/ros2_control/install/local/lib/python3.10/dist-packages:/home/<USER>/code/ros2_control/install/lib/python3.10/site-packages:/home/<USER>/code/ecat_ws/install/ethercat_msgs/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'en_US.UTF-8'), ('LC_PAPER', 'en_US.UTF-8'), ('RAW_TERMINAL_COLOR_CYAN', '\x1b[0;36m'), ('RAW_TERMINAL_COLOR_LIGHT_BLUE', '\x1b[1;34m'), ('RTW_COLOR_ERROR', '\\e[0;31m'), ('COLCON', '1'), ('RAW_TERMINAL_COLOR_LIGHT_RED', '\x1b[1;31m'), ('EDITOR', 'mvim'), ('CMAKE_PREFIX_PATH', '/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description'), ('RAW_TERMINAL_COLOR_NC', '\x1b[0m')]), 'shell': False}
[0.064162] (send_trajectory) StderrLine: {'line': b'\x1b[0mCMake Deprecation Warning at CMakeLists.txt:1 (cmake_minimum_required):\n'}
[0.064417] (send_trajectory) StderrLine: {'line': b'  Compatibility with CMake < 3.10 will be removed from a future version of\n'}
[0.064558] (send_trajectory) StderrLine: {'line': b'  CMake.\n'}
[0.064699] (send_trajectory) StderrLine: {'line': b'\n'}
[0.064834] (send_trajectory) StderrLine: {'line': b'  Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax\n'}
[0.064964] (send_trajectory) StderrLine: {'line': b'  to tell CMake that the project requires at least <min> but has been updated\n'}
[0.065094] (send_trajectory) StderrLine: {'line': b'  to work with policies introduced by <max> or earlier.\n'}
[0.065222] (send_trajectory) StderrLine: {'line': b'\n'}
[0.065353] (send_trajectory) StderrLine: {'line': b'\x1b[0m\n'}
[0.099752] (-) TimerEvent: {}
[0.149248] (tx2_60_description) StdoutLine: {'line': b'-- The C compiler identification is GNU 11.4.0\n'}
[0.149545] (send_trajectory) StdoutLine: {'line': b'-- The C compiler identification is GNU 11.4.0\n'}
[0.150000] (pmac_hardware_interface) StdoutLine: {'line': b'-- The C compiler identification is GNU 11.4.0\n'}
[0.150095] (pmac_primitive_controller) StdoutLine: {'line': b'-- The C compiler identification is GNU 11.4.0\n'}
[0.199883] (-) TimerEvent: {}
[0.240960] (tx2_60_description) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 11.4.0\n'}
[0.242377] (send_trajectory) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 11.4.0\n'}
[0.253240] (send_trajectory) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.253643] (tx2_60_description) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.272247] (pmac_hardware_interface) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 11.4.0\n'}
[0.277166] (pmac_primitive_controller) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 11.4.0\n'}
[0.283933] (pmac_hardware_interface) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.292207] (pmac_primitive_controller) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.299983] (-) TimerEvent: {}
[0.337146] (send_trajectory) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.340229] (tx2_60_description) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.361550] (tx2_60_description) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.361877] (tx2_60_description) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.362306] (tx2_60_description) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.365366] (pmac_hardware_interface) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.366452] (send_trajectory) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.366883] (send_trajectory) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.367547] (send_trajectory) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.382234] (tx2_60_description) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.386396] (send_trajectory) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.388495] (pmac_primitive_controller) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.394627] (pmac_hardware_interface) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.395173] (pmac_hardware_interface) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.396025] (pmac_hardware_interface) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.400084] (-) TimerEvent: {}
[0.416259] (pmac_hardware_interface) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.417457] (pmac_primitive_controller) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.417892] (pmac_primitive_controller) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.418298] (pmac_primitive_controller) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.436275] (pmac_primitive_controller) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.480287] (tx2_60_description) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.487066] (send_trajectory) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.500189] (-) TimerEvent: {}
[0.507910] (send_trajectory) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.508485] (send_trajectory) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.509205] (send_trajectory) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.514777] (tx2_60_description) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.515421] (tx2_60_description) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.516312] (tx2_60_description) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.524901] (send_trajectory) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.528468] (pmac_hardware_interface) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.530426] (tx2_60_description) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.536298] (pmac_primitive_controller) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.560064] (pmac_hardware_interface) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.560617] (pmac_hardware_interface) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.561640] (pmac_hardware_interface) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.567134] (pmac_primitive_controller) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.567498] (pmac_primitive_controller) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.568186] (pmac_primitive_controller) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.575681] (pmac_hardware_interface) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.581350] (pmac_primitive_controller) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.600287] (-) TimerEvent: {}
[0.700647] (-) TimerEvent: {}
[0.720777] (send_trajectory) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter\n'}
[0.728961] (tx2_60_description) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter\n'}
[0.777671] (pmac_hardware_interface) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter\n'}
[0.788934] (pmac_primitive_controller) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter\n'}
[0.800746] (-) TimerEvent: {}
[0.810099] (send_trajectory) StdoutLine: {'line': b'-- Override CMake install command with custom implementation using symlinks instead of copying resources\n'}
[0.818556] (tx2_60_description) StdoutLine: {'line': b'-- Override CMake install command with custom implementation using symlinks instead of copying resources\n'}
[0.868531] (pmac_hardware_interface) StdoutLine: {'line': b'-- Override CMake install command with custom implementation using symlinks instead of copying resources\n'}
[0.868959] (pmac_primitive_controller) StdoutLine: {'line': b'-- Override CMake install command with custom implementation using symlinks instead of copying resources\n'}
[0.900844] (-) TimerEvent: {}
[0.957856] (pmac_hardware_interface) StdoutLine: {'line': b'-- Found hardware_interface: 2.51.0 (/home/<USER>/code/ros2_control/install/share/hardware_interface/cmake)\n'}
[1.000953] (-) TimerEvent: {}
[1.007450] (pmac_primitive_controller) StdoutLine: {'line': b'-- Found generate_parameter_library: 0.5.0 (/opt/ros/humble/share/generate_parameter_library/cmake)\n'}
[1.007740] (send_trajectory) StdoutLine: {'line': b'-- Found kdl_parser: 2.6.4 (/opt/ros/humble/share/kdl_parser/cmake)\n'}
[1.026804] (pmac_primitive_controller) StdoutLine: {'line': b'-- Found parameter_traits: 0.5.0 (/opt/ros/humble/share/parameter_traits/cmake)\n'}
[1.050731] (tx2_60_description) StdoutLine: {'line': b'-- Configuring done (1.0s)\n'}
[1.053723] (pmac_hardware_interface) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[1.054950] (tx2_60_description) StdoutLine: {'line': b'-- Generating done (0.0s)\n'}
[1.055103] (tx2_60_description) StderrLine: {'line': b'\x1b[33mCMake Warning:\n'}
[1.055222] (tx2_60_description) StderrLine: {'line': b'  Manually-specified variables were not used by the project:\n'}
[1.055329] (tx2_60_description) StderrLine: {'line': b'\n'}
[1.055432] (tx2_60_description) StderrLine: {'line': b'    CMAKE_EXPORT_COMPILE_COMMANDS\n'}
[1.055535] (tx2_60_description) StderrLine: {'line': b'\n'}
[1.055659] (tx2_60_description) StderrLine: {'line': b'\x1b[0m\n'}
[1.055991] (tx2_60_description) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/code/pmac_robot/build/tx2_60_description\n'}
[1.060736] (tx2_60_description) CommandEnded: {'returncode': 0}
[1.062043] (tx2_60_description) JobProgress: {'identifier': 'tx2_60_description', 'progress': 'build'}
[1.062917] (tx2_60_description) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/code/pmac_robot/build/tx2_60_description', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/code/pmac_robot/build/tx2_60_description', 'env': OrderedDict([('RAW_TERMINAL_COLOR_LIGHT_CYAN', '\x1b[1;36m'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HISTFILESIZE', '2147450879'), ('XMAKE_PROGRAM_DIR', '/home/<USER>/.local/share/xmake'), ('LANGUAGE', 'en_US:en'), ('USER', 'hq'), ('TERMINAL_BG_COLOR_GREEN', '\\e[42m'), ('LC_TIME', 'en_US.UTF-8'), ('TERMINAL_COLOR_USER_INPUT_DECISION', '\\e[0;35m'), ('XDG_SESSION_TYPE', 'wayland'), ('FZF_DEFAULT_OPTS', "--height 40% --layout=reverse --preview '(highlight -O ansi {} || cat {}) 2> /dev/null | head -500'"), ('TERMINAL_COLOR_GREEN', '\\e[0;32m'), ('RAW_TERMINAL_COLOR_WHITE', '\x1b[1;37m'), ('RAW_TERMINAL_COLOR_GRAY', '\x1b[1;30m'), ('TERMINAL_BG_COLOR_LIGHT_GREEN', '\\e[1;42m'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/code/ros2_control/install/lib:/home/<USER>/code/ecat_ws/install/ethercat_manager/lib:/home/<USER>/code/ecat_ws/install/ethercat_msgs/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/lib:/home/<USER>/code/ecat_ws/install/ethercat_driver/lib:/home/<USER>/code/ecat_ws/install/ethercat_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib::/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('LESS', '-R'), ('AMENT_CPPCHECK_ALLOW_SLOW_VERSIONS', 'true'), ('HOME', '/home/<USER>'), ('XMAKE_ROOTDIR', '/home/<USER>/.local/bin'), ('ROS_WS_CACHE_SOURCED_TIME', '0'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('TERMINAL_BG_COLOR_BLUE', '\\e[44m'), ('TERMINAL_COLOR_LIGHT_GREEN', '\\e[1;32m'), ('LSCOLORS', 'Gxfxcxdxdxegedabagacad'), ('RAW_TERMINAL_COLOR_BLACK', '\x1b[0;30m'), ('RAW_TERMINAL_COLOR_LIGHT_GRAY', '\x1b[0;37m'), ('TERMINAL_BG_COLOR_RED', '\\e[41m'), ('RTI_LICENSE_FILE', '/opt/rti.com/rti_connext_dds-5.3.1/rti_license.dat'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('TERMINAL_COLOR_BLUE', '\\e[0;34m'), ('PAGER', 'less'), ('TERMINAL_COLOR_RED', '\\e[0;31m'), ('LC_MONETARY', 'en_US.UTF-8'), ('TERMINAL_BG_COLOR_CYAN', '\\e[46m'), ('MANAGERPID', '2382'), ('TERMINAL_BG_COLOR_LIGHT_BLUE', '\\e[1;44m'), ('SYSTEMD_EXEC_PID', '2933'), ('TERMINAL_BG_COLOR_LIGHT_RED', '\\e[1;41m'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINAL_COLOR_LIGHT_BLUE', '\\e[1;34m'), ('TERMINAL_COLOR_CYAN', '\\e[0;36m'), ('RAW_TERMINAL_COLOR_BROWN', '\x1b[0;33m'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '104852'), ('TERMINAL_COLOR_LIGHT_RED', '\\e[1;31m'), ('TERMINAL_BG_COLOR_LIGHT_CYAN', '\\e[1;46m'), ('RAW_TERMINAL_COLOR_PURPLE', '\x1b[0;35m'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/code/ws_moveit2/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install:/home/<USER>/code/ws_tools/install:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install'), ('ROS_DISTRO', 'humble'), ('TERMINAL_COLOR_NC', '\\e[0m'), ('ARCHFLAGS', '-arch x86_64'), ('TERMINAL_COLOR_LIGHT_CYAN', '\\e[1;36m'), ('LOGNAME', 'hq'), ('OSH', '/home/<USER>/.oh-my-bash'), ('JOURNAL_STREAM', '8:39227'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('RAW_TERMINAL_COLOR_LIGHT_PURPLE', '\x1b[1;35m'), ('RAW_TERMINAL_COLOR_YELLOW', '\x1b[1;33m'), ('TERMINAL_COLOR_USER_CONFIRMATION', '\\e[0;34m'), ('USERNAME', 'hq'), ('TERMINAL_BG_COLOR_WHITE', '\\e[1;47m'), ('TERMINAL_BG_COLOR_GRAY', '\\e[1;40m'), ('TERM', 'xterm-kitty'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models:'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('TERMINAL_COLOR_WHITE', '\\e[1;37m'), ('TERMINAL_COLOR_GRAY', '\\e[1;30m'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.fzf/bin'), ('SESSION_MANAGER', 'local/ubuntu2204:@/tmp/.ICE-unix/2600,unix/ubuntu2204:/tmp/.ICE-unix/2600'), ('INVOCATION_ID', 'd66a9f60f2014cd8b9df27ca687295ae'), ('TERMINAL_BG_COLOR_LIGHT_GRAY', '\\e[47m'), ('PAPERSIZE', 'letter'), ('TERMINAL_BG_COLOR_BLACK', '\\e[40m'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'en_US.UTF-8'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('RTW_COLOR_NOTIFY_USER', '\\e[1;33m'), ('TERMINAL_COLOR_LIGHT_GRAY', '\\e[0;37m'), ('TERMINAL_COLOR_BLACK', '\\e[0;30m'), ('DISPLAY', ':0'), ('HISTSIZE', '2147450879'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'en_US.UTF-8'), ('TERMINAL_BG_COLOR_BROWN', '\\e[43m'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.CWO0B3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '0'), ('AMENT_PREFIX_PATH', '/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description:/opt/ros/humble'), ('SHELL', '/usr/bin/bash'), ('LC_NAME', 'en_US.UTF-8'), ('XMAKE_PROGRAM_FILE', '/home/<USER>/.local/bin/xmake'), ('TERMINAL_COLOR_BROWN', '\\e[0;33m'), ('TERMINAL_BG_COLOR_PURPLE', '\\e[45m'), ('ROS_WS', '/home/<USER>/code/pmac_robot/'), ('QT_ACCESSIBILITY', '1'), ('SSH_KEY_PATH', '~/.ssh/rsa_id'), ('RAW_TERMINAL_COLOR_GREEN', '\x1b[0;32m'), ('GDMSESSION', 'ubuntu'), ('KITTY_WINDOW_ID', '1'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('TERMINAL_COLOR_PURPLE', '\\e[0;35m'), ('TERMINAL_BG_COLOR_LIGHT_PURPLE', '\\e[1;45m'), ('LC_MEASUREMENT', 'en_US.UTF-8'), ('XMAKE_SHELL', 'bash'), ('RAW_TERMINAL_COLOR_LIGHT_GREEN', '\x1b[1;32m'), ('TERMINAL_COLOR_USER_NOTICE', '\\e[1;33m'), ('TERMINAL_BG_COLOR_YELLOW', '\\e[1;43m'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'en_US.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'ibus'), ('TERMINAL_COLOR_LIGHT_PURPLE', '\\e[1;35m'), ('TERMINAL_COLOR_YELLOW', '\\e[1;33m'), ('PWD', '/home/<USER>/code/pmac_robot/build/tx2_60_description'), ('FZF_DEFAULT_COMMAND', 'ag --hidden --ignore .git -l -g ""'), ('RAW_TERMINAL_COLOR_BLUE', '\x1b[0;34m'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('RAW_TERMINAL_COLOR_RED', '\x1b[0;31m'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('RCUTILS_COLORIZED_OUTPUT', '1'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/code/ros2_control/build/rqt_joint_trajectory_controller:/home/<USER>/code/ros2_control/build/rqt_controller_manager:/home/<USER>/code/ros2_control/build/ros2controlcli:/home/<USER>/code/ros2_control/build/ros2_controllers_test_nodes:/home/<USER>/code/ros2_control/install/local/lib/python3.10/dist-packages:/home/<USER>/code/ros2_control/install/lib/python3.10/site-packages:/home/<USER>/code/ecat_ws/install/ethercat_msgs/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'en_US.UTF-8'), ('LC_PAPER', 'en_US.UTF-8'), ('RAW_TERMINAL_COLOR_CYAN', '\x1b[0;36m'), ('RAW_TERMINAL_COLOR_LIGHT_BLUE', '\x1b[1;34m'), ('RTW_COLOR_ERROR', '\\e[0;31m'), ('COLCON', '1'), ('RAW_TERMINAL_COLOR_LIGHT_RED', '\x1b[1;31m'), ('EDITOR', 'mvim'), ('CMAKE_PREFIX_PATH', '/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description'), ('RAW_TERMINAL_COLOR_NC', '\x1b[0m')]), 'shell': False}
[1.066865] (pmac_hardware_interface) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[1.077606] (send_trajectory) StdoutLine: {'line': b'-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)\n'}
[1.081227] (send_trajectory) StdoutLine: {'line': b'-- Found Eigen3: TRUE (found version "3.4.0")\n'}
[1.081482] (send_trajectory) StdoutLine: {'line': b'-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target\n'}
[1.087732] (tx2_60_description) CommandEnded: {'returncode': 0}
[1.088817] (pmac_hardware_interface) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[1.089250] (tx2_60_description) JobProgress: {'identifier': 'tx2_60_description', 'progress': 'install'}
[1.094555] (tx2_60_description) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/code/pmac_robot/build/tx2_60_description'], 'cwd': '/home/<USER>/code/pmac_robot/build/tx2_60_description', 'env': OrderedDict([('RAW_TERMINAL_COLOR_LIGHT_CYAN', '\x1b[1;36m'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HISTFILESIZE', '2147450879'), ('XMAKE_PROGRAM_DIR', '/home/<USER>/.local/share/xmake'), ('LANGUAGE', 'en_US:en'), ('USER', 'hq'), ('TERMINAL_BG_COLOR_GREEN', '\\e[42m'), ('LC_TIME', 'en_US.UTF-8'), ('TERMINAL_COLOR_USER_INPUT_DECISION', '\\e[0;35m'), ('XDG_SESSION_TYPE', 'wayland'), ('FZF_DEFAULT_OPTS', "--height 40% --layout=reverse --preview '(highlight -O ansi {} || cat {}) 2> /dev/null | head -500'"), ('TERMINAL_COLOR_GREEN', '\\e[0;32m'), ('RAW_TERMINAL_COLOR_WHITE', '\x1b[1;37m'), ('RAW_TERMINAL_COLOR_GRAY', '\x1b[1;30m'), ('TERMINAL_BG_COLOR_LIGHT_GREEN', '\\e[1;42m'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/code/ros2_control/install/lib:/home/<USER>/code/ecat_ws/install/ethercat_manager/lib:/home/<USER>/code/ecat_ws/install/ethercat_msgs/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/lib:/home/<USER>/code/ecat_ws/install/ethercat_driver/lib:/home/<USER>/code/ecat_ws/install/ethercat_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib::/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('LESS', '-R'), ('AMENT_CPPCHECK_ALLOW_SLOW_VERSIONS', 'true'), ('HOME', '/home/<USER>'), ('XMAKE_ROOTDIR', '/home/<USER>/.local/bin'), ('ROS_WS_CACHE_SOURCED_TIME', '0'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('TERMINAL_BG_COLOR_BLUE', '\\e[44m'), ('TERMINAL_COLOR_LIGHT_GREEN', '\\e[1;32m'), ('LSCOLORS', 'Gxfxcxdxdxegedabagacad'), ('RAW_TERMINAL_COLOR_BLACK', '\x1b[0;30m'), ('RAW_TERMINAL_COLOR_LIGHT_GRAY', '\x1b[0;37m'), ('TERMINAL_BG_COLOR_RED', '\\e[41m'), ('RTI_LICENSE_FILE', '/opt/rti.com/rti_connext_dds-5.3.1/rti_license.dat'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('TERMINAL_COLOR_BLUE', '\\e[0;34m'), ('PAGER', 'less'), ('TERMINAL_COLOR_RED', '\\e[0;31m'), ('LC_MONETARY', 'en_US.UTF-8'), ('TERMINAL_BG_COLOR_CYAN', '\\e[46m'), ('MANAGERPID', '2382'), ('TERMINAL_BG_COLOR_LIGHT_BLUE', '\\e[1;44m'), ('SYSTEMD_EXEC_PID', '2933'), ('TERMINAL_BG_COLOR_LIGHT_RED', '\\e[1;41m'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINAL_COLOR_LIGHT_BLUE', '\\e[1;34m'), ('TERMINAL_COLOR_CYAN', '\\e[0;36m'), ('RAW_TERMINAL_COLOR_BROWN', '\x1b[0;33m'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '104852'), ('TERMINAL_COLOR_LIGHT_RED', '\\e[1;31m'), ('TERMINAL_BG_COLOR_LIGHT_CYAN', '\\e[1;46m'), ('RAW_TERMINAL_COLOR_PURPLE', '\x1b[0;35m'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/code/ws_moveit2/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install:/home/<USER>/code/ws_tools/install:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install'), ('ROS_DISTRO', 'humble'), ('TERMINAL_COLOR_NC', '\\e[0m'), ('ARCHFLAGS', '-arch x86_64'), ('TERMINAL_COLOR_LIGHT_CYAN', '\\e[1;36m'), ('LOGNAME', 'hq'), ('OSH', '/home/<USER>/.oh-my-bash'), ('JOURNAL_STREAM', '8:39227'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('RAW_TERMINAL_COLOR_LIGHT_PURPLE', '\x1b[1;35m'), ('RAW_TERMINAL_COLOR_YELLOW', '\x1b[1;33m'), ('TERMINAL_COLOR_USER_CONFIRMATION', '\\e[0;34m'), ('USERNAME', 'hq'), ('TERMINAL_BG_COLOR_WHITE', '\\e[1;47m'), ('TERMINAL_BG_COLOR_GRAY', '\\e[1;40m'), ('TERM', 'xterm-kitty'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models:'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('TERMINAL_COLOR_WHITE', '\\e[1;37m'), ('TERMINAL_COLOR_GRAY', '\\e[1;30m'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.fzf/bin'), ('SESSION_MANAGER', 'local/ubuntu2204:@/tmp/.ICE-unix/2600,unix/ubuntu2204:/tmp/.ICE-unix/2600'), ('INVOCATION_ID', 'd66a9f60f2014cd8b9df27ca687295ae'), ('TERMINAL_BG_COLOR_LIGHT_GRAY', '\\e[47m'), ('PAPERSIZE', 'letter'), ('TERMINAL_BG_COLOR_BLACK', '\\e[40m'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'en_US.UTF-8'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('RTW_COLOR_NOTIFY_USER', '\\e[1;33m'), ('TERMINAL_COLOR_LIGHT_GRAY', '\\e[0;37m'), ('TERMINAL_COLOR_BLACK', '\\e[0;30m'), ('DISPLAY', ':0'), ('HISTSIZE', '2147450879'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'en_US.UTF-8'), ('TERMINAL_BG_COLOR_BROWN', '\\e[43m'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.CWO0B3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '0'), ('AMENT_PREFIX_PATH', '/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description:/opt/ros/humble'), ('SHELL', '/usr/bin/bash'), ('LC_NAME', 'en_US.UTF-8'), ('XMAKE_PROGRAM_FILE', '/home/<USER>/.local/bin/xmake'), ('TERMINAL_COLOR_BROWN', '\\e[0;33m'), ('TERMINAL_BG_COLOR_PURPLE', '\\e[45m'), ('ROS_WS', '/home/<USER>/code/pmac_robot/'), ('QT_ACCESSIBILITY', '1'), ('SSH_KEY_PATH', '~/.ssh/rsa_id'), ('RAW_TERMINAL_COLOR_GREEN', '\x1b[0;32m'), ('GDMSESSION', 'ubuntu'), ('KITTY_WINDOW_ID', '1'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('TERMINAL_COLOR_PURPLE', '\\e[0;35m'), ('TERMINAL_BG_COLOR_LIGHT_PURPLE', '\\e[1;45m'), ('LC_MEASUREMENT', 'en_US.UTF-8'), ('XMAKE_SHELL', 'bash'), ('RAW_TERMINAL_COLOR_LIGHT_GREEN', '\x1b[1;32m'), ('TERMINAL_COLOR_USER_NOTICE', '\\e[1;33m'), ('TERMINAL_BG_COLOR_YELLOW', '\\e[1;43m'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'en_US.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'ibus'), ('TERMINAL_COLOR_LIGHT_PURPLE', '\\e[1;35m'), ('TERMINAL_COLOR_YELLOW', '\\e[1;33m'), ('PWD', '/home/<USER>/code/pmac_robot/build/tx2_60_description'), ('FZF_DEFAULT_COMMAND', 'ag --hidden --ignore .git -l -g ""'), ('RAW_TERMINAL_COLOR_BLUE', '\x1b[0;34m'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('RAW_TERMINAL_COLOR_RED', '\x1b[0;31m'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('RCUTILS_COLORIZED_OUTPUT', '1'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/code/ros2_control/build/rqt_joint_trajectory_controller:/home/<USER>/code/ros2_control/build/rqt_controller_manager:/home/<USER>/code/ros2_control/build/ros2controlcli:/home/<USER>/code/ros2_control/build/ros2_controllers_test_nodes:/home/<USER>/code/ros2_control/install/local/lib/python3.10/dist-packages:/home/<USER>/code/ros2_control/install/lib/python3.10/site-packages:/home/<USER>/code/ecat_ws/install/ethercat_msgs/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'en_US.UTF-8'), ('LC_PAPER', 'en_US.UTF-8'), ('RAW_TERMINAL_COLOR_CYAN', '\x1b[0;36m'), ('RAW_TERMINAL_COLOR_LIGHT_BLUE', '\x1b[1;34m'), ('RTW_COLOR_ERROR', '\\e[0;31m'), ('COLCON', '1'), ('RAW_TERMINAL_COLOR_LIGHT_RED', '\x1b[1;31m'), ('EDITOR', 'mvim'), ('CMAKE_PREFIX_PATH', '/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description'), ('RAW_TERMINAL_COLOR_NC', '\x1b[0m')]), 'shell': False}
[1.099372] (tx2_60_description) StdoutLine: {'line': b'-- Install configuration: "RelWithDebInfo"\n'}
[1.099960] (tx2_60_description) StdoutLine: {'line': b'-- Execute custom install script\n'}
[1.100364] (tx2_60_description) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/config/.gitkeep\n'}
[1.101022] (-) TimerEvent: {}
[1.105718] (tx2_60_description) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/view_tx2_60.launch.py\n'}
[1.110650] (tx2_60_description) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/meshes/tx2_60/collision/.gitkeep\n'}
[1.114928] (pmac_hardware_interface) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[1.115747] (tx2_60_description) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/meshes/tx2_60/visual/.gitkeep\n'}
[1.119878] (send_trajectory) StdoutLine: {'line': b'-- Found trajectory_msgs: 4.9.0 (/opt/ros/humble/share/trajectory_msgs/cmake)\n'}
[1.120819] (tx2_60_description) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/rviz/tx2_60.rviz\n'}
[1.125852] (tx2_60_description) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/urdf/common.xacro\n'}
[1.130713] (tx2_60_description) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/urdf/tx2_60.urdf.xacro\n'}
[1.135729] (tx2_60_description) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/urdf/tx2_60/tx2_60_macro.bak.xacro\n'}
[1.140707] (tx2_60_description) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/urdf/tx2_60/tx2_60_macro.ros2_control.xacro\n'}
[1.145469] (tx2_60_description) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/urdf/tx2_60/tx2_60_macro.xacro\n'}
[1.150601] (tx2_60_description) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/package_run_dependencies/tx2_60_description\n'}
[1.151633] (pmac_hardware_interface) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[1.155702] (tx2_60_description) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/parent_prefix_path/tx2_60_description\n'}
[1.160601] (tx2_60_description) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/environment/ament_prefix_path.sh\n'}
[1.165353] (tx2_60_description) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/environment/ament_prefix_path.dsv\n'}
[1.170265] (tx2_60_description) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/environment/path.sh\n'}
[1.175229] (tx2_60_description) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/environment/path.dsv\n'}
[1.180274] (tx2_60_description) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/local_setup.bash\n'}
[1.185181] (tx2_60_description) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/local_setup.sh\n'}
[1.189902] (tx2_60_description) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/local_setup.zsh\n'}
[1.194716] (tx2_60_description) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/local_setup.dsv\n'}
[1.197113] (pmac_primitive_controller) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[1.201114] (-) TimerEvent: {}
[1.204761] (tx2_60_description) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/package.dsv\n'}
[1.211024] (tx2_60_description) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/packages/tx2_60_description\n'}
[1.215980] (tx2_60_description) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/cmake/tx2_60_descriptionConfig.cmake\n'}
[1.217033] (send_trajectory) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[1.217269] (pmac_primitive_controller) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[1.220806] (tx2_60_description) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/cmake/tx2_60_descriptionConfig-version.cmake\n'}
[1.225559] (tx2_60_description) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/tx2_60_description/package.xml\n'}
[1.231611] (tx2_60_description) CommandEnded: {'returncode': 0}
[1.236246] (send_trajectory) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[1.250397] (tx2_60_description) JobEnded: {'identifier': 'tx2_60_description', 'rc': 0}
[1.251639] (pmac_primitive_controller) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[1.270471] (send_trajectory) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[1.295614] (pmac_primitive_controller) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[1.301223] (-) TimerEvent: {}
[1.313208] (send_trajectory) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[1.354037] (pmac_primitive_controller) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[1.371047] (send_trajectory) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[1.401345] (-) TimerEvent: {}
[1.501629] (-) TimerEvent: {}
[1.548024] (send_trajectory) StdoutLine: {'line': b'-- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)\n'}
[1.557701] (pmac_primitive_controller) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[1.570378] (pmac_primitive_controller) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[1.601756] (-) TimerEvent: {}
[1.663278] (pmac_hardware_interface) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[1.670772] (pmac_hardware_interface) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[1.694520] (send_trajectory) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[1.701859] (-) TimerEvent: {}
[1.705390] (send_trajectory) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[1.725795] (pmac_hardware_interface) StdoutLine: {'line': b'-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND\n'}
[1.793548] (pmac_primitive_controller) StdoutLine: {'line': b'-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")\n'}
[1.801988] (-) TimerEvent: {}
[1.802863] (pmac_hardware_interface) StdoutLine: {'line': b'-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")\n'}
[1.807741] (pmac_hardware_interface) StdoutLine: {'line': b'-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND\n'}
[1.844283] (pmac_hardware_interface) StdoutLine: {'line': b'-- Found FastRTPS: /opt/ros/humble/include\n'}
[1.852090] (pmac_hardware_interface) StdoutLine: {'line': b'-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND\n'}
[1.861499] (pmac_primitive_controller) StdoutLine: {'line': b'-- Found FastRTPS: /opt/ros/humble/include\n'}
[1.862959] (pmac_hardware_interface) StdoutLine: {'line': b'-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND\n'}
[1.887519] (pmac_hardware_interface) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[1.902120] (-) TimerEvent: {}
[1.902794] (send_trajectory) StdoutLine: {'line': b'-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")\n'}
[1.921066] (pmac_hardware_interface) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD\n'}
[1.932918] (pmac_primitive_controller) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[1.953974] (pmac_primitive_controller) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD\n'}
[1.971857] (send_trajectory) StdoutLine: {'line': b'-- Found FastRTPS: /opt/ros/humble/include\n'}
[2.002227] (-) TimerEvent: {}
[2.035433] (pmac_primitive_controller) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success\n'}
[2.036775] (pmac_primitive_controller) StdoutLine: {'line': b'-- Found Threads: TRUE\n'}
[2.037333] (pmac_hardware_interface) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success\n'}
[2.039170] (pmac_hardware_interface) StdoutLine: {'line': b'-- Found Threads: TRUE\n'}
[2.063342] (send_trajectory) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[2.087086] (send_trajectory) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD\n'}
[2.102332] (-) TimerEvent: {}
[2.170456] (send_trajectory) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success\n'}
[2.172264] (send_trajectory) StdoutLine: {'line': b'-- Found Threads: TRUE\n'}
[2.202427] (-) TimerEvent: {}
[2.215679] (pmac_primitive_controller) StdoutLine: {'line': b'-- Found rclcpp_lifecycle: 16.0.14 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)\n'}
[2.255053] (pmac_primitive_controller) StdoutLine: {'line': b'-- Found control_msgs: 4.8.0 (/home/<USER>/code/ros2_control/install/share/control_msgs/cmake)\n'}
[2.258461] (pmac_hardware_interface) StdoutLine: {'line': b'-- Configuring done (2.2s)\n'}
[2.262250] (send_trajectory) StdoutLine: {'line': b'-- Found control_msgs: 4.8.0 (/home/<USER>/code/ros2_control/install/share/control_msgs/cmake)\n'}
[2.290031] (pmac_hardware_interface) StdoutLine: {'line': b'-- Generating done (0.0s)\n'}
[2.293206] (pmac_hardware_interface) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/code/pmac_robot/build/pmac_hardware_interface\n'}
[2.302553] (-) TimerEvent: {}
[2.309724] (pmac_hardware_interface) CommandEnded: {'returncode': 0}
[2.311150] (pmac_hardware_interface) JobProgress: {'identifier': 'pmac_hardware_interface', 'progress': 'build'}
[2.311612] (pmac_hardware_interface) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/code/pmac_robot/build/pmac_hardware_interface', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/code/pmac_robot/build/pmac_hardware_interface', 'env': OrderedDict([('RAW_TERMINAL_COLOR_LIGHT_CYAN', '\x1b[1;36m'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HISTFILESIZE', '2147450879'), ('XMAKE_PROGRAM_DIR', '/home/<USER>/.local/share/xmake'), ('LANGUAGE', 'en_US:en'), ('USER', 'hq'), ('TERMINAL_BG_COLOR_GREEN', '\\e[42m'), ('LC_TIME', 'en_US.UTF-8'), ('TERMINAL_COLOR_USER_INPUT_DECISION', '\\e[0;35m'), ('XDG_SESSION_TYPE', 'wayland'), ('FZF_DEFAULT_OPTS', "--height 40% --layout=reverse --preview '(highlight -O ansi {} || cat {}) 2> /dev/null | head -500'"), ('TERMINAL_COLOR_GREEN', '\\e[0;32m'), ('RAW_TERMINAL_COLOR_WHITE', '\x1b[1;37m'), ('RAW_TERMINAL_COLOR_GRAY', '\x1b[1;30m'), ('TERMINAL_BG_COLOR_LIGHT_GREEN', '\\e[1;42m'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/code/ros2_control/install/lib:/home/<USER>/code/ecat_ws/install/ethercat_manager/lib:/home/<USER>/code/ecat_ws/install/ethercat_msgs/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/lib:/home/<USER>/code/ecat_ws/install/ethercat_driver/lib:/home/<USER>/code/ecat_ws/install/ethercat_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib::/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('LESS', '-R'), ('AMENT_CPPCHECK_ALLOW_SLOW_VERSIONS', 'true'), ('HOME', '/home/<USER>'), ('XMAKE_ROOTDIR', '/home/<USER>/.local/bin'), ('ROS_WS_CACHE_SOURCED_TIME', '0'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('TERMINAL_BG_COLOR_BLUE', '\\e[44m'), ('TERMINAL_COLOR_LIGHT_GREEN', '\\e[1;32m'), ('LSCOLORS', 'Gxfxcxdxdxegedabagacad'), ('RAW_TERMINAL_COLOR_BLACK', '\x1b[0;30m'), ('RAW_TERMINAL_COLOR_LIGHT_GRAY', '\x1b[0;37m'), ('TERMINAL_BG_COLOR_RED', '\\e[41m'), ('RTI_LICENSE_FILE', '/opt/rti.com/rti_connext_dds-5.3.1/rti_license.dat'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('TERMINAL_COLOR_BLUE', '\\e[0;34m'), ('PAGER', 'less'), ('TERMINAL_COLOR_RED', '\\e[0;31m'), ('LC_MONETARY', 'en_US.UTF-8'), ('TERMINAL_BG_COLOR_CYAN', '\\e[46m'), ('MANAGERPID', '2382'), ('TERMINAL_BG_COLOR_LIGHT_BLUE', '\\e[1;44m'), ('SYSTEMD_EXEC_PID', '2933'), ('TERMINAL_BG_COLOR_LIGHT_RED', '\\e[1;41m'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINAL_COLOR_LIGHT_BLUE', '\\e[1;34m'), ('TERMINAL_COLOR_CYAN', '\\e[0;36m'), ('RAW_TERMINAL_COLOR_BROWN', '\x1b[0;33m'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '104852'), ('TERMINAL_COLOR_LIGHT_RED', '\\e[1;31m'), ('TERMINAL_BG_COLOR_LIGHT_CYAN', '\\e[1;46m'), ('RAW_TERMINAL_COLOR_PURPLE', '\x1b[0;35m'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/code/ws_moveit2/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install:/home/<USER>/code/ws_tools/install:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install'), ('ROS_DISTRO', 'humble'), ('TERMINAL_COLOR_NC', '\\e[0m'), ('ARCHFLAGS', '-arch x86_64'), ('TERMINAL_COLOR_LIGHT_CYAN', '\\e[1;36m'), ('LOGNAME', 'hq'), ('OSH', '/home/<USER>/.oh-my-bash'), ('JOURNAL_STREAM', '8:39227'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('RAW_TERMINAL_COLOR_LIGHT_PURPLE', '\x1b[1;35m'), ('RAW_TERMINAL_COLOR_YELLOW', '\x1b[1;33m'), ('TERMINAL_COLOR_USER_CONFIRMATION', '\\e[0;34m'), ('USERNAME', 'hq'), ('TERMINAL_BG_COLOR_WHITE', '\\e[1;47m'), ('TERMINAL_BG_COLOR_GRAY', '\\e[1;40m'), ('TERM', 'xterm-kitty'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models:'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('TERMINAL_COLOR_WHITE', '\\e[1;37m'), ('TERMINAL_COLOR_GRAY', '\\e[1;30m'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.fzf/bin'), ('SESSION_MANAGER', 'local/ubuntu2204:@/tmp/.ICE-unix/2600,unix/ubuntu2204:/tmp/.ICE-unix/2600'), ('INVOCATION_ID', 'd66a9f60f2014cd8b9df27ca687295ae'), ('TERMINAL_BG_COLOR_LIGHT_GRAY', '\\e[47m'), ('PAPERSIZE', 'letter'), ('TERMINAL_BG_COLOR_BLACK', '\\e[40m'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'en_US.UTF-8'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('RTW_COLOR_NOTIFY_USER', '\\e[1;33m'), ('TERMINAL_COLOR_LIGHT_GRAY', '\\e[0;37m'), ('TERMINAL_COLOR_BLACK', '\\e[0;30m'), ('DISPLAY', ':0'), ('HISTSIZE', '2147450879'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'en_US.UTF-8'), ('TERMINAL_BG_COLOR_BROWN', '\\e[43m'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.CWO0B3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '0'), ('AMENT_PREFIX_PATH', '/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description:/opt/ros/humble'), ('SHELL', '/usr/bin/bash'), ('LC_NAME', 'en_US.UTF-8'), ('XMAKE_PROGRAM_FILE', '/home/<USER>/.local/bin/xmake'), ('TERMINAL_COLOR_BROWN', '\\e[0;33m'), ('TERMINAL_BG_COLOR_PURPLE', '\\e[45m'), ('ROS_WS', '/home/<USER>/code/pmac_robot/'), ('QT_ACCESSIBILITY', '1'), ('SSH_KEY_PATH', '~/.ssh/rsa_id'), ('RAW_TERMINAL_COLOR_GREEN', '\x1b[0;32m'), ('GDMSESSION', 'ubuntu'), ('KITTY_WINDOW_ID', '1'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('TERMINAL_COLOR_PURPLE', '\\e[0;35m'), ('TERMINAL_BG_COLOR_LIGHT_PURPLE', '\\e[1;45m'), ('LC_MEASUREMENT', 'en_US.UTF-8'), ('XMAKE_SHELL', 'bash'), ('RAW_TERMINAL_COLOR_LIGHT_GREEN', '\x1b[1;32m'), ('TERMINAL_COLOR_USER_NOTICE', '\\e[1;33m'), ('TERMINAL_BG_COLOR_YELLOW', '\\e[1;43m'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'en_US.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'ibus'), ('TERMINAL_COLOR_LIGHT_PURPLE', '\\e[1;35m'), ('TERMINAL_COLOR_YELLOW', '\\e[1;33m'), ('PWD', '/home/<USER>/code/pmac_robot/build/pmac_hardware_interface'), ('FZF_DEFAULT_COMMAND', 'ag --hidden --ignore .git -l -g ""'), ('RAW_TERMINAL_COLOR_BLUE', '\x1b[0;34m'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('RAW_TERMINAL_COLOR_RED', '\x1b[0;31m'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('RCUTILS_COLORIZED_OUTPUT', '1'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/code/ros2_control/build/rqt_joint_trajectory_controller:/home/<USER>/code/ros2_control/build/rqt_controller_manager:/home/<USER>/code/ros2_control/build/ros2controlcli:/home/<USER>/code/ros2_control/build/ros2_controllers_test_nodes:/home/<USER>/code/ros2_control/install/local/lib/python3.10/dist-packages:/home/<USER>/code/ros2_control/install/lib/python3.10/site-packages:/home/<USER>/code/ecat_ws/install/ethercat_msgs/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'en_US.UTF-8'), ('LC_PAPER', 'en_US.UTF-8'), ('RAW_TERMINAL_COLOR_CYAN', '\x1b[0;36m'), ('RAW_TERMINAL_COLOR_LIGHT_BLUE', '\x1b[1;34m'), ('RTW_COLOR_ERROR', '\\e[0;31m'), ('COLCON', '1'), ('RAW_TERMINAL_COLOR_LIGHT_RED', '\x1b[1;31m'), ('EDITOR', 'mvim'), ('CMAKE_PREFIX_PATH', '/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description'), ('RAW_TERMINAL_COLOR_NC', '\x1b[0m')]), 'shell': False}
[2.352513] (pmac_hardware_interface) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/pmac_hardware_interface.dir/src/ck3m_primitive.cpp.o\x1b[0m\n'}
[2.402660] (-) TimerEvent: {}
[2.452894] (pmac_primitive_controller) StdoutLine: {'line': b'-- Found controller_interface: 2.51.0 (/home/<USER>/code/ros2_control/install/share/controller_interface/cmake)\n'}
[2.502761] (-) TimerEvent: {}
[2.523697] (send_trajectory) StdoutLine: {'line': b'-- Configuring done (2.5s)\n'}
[2.532650] (pmac_primitive_controller) StdoutLine: {'line': b'-- Found realtime_tools: 2.14.0 (/home/<USER>/code/ros2_control/install/share/realtime_tools/cmake)\n'}
[2.561226] (send_trajectory) StdoutLine: {'line': b'-- Generating done (0.0s)\n'}
[2.566470] (send_trajectory) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/code/pmac_robot/build/send_trajectory\n'}
[2.573134] (pmac_primitive_controller) StdoutLine: {'line': b'-- Found std_srvs: 4.9.0 (/opt/ros/humble/share/std_srvs/cmake)\n'}
[2.582916] (send_trajectory) CommandEnded: {'returncode': 0}
[2.584320] (send_trajectory) JobProgress: {'identifier': 'send_trajectory', 'progress': 'build'}
[2.585750] (send_trajectory) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/code/pmac_robot/build/send_trajectory', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/code/pmac_robot/build/send_trajectory', 'env': OrderedDict([('RAW_TERMINAL_COLOR_LIGHT_CYAN', '\x1b[1;36m'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HISTFILESIZE', '2147450879'), ('XMAKE_PROGRAM_DIR', '/home/<USER>/.local/share/xmake'), ('LANGUAGE', 'en_US:en'), ('USER', 'hq'), ('TERMINAL_BG_COLOR_GREEN', '\\e[42m'), ('LC_TIME', 'en_US.UTF-8'), ('TERMINAL_COLOR_USER_INPUT_DECISION', '\\e[0;35m'), ('XDG_SESSION_TYPE', 'wayland'), ('FZF_DEFAULT_OPTS', "--height 40% --layout=reverse --preview '(highlight -O ansi {} || cat {}) 2> /dev/null | head -500'"), ('TERMINAL_COLOR_GREEN', '\\e[0;32m'), ('RAW_TERMINAL_COLOR_WHITE', '\x1b[1;37m'), ('RAW_TERMINAL_COLOR_GRAY', '\x1b[1;30m'), ('TERMINAL_BG_COLOR_LIGHT_GREEN', '\\e[1;42m'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/code/ros2_control/install/lib:/home/<USER>/code/ecat_ws/install/ethercat_manager/lib:/home/<USER>/code/ecat_ws/install/ethercat_msgs/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/lib:/home/<USER>/code/ecat_ws/install/ethercat_driver/lib:/home/<USER>/code/ecat_ws/install/ethercat_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib::/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('LESS', '-R'), ('AMENT_CPPCHECK_ALLOW_SLOW_VERSIONS', 'true'), ('HOME', '/home/<USER>'), ('XMAKE_ROOTDIR', '/home/<USER>/.local/bin'), ('ROS_WS_CACHE_SOURCED_TIME', '0'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('TERMINAL_BG_COLOR_BLUE', '\\e[44m'), ('TERMINAL_COLOR_LIGHT_GREEN', '\\e[1;32m'), ('LSCOLORS', 'Gxfxcxdxdxegedabagacad'), ('RAW_TERMINAL_COLOR_BLACK', '\x1b[0;30m'), ('RAW_TERMINAL_COLOR_LIGHT_GRAY', '\x1b[0;37m'), ('TERMINAL_BG_COLOR_RED', '\\e[41m'), ('RTI_LICENSE_FILE', '/opt/rti.com/rti_connext_dds-5.3.1/rti_license.dat'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('TERMINAL_COLOR_BLUE', '\\e[0;34m'), ('PAGER', 'less'), ('TERMINAL_COLOR_RED', '\\e[0;31m'), ('LC_MONETARY', 'en_US.UTF-8'), ('TERMINAL_BG_COLOR_CYAN', '\\e[46m'), ('MANAGERPID', '2382'), ('TERMINAL_BG_COLOR_LIGHT_BLUE', '\\e[1;44m'), ('SYSTEMD_EXEC_PID', '2933'), ('TERMINAL_BG_COLOR_LIGHT_RED', '\\e[1;41m'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINAL_COLOR_LIGHT_BLUE', '\\e[1;34m'), ('TERMINAL_COLOR_CYAN', '\\e[0;36m'), ('RAW_TERMINAL_COLOR_BROWN', '\x1b[0;33m'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '104852'), ('TERMINAL_COLOR_LIGHT_RED', '\\e[1;31m'), ('TERMINAL_BG_COLOR_LIGHT_CYAN', '\\e[1;46m'), ('RAW_TERMINAL_COLOR_PURPLE', '\x1b[0;35m'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/code/ws_moveit2/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install:/home/<USER>/code/ws_tools/install:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install'), ('ROS_DISTRO', 'humble'), ('TERMINAL_COLOR_NC', '\\e[0m'), ('ARCHFLAGS', '-arch x86_64'), ('TERMINAL_COLOR_LIGHT_CYAN', '\\e[1;36m'), ('LOGNAME', 'hq'), ('OSH', '/home/<USER>/.oh-my-bash'), ('JOURNAL_STREAM', '8:39227'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('RAW_TERMINAL_COLOR_LIGHT_PURPLE', '\x1b[1;35m'), ('RAW_TERMINAL_COLOR_YELLOW', '\x1b[1;33m'), ('TERMINAL_COLOR_USER_CONFIRMATION', '\\e[0;34m'), ('USERNAME', 'hq'), ('TERMINAL_BG_COLOR_WHITE', '\\e[1;47m'), ('TERMINAL_BG_COLOR_GRAY', '\\e[1;40m'), ('TERM', 'xterm-kitty'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models:'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('TERMINAL_COLOR_WHITE', '\\e[1;37m'), ('TERMINAL_COLOR_GRAY', '\\e[1;30m'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.fzf/bin'), ('SESSION_MANAGER', 'local/ubuntu2204:@/tmp/.ICE-unix/2600,unix/ubuntu2204:/tmp/.ICE-unix/2600'), ('INVOCATION_ID', 'd66a9f60f2014cd8b9df27ca687295ae'), ('TERMINAL_BG_COLOR_LIGHT_GRAY', '\\e[47m'), ('PAPERSIZE', 'letter'), ('TERMINAL_BG_COLOR_BLACK', '\\e[40m'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'en_US.UTF-8'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('RTW_COLOR_NOTIFY_USER', '\\e[1;33m'), ('TERMINAL_COLOR_LIGHT_GRAY', '\\e[0;37m'), ('TERMINAL_COLOR_BLACK', '\\e[0;30m'), ('DISPLAY', ':0'), ('HISTSIZE', '2147450879'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'en_US.UTF-8'), ('TERMINAL_BG_COLOR_BROWN', '\\e[43m'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.CWO0B3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '0'), ('AMENT_PREFIX_PATH', '/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description:/opt/ros/humble'), ('SHELL', '/usr/bin/bash'), ('LC_NAME', 'en_US.UTF-8'), ('XMAKE_PROGRAM_FILE', '/home/<USER>/.local/bin/xmake'), ('TERMINAL_COLOR_BROWN', '\\e[0;33m'), ('TERMINAL_BG_COLOR_PURPLE', '\\e[45m'), ('ROS_WS', '/home/<USER>/code/pmac_robot/'), ('QT_ACCESSIBILITY', '1'), ('SSH_KEY_PATH', '~/.ssh/rsa_id'), ('RAW_TERMINAL_COLOR_GREEN', '\x1b[0;32m'), ('GDMSESSION', 'ubuntu'), ('KITTY_WINDOW_ID', '1'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('TERMINAL_COLOR_PURPLE', '\\e[0;35m'), ('TERMINAL_BG_COLOR_LIGHT_PURPLE', '\\e[1;45m'), ('LC_MEASUREMENT', 'en_US.UTF-8'), ('XMAKE_SHELL', 'bash'), ('RAW_TERMINAL_COLOR_LIGHT_GREEN', '\x1b[1;32m'), ('TERMINAL_COLOR_USER_NOTICE', '\\e[1;33m'), ('TERMINAL_BG_COLOR_YELLOW', '\\e[1;43m'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'en_US.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'ibus'), ('TERMINAL_COLOR_LIGHT_PURPLE', '\\e[1;35m'), ('TERMINAL_COLOR_YELLOW', '\\e[1;33m'), ('PWD', '/home/<USER>/code/pmac_robot/build/send_trajectory'), ('FZF_DEFAULT_COMMAND', 'ag --hidden --ignore .git -l -g ""'), ('RAW_TERMINAL_COLOR_BLUE', '\x1b[0;34m'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('RAW_TERMINAL_COLOR_RED', '\x1b[0;31m'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('RCUTILS_COLORIZED_OUTPUT', '1'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/code/ros2_control/build/rqt_joint_trajectory_controller:/home/<USER>/code/ros2_control/build/rqt_controller_manager:/home/<USER>/code/ros2_control/build/ros2controlcli:/home/<USER>/code/ros2_control/build/ros2_controllers_test_nodes:/home/<USER>/code/ros2_control/install/local/lib/python3.10/dist-packages:/home/<USER>/code/ros2_control/install/lib/python3.10/site-packages:/home/<USER>/code/ecat_ws/install/ethercat_msgs/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'en_US.UTF-8'), ('LC_PAPER', 'en_US.UTF-8'), ('RAW_TERMINAL_COLOR_CYAN', '\x1b[0;36m'), ('RAW_TERMINAL_COLOR_LIGHT_BLUE', '\x1b[1;34m'), ('RTW_COLOR_ERROR', '\\e[0;31m'), ('COLCON', '1'), ('RAW_TERMINAL_COLOR_LIGHT_RED', '\x1b[1;31m'), ('EDITOR', 'mvim'), ('CMAKE_PREFIX_PATH', '/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description'), ('RAW_TERMINAL_COLOR_NC', '\x1b[0m')]), 'shell': False}
[2.602863] (-) TimerEvent: {}
[2.624277] (send_trajectory) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/send_trajectory.dir/src/send_trajctory.cpp.o\x1b[0m\n'}
[2.702994] (-) TimerEvent: {}
[2.716371] (pmac_primitive_controller) StdoutLine: {'line': b'-- Configuring done (2.7s)\n'}
[2.740492] (pmac_primitive_controller) StdoutLine: {'line': b'-- Generating done (0.0s)\n'}
[2.744054] (pmac_primitive_controller) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/code/pmac_robot/build/pmac_primitive_controller\n'}
[2.757619] (pmac_primitive_controller) CommandEnded: {'returncode': 0}
[2.759159] (pmac_primitive_controller) JobProgress: {'identifier': 'pmac_primitive_controller', 'progress': 'build'}
[2.759666] (pmac_primitive_controller) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/code/pmac_robot/build/pmac_primitive_controller', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/code/pmac_robot/build/pmac_primitive_controller', 'env': OrderedDict([('RAW_TERMINAL_COLOR_LIGHT_CYAN', '\x1b[1;36m'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HISTFILESIZE', '2147450879'), ('XMAKE_PROGRAM_DIR', '/home/<USER>/.local/share/xmake'), ('LANGUAGE', 'en_US:en'), ('USER', 'hq'), ('TERMINAL_BG_COLOR_GREEN', '\\e[42m'), ('LC_TIME', 'en_US.UTF-8'), ('TERMINAL_COLOR_USER_INPUT_DECISION', '\\e[0;35m'), ('XDG_SESSION_TYPE', 'wayland'), ('FZF_DEFAULT_OPTS', "--height 40% --layout=reverse --preview '(highlight -O ansi {} || cat {}) 2> /dev/null | head -500'"), ('TERMINAL_COLOR_GREEN', '\\e[0;32m'), ('RAW_TERMINAL_COLOR_WHITE', '\x1b[1;37m'), ('RAW_TERMINAL_COLOR_GRAY', '\x1b[1;30m'), ('TERMINAL_BG_COLOR_LIGHT_GREEN', '\\e[1;42m'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/code/ros2_control/install/lib:/home/<USER>/code/ecat_ws/install/ethercat_manager/lib:/home/<USER>/code/ecat_ws/install/ethercat_msgs/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/lib:/home/<USER>/code/ecat_ws/install/ethercat_driver/lib:/home/<USER>/code/ecat_ws/install/ethercat_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib::/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('LESS', '-R'), ('AMENT_CPPCHECK_ALLOW_SLOW_VERSIONS', 'true'), ('HOME', '/home/<USER>'), ('XMAKE_ROOTDIR', '/home/<USER>/.local/bin'), ('ROS_WS_CACHE_SOURCED_TIME', '0'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('TERMINAL_BG_COLOR_BLUE', '\\e[44m'), ('TERMINAL_COLOR_LIGHT_GREEN', '\\e[1;32m'), ('LSCOLORS', 'Gxfxcxdxdxegedabagacad'), ('RAW_TERMINAL_COLOR_BLACK', '\x1b[0;30m'), ('RAW_TERMINAL_COLOR_LIGHT_GRAY', '\x1b[0;37m'), ('TERMINAL_BG_COLOR_RED', '\\e[41m'), ('RTI_LICENSE_FILE', '/opt/rti.com/rti_connext_dds-5.3.1/rti_license.dat'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('TERMINAL_COLOR_BLUE', '\\e[0;34m'), ('PAGER', 'less'), ('TERMINAL_COLOR_RED', '\\e[0;31m'), ('LC_MONETARY', 'en_US.UTF-8'), ('TERMINAL_BG_COLOR_CYAN', '\\e[46m'), ('MANAGERPID', '2382'), ('TERMINAL_BG_COLOR_LIGHT_BLUE', '\\e[1;44m'), ('SYSTEMD_EXEC_PID', '2933'), ('TERMINAL_BG_COLOR_LIGHT_RED', '\\e[1;41m'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINAL_COLOR_LIGHT_BLUE', '\\e[1;34m'), ('TERMINAL_COLOR_CYAN', '\\e[0;36m'), ('RAW_TERMINAL_COLOR_BROWN', '\x1b[0;33m'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '104852'), ('TERMINAL_COLOR_LIGHT_RED', '\\e[1;31m'), ('TERMINAL_BG_COLOR_LIGHT_CYAN', '\\e[1;46m'), ('RAW_TERMINAL_COLOR_PURPLE', '\x1b[0;35m'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/code/ws_moveit2/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install:/home/<USER>/code/ws_tools/install:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install'), ('ROS_DISTRO', 'humble'), ('TERMINAL_COLOR_NC', '\\e[0m'), ('ARCHFLAGS', '-arch x86_64'), ('TERMINAL_COLOR_LIGHT_CYAN', '\\e[1;36m'), ('LOGNAME', 'hq'), ('OSH', '/home/<USER>/.oh-my-bash'), ('JOURNAL_STREAM', '8:39227'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('RAW_TERMINAL_COLOR_LIGHT_PURPLE', '\x1b[1;35m'), ('RAW_TERMINAL_COLOR_YELLOW', '\x1b[1;33m'), ('TERMINAL_COLOR_USER_CONFIRMATION', '\\e[0;34m'), ('USERNAME', 'hq'), ('TERMINAL_BG_COLOR_WHITE', '\\e[1;47m'), ('TERMINAL_BG_COLOR_GRAY', '\\e[1;40m'), ('TERM', 'xterm-kitty'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models:'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('TERMINAL_COLOR_WHITE', '\\e[1;37m'), ('TERMINAL_COLOR_GRAY', '\\e[1;30m'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.fzf/bin'), ('SESSION_MANAGER', 'local/ubuntu2204:@/tmp/.ICE-unix/2600,unix/ubuntu2204:/tmp/.ICE-unix/2600'), ('INVOCATION_ID', 'd66a9f60f2014cd8b9df27ca687295ae'), ('TERMINAL_BG_COLOR_LIGHT_GRAY', '\\e[47m'), ('PAPERSIZE', 'letter'), ('TERMINAL_BG_COLOR_BLACK', '\\e[40m'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'en_US.UTF-8'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('RTW_COLOR_NOTIFY_USER', '\\e[1;33m'), ('TERMINAL_COLOR_LIGHT_GRAY', '\\e[0;37m'), ('TERMINAL_COLOR_BLACK', '\\e[0;30m'), ('DISPLAY', ':0'), ('HISTSIZE', '2147450879'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'en_US.UTF-8'), ('TERMINAL_BG_COLOR_BROWN', '\\e[43m'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.CWO0B3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '0'), ('AMENT_PREFIX_PATH', '/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description:/opt/ros/humble'), ('SHELL', '/usr/bin/bash'), ('LC_NAME', 'en_US.UTF-8'), ('XMAKE_PROGRAM_FILE', '/home/<USER>/.local/bin/xmake'), ('TERMINAL_COLOR_BROWN', '\\e[0;33m'), ('TERMINAL_BG_COLOR_PURPLE', '\\e[45m'), ('ROS_WS', '/home/<USER>/code/pmac_robot/'), ('QT_ACCESSIBILITY', '1'), ('SSH_KEY_PATH', '~/.ssh/rsa_id'), ('RAW_TERMINAL_COLOR_GREEN', '\x1b[0;32m'), ('GDMSESSION', 'ubuntu'), ('KITTY_WINDOW_ID', '1'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('TERMINAL_COLOR_PURPLE', '\\e[0;35m'), ('TERMINAL_BG_COLOR_LIGHT_PURPLE', '\\e[1;45m'), ('LC_MEASUREMENT', 'en_US.UTF-8'), ('XMAKE_SHELL', 'bash'), ('RAW_TERMINAL_COLOR_LIGHT_GREEN', '\x1b[1;32m'), ('TERMINAL_COLOR_USER_NOTICE', '\\e[1;33m'), ('TERMINAL_BG_COLOR_YELLOW', '\\e[1;43m'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'en_US.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'ibus'), ('TERMINAL_COLOR_LIGHT_PURPLE', '\\e[1;35m'), ('TERMINAL_COLOR_YELLOW', '\\e[1;33m'), ('PWD', '/home/<USER>/code/pmac_robot/build/pmac_primitive_controller'), ('FZF_DEFAULT_COMMAND', 'ag --hidden --ignore .git -l -g ""'), ('RAW_TERMINAL_COLOR_BLUE', '\x1b[0;34m'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('RAW_TERMINAL_COLOR_RED', '\x1b[0;31m'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('RCUTILS_COLORIZED_OUTPUT', '1'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/code/ros2_control/build/rqt_joint_trajectory_controller:/home/<USER>/code/ros2_control/build/rqt_controller_manager:/home/<USER>/code/ros2_control/build/ros2controlcli:/home/<USER>/code/ros2_control/build/ros2_controllers_test_nodes:/home/<USER>/code/ros2_control/install/local/lib/python3.10/dist-packages:/home/<USER>/code/ros2_control/install/lib/python3.10/site-packages:/home/<USER>/code/ecat_ws/install/ethercat_msgs/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'en_US.UTF-8'), ('LC_PAPER', 'en_US.UTF-8'), ('RAW_TERMINAL_COLOR_CYAN', '\x1b[0;36m'), ('RAW_TERMINAL_COLOR_LIGHT_BLUE', '\x1b[1;34m'), ('RTW_COLOR_ERROR', '\\e[0;31m'), ('COLCON', '1'), ('RAW_TERMINAL_COLOR_LIGHT_RED', '\x1b[1;31m'), ('EDITOR', 'mvim'), ('CMAKE_PREFIX_PATH', '/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description'), ('RAW_TERMINAL_COLOR_NC', '\x1b[0m')]), 'shell': False}
[2.801470] (pmac_primitive_controller) StdoutLine: {'line': b'[ 25%] \x1b[1m\x1b[34mRunning `/opt/ros/humble/bin/generate_parameter_library_cpp /home/<USER>/code/pmac_robot/build/pmac_primitive_controller/include/pmac_primitive_controller/pmac_primitive_controller_parameters.hpp /home/<USER>/code/pmac_robot/src/pmac_primitive_controller/src/pmac_primitive_controller.yaml validate_pmac_primitive_controller_parameters.hpp`\x1b[0m\n'}
[2.803105] (-) TimerEvent: {}
[2.903372] (-) TimerEvent: {}
[3.003674] (-) TimerEvent: {}
[3.060995] (pmac_primitive_controller) StdoutLine: {'line': b'[ 50%] \x1b[1m\x1b[34mCreating deprecated header file /home/<USER>/code/pmac_robot/build/pmac_primitive_controller/include/pmac_primitive_controller_parameters.hpp\x1b[0m\n'}
[3.072558] (pmac_primitive_controller) StdoutLine: {'line': b'[ 50%] Built target pmac_primitive_controller_parameters\n'}
[3.088338] (pmac_primitive_controller) StdoutLine: {'line': b'[ 75%] \x1b[32mBuilding CXX object CMakeFiles/pmac_primitive_controller.dir/src/pmac_primitive_controller.cpp.o\x1b[0m\n'}
[3.103805] (-) TimerEvent: {}
[3.204075] (-) TimerEvent: {}
[3.304509] (-) TimerEvent: {}
[3.404821] (-) TimerEvent: {}
[3.505149] (-) TimerEvent: {}
[3.605469] (-) TimerEvent: {}
[3.705776] (-) TimerEvent: {}
[3.806075] (-) TimerEvent: {}
[3.906391] (-) TimerEvent: {}
[4.006741] (-) TimerEvent: {}
[4.107092] (-) TimerEvent: {}
[4.207496] (-) TimerEvent: {}
[4.307840] (-) TimerEvent: {}
[4.408200] (-) TimerEvent: {}
[4.508494] (-) TimerEvent: {}
[4.608869] (-) TimerEvent: {}
[4.709259] (-) TimerEvent: {}
[4.809622] (-) TimerEvent: {}
[4.909921] (-) TimerEvent: {}
[5.010236] (-) TimerEvent: {}
[5.110568] (-) TimerEvent: {}
[5.210977] (-) TimerEvent: {}
[5.311310] (-) TimerEvent: {}
[5.411658] (-) TimerEvent: {}
[5.511979] (-) TimerEvent: {}
[5.612310] (-) TimerEvent: {}
[5.712633] (-) TimerEvent: {}
[5.812924] (-) TimerEvent: {}
[5.913255] (-) TimerEvent: {}
[6.013566] (-) TimerEvent: {}
[6.113861] (-) TimerEvent: {}
[6.214188] (-) TimerEvent: {}
[6.314533] (-) TimerEvent: {}
[6.337015] (pmac_primitive_controller) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/code/pmac_robot/src/pmac_primitive_controller/include/pmac_primitive_controller/pmac_primitive_controller.hpp:23\x1b[m\x1b[K,\n'}
[6.337174] (pmac_primitive_controller) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/code/pmac_robot/src/pmac_primitive_controller/src/pmac_primitive_controller.cpp:15\x1b[m\x1b[K:\n'}
[6.337247] (pmac_primitive_controller) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/code/pmac_robot/build/pmac_primitive_controller/include/pmac_primitive_controller_parameters.hpp:1:178:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[K#pragma message: #include "pmac_primitive_controller_parameters.hpp" is deprecated. Use #include <pmac_primitive_controller/pmac_primitive_controller_parameters.hpp> instead.\x1b[m\x1b[K\xe2\x80\x99\n'}
[6.337334] (pmac_primitive_controller) StderrLine: {'line': b'    1 | #pragma message("#include \\"pmac_primitive_controller_parameters.hpp\\" is deprecated. Use #include <pmac_primitive_controller/pmac_primitive_controller_parameters.hpp> instead."\x1b[01;36m\x1b[K)\x1b[m\x1b[K\n'}
[6.337403] (pmac_primitive_controller) StderrLine: {'line': b'      |                                                                                                                                                                                  \x1b[01;36m\x1b[K^\x1b[m\x1b[K\n'}
[6.414699] (-) TimerEvent: {}
[6.508764] (pmac_primitive_controller) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/code/pmac_robot/build/pmac_primitive_controller/include/pmac_primitive_controller_parameters.hpp:31\x1b[m\x1b[K,\n'}
[6.508920] (pmac_primitive_controller) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/code/pmac_robot/src/pmac_primitive_controller/include/pmac_primitive_controller/pmac_primitive_controller.hpp:23\x1b[m\x1b[K,\n'}
[6.508994] (pmac_primitive_controller) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/code/pmac_robot/src/pmac_primitive_controller/src/pmac_primitive_controller.cpp:15\x1b[m\x1b[K:\n'}
[6.509062] (pmac_primitive_controller) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/code/pmac_robot/build/pmac_primitive_controller/include/validate_pmac_primitive_controller_parameters.hpp:25:75:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kusing Result = class tl::expected<void, std::__cxx11::basic_string<char> >\x1b[m\x1b[K\xe2\x80\x99 is deprecated: Use tl::expected<void, std::string> for return instead. `#include <tl_expected/expected.hpp>`. [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wdeprecated-declarations\x07-Wdeprecated-declarations\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[6.509134] (pmac_primitive_controller) StderrLine: {'line': b'   25 | Result forbidden_interface_name_prefix(rclcpp::Parameter const & parameter\x1b[01;35m\x1b[K)\x1b[m\x1b[K\n'}
[6.509216] (pmac_primitive_controller) StderrLine: {'line': b'      |                                                                           \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[6.509280] (pmac_primitive_controller) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/code/pmac_robot/build/pmac_primitive_controller/include/pmac_primitive_controller_parameters.hpp:25\x1b[m\x1b[K,\n'}
[6.509343] (pmac_primitive_controller) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/code/pmac_robot/src/pmac_primitive_controller/include/pmac_primitive_controller/pmac_primitive_controller.hpp:23\x1b[m\x1b[K,\n'}
[6.509406] (pmac_primitive_controller) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/code/pmac_robot/src/pmac_primitive_controller/src/pmac_primitive_controller.cpp:15\x1b[m\x1b[K:\n'}
[6.509482] (pmac_primitive_controller) StderrLine: {'line': b'\x1b[01m\x1b[K/opt/ros/humble/include/parameter_traits/parameter_traits/parameter_traits.hpp:37:7:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[Kdeclared here\n'}
[6.509544] (pmac_primitive_controller) StderrLine: {'line': b'   37 | using \x1b[01;36m\x1b[KResult\x1b[m\x1b[K\n'}
[6.509639] (pmac_primitive_controller) StderrLine: {'line': b'      |       \x1b[01;36m\x1b[K^~~~~~\x1b[m\x1b[K\n'}
[6.514827] (-) TimerEvent: {}
[6.532945] (pmac_primitive_controller) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/code/pmac_robot/src/pmac_primitive_controller/include/pmac_primitive_controller/pmac_primitive_controller.hpp:27\x1b[m\x1b[K,\n'}
[6.533118] (pmac_primitive_controller) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/code/pmac_robot/src/pmac_primitive_controller/src/pmac_primitive_controller.cpp:15\x1b[m\x1b[K:\n'}
[6.533253] (pmac_primitive_controller) StderrLine: {'line': b"\x1b[01m\x1b[K/home/<USER>/code/ros2_control/install/include/realtime_tools/realtime_tools/realtime_buffer.h:22:100:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[K#pragma message: This header include is deprecated. Please update your code to use 'realtime_buffer.hpp' header.\x1b[m\x1b[K\xe2\x80\x99\n"}
[6.533404] (pmac_primitive_controller) StderrLine: {'line': b'   22 |   "This header include is deprecated. Please update your code to use \'realtime_buffer.hpp\' header."\x1b[01;36m\x1b[K)\x1b[m\x1b[K  //NOLINT\n'}
[6.533517] (pmac_primitive_controller) StderrLine: {'line': b'      |                                                                                                    \x1b[01;36m\x1b[K^\x1b[m\x1b[K\n'}
[6.534484] (pmac_primitive_controller) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/code/pmac_robot/src/pmac_primitive_controller/include/pmac_primitive_controller/pmac_primitive_controller.hpp:28\x1b[m\x1b[K,\n'}
[6.534652] (pmac_primitive_controller) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/code/pmac_robot/src/pmac_primitive_controller/src/pmac_primitive_controller.cpp:15\x1b[m\x1b[K:\n'}
[6.534730] (pmac_primitive_controller) StderrLine: {'line': b"\x1b[01m\x1b[K/home/<USER>/code/ros2_control/install/include/realtime_tools/realtime_tools/realtime_publisher.h:22:103:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[K#pragma message: This header include is deprecated. Please update your code to use 'realtime_publisher.hpp' header.\x1b[m\x1b[K\xe2\x80\x99\n"}
[6.534803] (pmac_primitive_controller) StderrLine: {'line': b'   22 |   "This header include is deprecated. Please update your code to use \'realtime_publisher.hpp\' header."\x1b[01;36m\x1b[K)\x1b[m\x1b[K  //NOLINT\n'}
[6.534883] (pmac_primitive_controller) StderrLine: {'line': b'      |                                                                                                       \x1b[01;36m\x1b[K^\x1b[m\x1b[K\n'}
[6.614988] (-) TimerEvent: {}
[6.715290] (-) TimerEvent: {}
[6.815640] (-) TimerEvent: {}
[6.915964] (-) TimerEvent: {}
[7.016277] (-) TimerEvent: {}
[7.116665] (-) TimerEvent: {}
[7.217005] (-) TimerEvent: {}
[7.317330] (-) TimerEvent: {}
[7.417668] (-) TimerEvent: {}
[7.460938] (send_trajectory) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/code/pmac_robot/src/send_trajectory/src/send_trajctory.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kint main(int, char**)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[7.461098] (send_trajectory) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/code/pmac_robot/src/send_trajectory/src/send_trajctory.cpp:40:14:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kvariable \xe2\x80\x98\x1b[01m\x1b[Ktwist\x1b[m\x1b[K\xe2\x80\x99 set but not used [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable\x07-Wunused-but-set-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[7.461174] (send_trajectory) StderrLine: {'line': b'   40 |         auto \x1b[01;35m\x1b[Ktwist\x1b[m\x1b[K = KDL::Twist();\n'}
[7.461262] (send_trajectory) StderrLine: {'line': b'      |              \x1b[01;35m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[7.461326] (send_trajectory) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/code/pmac_robot/src/send_trajectory/src/send_trajctory.cpp:60:16:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kdt\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[7.461394] (send_trajectory) StderrLine: {'line': b'   60 |         double \x1b[01;35m\x1b[Kdt\x1b[m\x1b[K = 1. / loop_rate;\n'}
[7.461456] (send_trajectory) StderrLine: {'line': b'      |                \x1b[01;35m\x1b[K^~\x1b[m\x1b[K\n'}
[7.517798] (-) TimerEvent: {}
[7.618150] (-) TimerEvent: {}
[7.718480] (-) TimerEvent: {}
[7.818797] (-) TimerEvent: {}
[7.919120] (-) TimerEvent: {}
[8.019460] (-) TimerEvent: {}
[8.119775] (-) TimerEvent: {}
[8.220033] (-) TimerEvent: {}
[8.320370] (-) TimerEvent: {}
[8.420681] (-) TimerEvent: {}
[8.521010] (-) TimerEvent: {}
[8.621278] (-) TimerEvent: {}
[8.721619] (-) TimerEvent: {}
[8.821946] (-) TimerEvent: {}
[8.922282] (-) TimerEvent: {}
[9.022625] (-) TimerEvent: {}
[9.122923] (-) TimerEvent: {}
[9.223253] (-) TimerEvent: {}
[9.323596] (-) TimerEvent: {}
[9.423918] (-) TimerEvent: {}
[9.524242] (-) TimerEvent: {}
[9.624513] (-) TimerEvent: {}
[9.724843] (-) TimerEvent: {}
[9.825180] (-) TimerEvent: {}
[9.925520] (-) TimerEvent: {}
[10.025840] (-) TimerEvent: {}
[10.126144] (-) TimerEvent: {}
[10.226440] (-) TimerEvent: {}
[10.326751] (-) TimerEvent: {}
[10.427045] (-) TimerEvent: {}
[10.527342] (-) TimerEvent: {}
[10.627619] (-) TimerEvent: {}
[10.727970] (-) TimerEvent: {}
[10.775080] (pmac_hardware_interface) StdoutLine: {'line': b'[100%] \x1b[1m\x1b[32mLinking CXX shared library libpmac_hardware_interface.so\x1b[0m\n'}
[10.828104] (-) TimerEvent: {}
[10.928506] (-) TimerEvent: {}
[10.933420] (pmac_hardware_interface) StdoutLine: {'line': b'[100%] Built target pmac_hardware_interface\n'}
[11.000106] (pmac_hardware_interface) CommandEnded: {'returncode': 0}
[11.001161] (pmac_hardware_interface) JobProgress: {'identifier': 'pmac_hardware_interface', 'progress': 'install'}
[11.001602] (pmac_hardware_interface) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/code/pmac_robot/build/pmac_hardware_interface'], 'cwd': '/home/<USER>/code/pmac_robot/build/pmac_hardware_interface', 'env': OrderedDict([('RAW_TERMINAL_COLOR_LIGHT_CYAN', '\x1b[1;36m'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HISTFILESIZE', '2147450879'), ('XMAKE_PROGRAM_DIR', '/home/<USER>/.local/share/xmake'), ('LANGUAGE', 'en_US:en'), ('USER', 'hq'), ('TERMINAL_BG_COLOR_GREEN', '\\e[42m'), ('LC_TIME', 'en_US.UTF-8'), ('TERMINAL_COLOR_USER_INPUT_DECISION', '\\e[0;35m'), ('XDG_SESSION_TYPE', 'wayland'), ('FZF_DEFAULT_OPTS', "--height 40% --layout=reverse --preview '(highlight -O ansi {} || cat {}) 2> /dev/null | head -500'"), ('TERMINAL_COLOR_GREEN', '\\e[0;32m'), ('RAW_TERMINAL_COLOR_WHITE', '\x1b[1;37m'), ('RAW_TERMINAL_COLOR_GRAY', '\x1b[1;30m'), ('TERMINAL_BG_COLOR_LIGHT_GREEN', '\\e[1;42m'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/code/ros2_control/install/lib:/home/<USER>/code/ecat_ws/install/ethercat_manager/lib:/home/<USER>/code/ecat_ws/install/ethercat_msgs/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/lib:/home/<USER>/code/ecat_ws/install/ethercat_driver/lib:/home/<USER>/code/ecat_ws/install/ethercat_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib::/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('LESS', '-R'), ('AMENT_CPPCHECK_ALLOW_SLOW_VERSIONS', 'true'), ('HOME', '/home/<USER>'), ('XMAKE_ROOTDIR', '/home/<USER>/.local/bin'), ('ROS_WS_CACHE_SOURCED_TIME', '0'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('TERMINAL_BG_COLOR_BLUE', '\\e[44m'), ('TERMINAL_COLOR_LIGHT_GREEN', '\\e[1;32m'), ('LSCOLORS', 'Gxfxcxdxdxegedabagacad'), ('RAW_TERMINAL_COLOR_BLACK', '\x1b[0;30m'), ('RAW_TERMINAL_COLOR_LIGHT_GRAY', '\x1b[0;37m'), ('TERMINAL_BG_COLOR_RED', '\\e[41m'), ('RTI_LICENSE_FILE', '/opt/rti.com/rti_connext_dds-5.3.1/rti_license.dat'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('TERMINAL_COLOR_BLUE', '\\e[0;34m'), ('PAGER', 'less'), ('TERMINAL_COLOR_RED', '\\e[0;31m'), ('LC_MONETARY', 'en_US.UTF-8'), ('TERMINAL_BG_COLOR_CYAN', '\\e[46m'), ('MANAGERPID', '2382'), ('TERMINAL_BG_COLOR_LIGHT_BLUE', '\\e[1;44m'), ('SYSTEMD_EXEC_PID', '2933'), ('TERMINAL_BG_COLOR_LIGHT_RED', '\\e[1;41m'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINAL_COLOR_LIGHT_BLUE', '\\e[1;34m'), ('TERMINAL_COLOR_CYAN', '\\e[0;36m'), ('RAW_TERMINAL_COLOR_BROWN', '\x1b[0;33m'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '104852'), ('TERMINAL_COLOR_LIGHT_RED', '\\e[1;31m'), ('TERMINAL_BG_COLOR_LIGHT_CYAN', '\\e[1;46m'), ('RAW_TERMINAL_COLOR_PURPLE', '\x1b[0;35m'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/code/ws_moveit2/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install:/home/<USER>/code/ws_tools/install:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install'), ('ROS_DISTRO', 'humble'), ('TERMINAL_COLOR_NC', '\\e[0m'), ('ARCHFLAGS', '-arch x86_64'), ('TERMINAL_COLOR_LIGHT_CYAN', '\\e[1;36m'), ('LOGNAME', 'hq'), ('OSH', '/home/<USER>/.oh-my-bash'), ('JOURNAL_STREAM', '8:39227'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('RAW_TERMINAL_COLOR_LIGHT_PURPLE', '\x1b[1;35m'), ('RAW_TERMINAL_COLOR_YELLOW', '\x1b[1;33m'), ('TERMINAL_COLOR_USER_CONFIRMATION', '\\e[0;34m'), ('USERNAME', 'hq'), ('TERMINAL_BG_COLOR_WHITE', '\\e[1;47m'), ('TERMINAL_BG_COLOR_GRAY', '\\e[1;40m'), ('TERM', 'xterm-kitty'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models:'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('TERMINAL_COLOR_WHITE', '\\e[1;37m'), ('TERMINAL_COLOR_GRAY', '\\e[1;30m'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.fzf/bin'), ('SESSION_MANAGER', 'local/ubuntu2204:@/tmp/.ICE-unix/2600,unix/ubuntu2204:/tmp/.ICE-unix/2600'), ('INVOCATION_ID', 'd66a9f60f2014cd8b9df27ca687295ae'), ('TERMINAL_BG_COLOR_LIGHT_GRAY', '\\e[47m'), ('PAPERSIZE', 'letter'), ('TERMINAL_BG_COLOR_BLACK', '\\e[40m'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'en_US.UTF-8'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('RTW_COLOR_NOTIFY_USER', '\\e[1;33m'), ('TERMINAL_COLOR_LIGHT_GRAY', '\\e[0;37m'), ('TERMINAL_COLOR_BLACK', '\\e[0;30m'), ('DISPLAY', ':0'), ('HISTSIZE', '2147450879'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'en_US.UTF-8'), ('TERMINAL_BG_COLOR_BROWN', '\\e[43m'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.CWO0B3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '0'), ('AMENT_PREFIX_PATH', '/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description:/opt/ros/humble'), ('SHELL', '/usr/bin/bash'), ('LC_NAME', 'en_US.UTF-8'), ('XMAKE_PROGRAM_FILE', '/home/<USER>/.local/bin/xmake'), ('TERMINAL_COLOR_BROWN', '\\e[0;33m'), ('TERMINAL_BG_COLOR_PURPLE', '\\e[45m'), ('ROS_WS', '/home/<USER>/code/pmac_robot/'), ('QT_ACCESSIBILITY', '1'), ('SSH_KEY_PATH', '~/.ssh/rsa_id'), ('RAW_TERMINAL_COLOR_GREEN', '\x1b[0;32m'), ('GDMSESSION', 'ubuntu'), ('KITTY_WINDOW_ID', '1'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('TERMINAL_COLOR_PURPLE', '\\e[0;35m'), ('TERMINAL_BG_COLOR_LIGHT_PURPLE', '\\e[1;45m'), ('LC_MEASUREMENT', 'en_US.UTF-8'), ('XMAKE_SHELL', 'bash'), ('RAW_TERMINAL_COLOR_LIGHT_GREEN', '\x1b[1;32m'), ('TERMINAL_COLOR_USER_NOTICE', '\\e[1;33m'), ('TERMINAL_BG_COLOR_YELLOW', '\\e[1;43m'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'en_US.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'ibus'), ('TERMINAL_COLOR_LIGHT_PURPLE', '\\e[1;35m'), ('TERMINAL_COLOR_YELLOW', '\\e[1;33m'), ('PWD', '/home/<USER>/code/pmac_robot/build/pmac_hardware_interface'), ('FZF_DEFAULT_COMMAND', 'ag --hidden --ignore .git -l -g ""'), ('RAW_TERMINAL_COLOR_BLUE', '\x1b[0;34m'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('RAW_TERMINAL_COLOR_RED', '\x1b[0;31m'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('RCUTILS_COLORIZED_OUTPUT', '1'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/code/ros2_control/build/rqt_joint_trajectory_controller:/home/<USER>/code/ros2_control/build/rqt_controller_manager:/home/<USER>/code/ros2_control/build/ros2controlcli:/home/<USER>/code/ros2_control/build/ros2_controllers_test_nodes:/home/<USER>/code/ros2_control/install/local/lib/python3.10/dist-packages:/home/<USER>/code/ros2_control/install/lib/python3.10/site-packages:/home/<USER>/code/ecat_ws/install/ethercat_msgs/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'en_US.UTF-8'), ('LC_PAPER', 'en_US.UTF-8'), ('RAW_TERMINAL_COLOR_CYAN', '\x1b[0;36m'), ('RAW_TERMINAL_COLOR_LIGHT_BLUE', '\x1b[1;34m'), ('RTW_COLOR_ERROR', '\\e[0;31m'), ('COLCON', '1'), ('RAW_TERMINAL_COLOR_LIGHT_RED', '\x1b[1;31m'), ('EDITOR', 'mvim'), ('CMAKE_PREFIX_PATH', '/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description'), ('RAW_TERMINAL_COLOR_NC', '\x1b[0m')]), 'shell': False}
[11.006680] (pmac_hardware_interface) StdoutLine: {'line': b'-- Install configuration: "RelWithDebInfo"\n'}
[11.007248] (pmac_hardware_interface) StdoutLine: {'line': b'-- Execute custom install script\n'}
[11.007518] (pmac_hardware_interface) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface//pmac_hardware_interface.xml\n'}
[11.012820] (pmac_hardware_interface) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/include/pmac_hardware_interface/ck3m_primitive.hpp\n'}
[11.017707] (pmac_hardware_interface) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/include/pmac_hardware_interface/visibility_control.h\n'}
[11.022714] (pmac_hardware_interface) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/environment/library_path.sh\n'}
[11.027757] (pmac_hardware_interface) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/environment/library_path.dsv\n'}
[11.028651] (-) TimerEvent: {}
[11.032880] (pmac_hardware_interface) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/package_run_dependencies/pmac_hardware_interface\n'}
[11.037909] (pmac_hardware_interface) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/parent_prefix_path/pmac_hardware_interface\n'}
[11.042732] (pmac_hardware_interface) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/environment/ament_prefix_path.sh\n'}
[11.047700] (pmac_hardware_interface) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/environment/ament_prefix_path.dsv\n'}
[11.052599] (pmac_hardware_interface) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/environment/path.sh\n'}
[11.057718] (pmac_hardware_interface) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/environment/path.dsv\n'}
[11.062552] (pmac_hardware_interface) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/local_setup.bash\n'}
[11.068306] (pmac_hardware_interface) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/local_setup.sh\n'}
[11.073260] (pmac_hardware_interface) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/local_setup.zsh\n'}
[11.078468] (pmac_hardware_interface) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/local_setup.dsv\n'}
[11.083719] (pmac_hardware_interface) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/package.dsv\n'}
[11.088964] (pmac_hardware_interface) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/packages/pmac_hardware_interface\n'}
[11.094675] (pmac_hardware_interface) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/hardware_interface__pluginlib__plugin/pmac_hardware_interface\n'}
[11.099972] (pmac_hardware_interface) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/cmake/ament_cmake_export_include_directories-extras.cmake\n'}
[11.104932] (pmac_hardware_interface) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/cmake/ament_cmake_export_libraries-extras.cmake\n'}
[11.109990] (pmac_hardware_interface) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[11.114898] (pmac_hardware_interface) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/cmake/pmac_hardware_interfaceConfig.cmake\n'}
[11.120013] (pmac_hardware_interface) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/cmake/pmac_hardware_interfaceConfig-version.cmake\n'}
[11.125191] (pmac_hardware_interface) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_hardware_interface/package.xml\n'}
[11.128755] (-) TimerEvent: {}
[11.130111] (pmac_hardware_interface) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/pmac_robot/install/lib/libpmac_hardware_interface.so\n'}
[11.134783] (pmac_hardware_interface) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/code/pmac_robot/install/lib/libpmac_hardware_interface.so" to ""\n'}
[11.136323] (pmac_hardware_interface) CommandEnded: {'returncode': 0}
[11.154840] (pmac_hardware_interface) JobEnded: {'identifier': 'pmac_hardware_interface', 'rc': 0}
[11.228849] (-) TimerEvent: {}
[11.329118] (-) TimerEvent: {}
[11.429395] (-) TimerEvent: {}
[11.529679] (-) TimerEvent: {}
[11.630016] (-) TimerEvent: {}
[11.730369] (-) TimerEvent: {}
[11.830769] (-) TimerEvent: {}
[11.931054] (-) TimerEvent: {}
[12.031369] (-) TimerEvent: {}
[12.131642] (-) TimerEvent: {}
[12.231975] (-) TimerEvent: {}
[12.332338] (-) TimerEvent: {}
[12.432733] (-) TimerEvent: {}
[12.533136] (-) TimerEvent: {}
[12.633470] (-) TimerEvent: {}
[12.733795] (-) TimerEvent: {}
[12.834105] (-) TimerEvent: {}
[12.934434] (-) TimerEvent: {}
[13.034739] (-) TimerEvent: {}
[13.135087] (-) TimerEvent: {}
[13.235381] (-) TimerEvent: {}
[13.335714] (-) TimerEvent: {}
[13.436039] (-) TimerEvent: {}
[13.536394] (-) TimerEvent: {}
[13.636689] (-) TimerEvent: {}
[13.737015] (-) TimerEvent: {}
[13.837327] (-) TimerEvent: {}
[13.937631] (-) TimerEvent: {}
[14.037925] (-) TimerEvent: {}
[14.138251] (-) TimerEvent: {}
[14.238572] (-) TimerEvent: {}
[14.338869] (-) TimerEvent: {}
[14.439165] (-) TimerEvent: {}
[14.539569] (-) TimerEvent: {}
[14.639935] (-) TimerEvent: {}
[14.647139] (send_trajectory) StdoutLine: {'line': b'[100%] \x1b[1m\x1b[32mLinking CXX executable send_trajectory\x1b[0m\n'}
[14.740059] (-) TimerEvent: {}
[14.840654] (-) TimerEvent: {}
[14.940930] (-) TimerEvent: {}
[14.964276] (send_trajectory) StdoutLine: {'line': b'[100%] Built target send_trajectory\n'}
[14.971428] (send_trajectory) CommandEnded: {'returncode': 0}
[14.972406] (send_trajectory) JobProgress: {'identifier': 'send_trajectory', 'progress': 'install'}
[14.972699] (send_trajectory) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/code/pmac_robot/build/send_trajectory'], 'cwd': '/home/<USER>/code/pmac_robot/build/send_trajectory', 'env': OrderedDict([('RAW_TERMINAL_COLOR_LIGHT_CYAN', '\x1b[1;36m'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HISTFILESIZE', '2147450879'), ('XMAKE_PROGRAM_DIR', '/home/<USER>/.local/share/xmake'), ('LANGUAGE', 'en_US:en'), ('USER', 'hq'), ('TERMINAL_BG_COLOR_GREEN', '\\e[42m'), ('LC_TIME', 'en_US.UTF-8'), ('TERMINAL_COLOR_USER_INPUT_DECISION', '\\e[0;35m'), ('XDG_SESSION_TYPE', 'wayland'), ('FZF_DEFAULT_OPTS', "--height 40% --layout=reverse --preview '(highlight -O ansi {} || cat {}) 2> /dev/null | head -500'"), ('TERMINAL_COLOR_GREEN', '\\e[0;32m'), ('RAW_TERMINAL_COLOR_WHITE', '\x1b[1;37m'), ('RAW_TERMINAL_COLOR_GRAY', '\x1b[1;30m'), ('TERMINAL_BG_COLOR_LIGHT_GREEN', '\\e[1;42m'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/code/ros2_control/install/lib:/home/<USER>/code/ecat_ws/install/ethercat_manager/lib:/home/<USER>/code/ecat_ws/install/ethercat_msgs/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/lib:/home/<USER>/code/ecat_ws/install/ethercat_driver/lib:/home/<USER>/code/ecat_ws/install/ethercat_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib::/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('LESS', '-R'), ('AMENT_CPPCHECK_ALLOW_SLOW_VERSIONS', 'true'), ('HOME', '/home/<USER>'), ('XMAKE_ROOTDIR', '/home/<USER>/.local/bin'), ('ROS_WS_CACHE_SOURCED_TIME', '0'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('TERMINAL_BG_COLOR_BLUE', '\\e[44m'), ('TERMINAL_COLOR_LIGHT_GREEN', '\\e[1;32m'), ('LSCOLORS', 'Gxfxcxdxdxegedabagacad'), ('RAW_TERMINAL_COLOR_BLACK', '\x1b[0;30m'), ('RAW_TERMINAL_COLOR_LIGHT_GRAY', '\x1b[0;37m'), ('TERMINAL_BG_COLOR_RED', '\\e[41m'), ('RTI_LICENSE_FILE', '/opt/rti.com/rti_connext_dds-5.3.1/rti_license.dat'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('TERMINAL_COLOR_BLUE', '\\e[0;34m'), ('PAGER', 'less'), ('TERMINAL_COLOR_RED', '\\e[0;31m'), ('LC_MONETARY', 'en_US.UTF-8'), ('TERMINAL_BG_COLOR_CYAN', '\\e[46m'), ('MANAGERPID', '2382'), ('TERMINAL_BG_COLOR_LIGHT_BLUE', '\\e[1;44m'), ('SYSTEMD_EXEC_PID', '2933'), ('TERMINAL_BG_COLOR_LIGHT_RED', '\\e[1;41m'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINAL_COLOR_LIGHT_BLUE', '\\e[1;34m'), ('TERMINAL_COLOR_CYAN', '\\e[0;36m'), ('RAW_TERMINAL_COLOR_BROWN', '\x1b[0;33m'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '104852'), ('TERMINAL_COLOR_LIGHT_RED', '\\e[1;31m'), ('TERMINAL_BG_COLOR_LIGHT_CYAN', '\\e[1;46m'), ('RAW_TERMINAL_COLOR_PURPLE', '\x1b[0;35m'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/code/ws_moveit2/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install:/home/<USER>/code/ws_tools/install:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install'), ('ROS_DISTRO', 'humble'), ('TERMINAL_COLOR_NC', '\\e[0m'), ('ARCHFLAGS', '-arch x86_64'), ('TERMINAL_COLOR_LIGHT_CYAN', '\\e[1;36m'), ('LOGNAME', 'hq'), ('OSH', '/home/<USER>/.oh-my-bash'), ('JOURNAL_STREAM', '8:39227'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('RAW_TERMINAL_COLOR_LIGHT_PURPLE', '\x1b[1;35m'), ('RAW_TERMINAL_COLOR_YELLOW', '\x1b[1;33m'), ('TERMINAL_COLOR_USER_CONFIRMATION', '\\e[0;34m'), ('USERNAME', 'hq'), ('TERMINAL_BG_COLOR_WHITE', '\\e[1;47m'), ('TERMINAL_BG_COLOR_GRAY', '\\e[1;40m'), ('TERM', 'xterm-kitty'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models:'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('TERMINAL_COLOR_WHITE', '\\e[1;37m'), ('TERMINAL_COLOR_GRAY', '\\e[1;30m'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.fzf/bin'), ('SESSION_MANAGER', 'local/ubuntu2204:@/tmp/.ICE-unix/2600,unix/ubuntu2204:/tmp/.ICE-unix/2600'), ('INVOCATION_ID', 'd66a9f60f2014cd8b9df27ca687295ae'), ('TERMINAL_BG_COLOR_LIGHT_GRAY', '\\e[47m'), ('PAPERSIZE', 'letter'), ('TERMINAL_BG_COLOR_BLACK', '\\e[40m'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'en_US.UTF-8'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('RTW_COLOR_NOTIFY_USER', '\\e[1;33m'), ('TERMINAL_COLOR_LIGHT_GRAY', '\\e[0;37m'), ('TERMINAL_COLOR_BLACK', '\\e[0;30m'), ('DISPLAY', ':0'), ('HISTSIZE', '2147450879'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'en_US.UTF-8'), ('TERMINAL_BG_COLOR_BROWN', '\\e[43m'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.CWO0B3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '0'), ('AMENT_PREFIX_PATH', '/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description:/opt/ros/humble'), ('SHELL', '/usr/bin/bash'), ('LC_NAME', 'en_US.UTF-8'), ('XMAKE_PROGRAM_FILE', '/home/<USER>/.local/bin/xmake'), ('TERMINAL_COLOR_BROWN', '\\e[0;33m'), ('TERMINAL_BG_COLOR_PURPLE', '\\e[45m'), ('ROS_WS', '/home/<USER>/code/pmac_robot/'), ('QT_ACCESSIBILITY', '1'), ('SSH_KEY_PATH', '~/.ssh/rsa_id'), ('RAW_TERMINAL_COLOR_GREEN', '\x1b[0;32m'), ('GDMSESSION', 'ubuntu'), ('KITTY_WINDOW_ID', '1'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('TERMINAL_COLOR_PURPLE', '\\e[0;35m'), ('TERMINAL_BG_COLOR_LIGHT_PURPLE', '\\e[1;45m'), ('LC_MEASUREMENT', 'en_US.UTF-8'), ('XMAKE_SHELL', 'bash'), ('RAW_TERMINAL_COLOR_LIGHT_GREEN', '\x1b[1;32m'), ('TERMINAL_COLOR_USER_NOTICE', '\\e[1;33m'), ('TERMINAL_BG_COLOR_YELLOW', '\\e[1;43m'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'en_US.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'ibus'), ('TERMINAL_COLOR_LIGHT_PURPLE', '\\e[1;35m'), ('TERMINAL_COLOR_YELLOW', '\\e[1;33m'), ('PWD', '/home/<USER>/code/pmac_robot/build/send_trajectory'), ('FZF_DEFAULT_COMMAND', 'ag --hidden --ignore .git -l -g ""'), ('RAW_TERMINAL_COLOR_BLUE', '\x1b[0;34m'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('RAW_TERMINAL_COLOR_RED', '\x1b[0;31m'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('RCUTILS_COLORIZED_OUTPUT', '1'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/code/ros2_control/build/rqt_joint_trajectory_controller:/home/<USER>/code/ros2_control/build/rqt_controller_manager:/home/<USER>/code/ros2_control/build/ros2controlcli:/home/<USER>/code/ros2_control/build/ros2_controllers_test_nodes:/home/<USER>/code/ros2_control/install/local/lib/python3.10/dist-packages:/home/<USER>/code/ros2_control/install/lib/python3.10/site-packages:/home/<USER>/code/ecat_ws/install/ethercat_msgs/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'en_US.UTF-8'), ('LC_PAPER', 'en_US.UTF-8'), ('RAW_TERMINAL_COLOR_CYAN', '\x1b[0;36m'), ('RAW_TERMINAL_COLOR_LIGHT_BLUE', '\x1b[1;34m'), ('RTW_COLOR_ERROR', '\\e[0;31m'), ('COLCON', '1'), ('RAW_TERMINAL_COLOR_LIGHT_RED', '\x1b[1;31m'), ('EDITOR', 'mvim'), ('CMAKE_PREFIX_PATH', '/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description'), ('RAW_TERMINAL_COLOR_NC', '\x1b[0m')]), 'shell': False}
[14.979818] (send_trajectory) StdoutLine: {'line': b'-- Install configuration: "RelWithDebInfo"\n'}
[14.980611] (send_trajectory) StdoutLine: {'line': b'-- Execute custom install script\n'}
[14.981029] (send_trajectory) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/lib/send_trajectory/send_trajectory\n'}
[14.986523] (send_trajectory) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/launch/send_trajectory.launch.py\n'}
[14.991999] (send_trajectory) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/package_run_dependencies/send_trajectory\n'}
[14.997774] (send_trajectory) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/parent_prefix_path/send_trajectory\n'}
[15.003338] (send_trajectory) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/environment/ament_prefix_path.sh\n'}
[15.008985] (send_trajectory) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/environment/ament_prefix_path.dsv\n'}
[15.014479] (send_trajectory) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/environment/path.sh\n'}
[15.019389] (send_trajectory) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/environment/path.dsv\n'}
[15.024468] (send_trajectory) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/local_setup.bash\n'}
[15.029484] (send_trajectory) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/local_setup.sh\n'}
[15.035037] (send_trajectory) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/local_setup.zsh\n'}
[15.039951] (send_trajectory) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/local_setup.dsv\n'}
[15.041006] (-) TimerEvent: {}
[15.045064] (send_trajectory) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/package.dsv\n'}
[15.049963] (send_trajectory) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/packages/send_trajectory\n'}
[15.054925] (send_trajectory) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/cmake/send_trajectoryConfig.cmake\n'}
[15.059964] (send_trajectory) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/cmake/send_trajectoryConfig-version.cmake\n'}
[15.064973] (send_trajectory) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/send_trajectory/package.xml\n'}
[15.072061] (send_trajectory) CommandEnded: {'returncode': 0}
[15.093621] (send_trajectory) JobEnded: {'identifier': 'send_trajectory', 'rc': 0}
[15.141177] (-) TimerEvent: {}
[15.241403] (-) TimerEvent: {}
[15.341693] (-) TimerEvent: {}
[15.442044] (-) TimerEvent: {}
[15.542367] (-) TimerEvent: {}
[15.643833] (-) TimerEvent: {}
[15.744141] (-) TimerEvent: {}
[15.844467] (-) TimerEvent: {}
[15.944767] (-) TimerEvent: {}
[16.045115] (-) TimerEvent: {}
[16.145375] (-) TimerEvent: {}
[16.245715] (-) TimerEvent: {}
[16.346009] (-) TimerEvent: {}
[16.446302] (-) TimerEvent: {}
[16.546675] (-) TimerEvent: {}
[16.646963] (-) TimerEvent: {}
[16.747271] (-) TimerEvent: {}
[16.847545] (-) TimerEvent: {}
[16.947823] (-) TimerEvent: {}
[17.048146] (-) TimerEvent: {}
[17.148479] (-) TimerEvent: {}
[17.248778] (-) TimerEvent: {}
[17.349041] (-) TimerEvent: {}
[17.449384] (-) TimerEvent: {}
[17.549658] (-) TimerEvent: {}
[17.649983] (-) TimerEvent: {}
[17.750313] (-) TimerEvent: {}
[17.850652] (-) TimerEvent: {}
[17.950986] (-) TimerEvent: {}
[18.051329] (-) TimerEvent: {}
[18.151665] (-) TimerEvent: {}
[18.251944] (-) TimerEvent: {}
[18.352269] (-) TimerEvent: {}
[18.452624] (-) TimerEvent: {}
[18.552952] (-) TimerEvent: {}
[18.653293] (-) TimerEvent: {}
[18.753725] (-) TimerEvent: {}
[18.854055] (-) TimerEvent: {}
[18.954371] (-) TimerEvent: {}
[19.054675] (-) TimerEvent: {}
[19.154985] (-) TimerEvent: {}
[19.255267] (-) TimerEvent: {}
[19.355607] (-) TimerEvent: {}
[19.455932] (-) TimerEvent: {}
[19.556237] (-) TimerEvent: {}
[19.656555] (-) TimerEvent: {}
[19.756898] (-) TimerEvent: {}
[19.857209] (-) TimerEvent: {}
[19.957511] (-) TimerEvent: {}
[20.057811] (-) TimerEvent: {}
[20.158123] (-) TimerEvent: {}
[20.258504] (-) TimerEvent: {}
[20.358833] (-) TimerEvent: {}
[20.459143] (-) TimerEvent: {}
[20.559450] (-) TimerEvent: {}
[20.659735] (-) TimerEvent: {}
[20.760052] (-) TimerEvent: {}
[20.860290] (-) TimerEvent: {}
[20.960560] (-) TimerEvent: {}
[21.060839] (-) TimerEvent: {}
[21.161147] (-) TimerEvent: {}
[21.261403] (-) TimerEvent: {}
[21.361675] (-) TimerEvent: {}
[21.461933] (-) TimerEvent: {}
[21.562206] (-) TimerEvent: {}
[21.662438] (-) TimerEvent: {}
[21.762679] (-) TimerEvent: {}
[21.863000] (-) TimerEvent: {}
[21.963337] (-) TimerEvent: {}
[22.063649] (-) TimerEvent: {}
[22.163973] (-) TimerEvent: {}
[22.264346] (-) TimerEvent: {}
[22.364715] (-) TimerEvent: {}
[22.465005] (-) TimerEvent: {}
[22.565673] (-) TimerEvent: {}
[22.665953] (-) TimerEvent: {}
[22.766301] (-) TimerEvent: {}
[22.866661] (-) TimerEvent: {}
[22.966970] (-) TimerEvent: {}
[23.067241] (-) TimerEvent: {}
[23.167578] (-) TimerEvent: {}
[23.267918] (-) TimerEvent: {}
[23.368244] (-) TimerEvent: {}
[23.468539] (-) TimerEvent: {}
[23.568915] (-) TimerEvent: {}
[23.669252] (-) TimerEvent: {}
[23.769555] (-) TimerEvent: {}
[23.869846] (-) TimerEvent: {}
[23.970172] (-) TimerEvent: {}
[24.070477] (-) TimerEvent: {}
[24.170828] (-) TimerEvent: {}
[24.271157] (-) TimerEvent: {}
[24.371505] (-) TimerEvent: {}
[24.471817] (-) TimerEvent: {}
[24.572127] (-) TimerEvent: {}
[24.672449] (-) TimerEvent: {}
[24.772732] (-) TimerEvent: {}
[24.873024] (-) TimerEvent: {}
[24.973323] (-) TimerEvent: {}
[25.073615] (-) TimerEvent: {}
[25.173870] (-) TimerEvent: {}
[25.274239] (-) TimerEvent: {}
[25.374517] (-) TimerEvent: {}
[25.477135] (-) TimerEvent: {}
[25.577395] (-) TimerEvent: {}
[25.677732] (-) TimerEvent: {}
[25.778057] (-) TimerEvent: {}
[25.878320] (-) TimerEvent: {}
[25.978646] (-) TimerEvent: {}
[26.078925] (-) TimerEvent: {}
[26.179275] (-) TimerEvent: {}
[26.279529] (-) TimerEvent: {}
[26.379840] (-) TimerEvent: {}
[26.480106] (-) TimerEvent: {}
[26.580453] (-) TimerEvent: {}
[26.680783] (-) TimerEvent: {}
[26.781112] (-) TimerEvent: {}
[26.881372] (-) TimerEvent: {}
[26.948168] (pmac_primitive_controller) StdoutLine: {'line': b'[100%] \x1b[1m\x1b[32mLinking CXX shared library libpmac_primitive_controller.so\x1b[0m\n'}
[26.981516] (-) TimerEvent: {}
[27.081853] (-) TimerEvent: {}
[27.182202] (-) TimerEvent: {}
[27.282510] (-) TimerEvent: {}
[27.382866] (-) TimerEvent: {}
[27.416040] (pmac_primitive_controller) StdoutLine: {'line': b'[100%] Built target pmac_primitive_controller\n'}
[27.482856] (pmac_primitive_controller) CommandEnded: {'returncode': 0}
[27.483823] (-) TimerEvent: {}
[27.484310] (pmac_primitive_controller) JobProgress: {'identifier': 'pmac_primitive_controller', 'progress': 'install'}
[27.485430] (pmac_primitive_controller) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/code/pmac_robot/build/pmac_primitive_controller'], 'cwd': '/home/<USER>/code/pmac_robot/build/pmac_primitive_controller', 'env': OrderedDict([('RAW_TERMINAL_COLOR_LIGHT_CYAN', '\x1b[1;36m'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HISTFILESIZE', '2147450879'), ('XMAKE_PROGRAM_DIR', '/home/<USER>/.local/share/xmake'), ('LANGUAGE', 'en_US:en'), ('USER', 'hq'), ('TERMINAL_BG_COLOR_GREEN', '\\e[42m'), ('LC_TIME', 'en_US.UTF-8'), ('TERMINAL_COLOR_USER_INPUT_DECISION', '\\e[0;35m'), ('XDG_SESSION_TYPE', 'wayland'), ('FZF_DEFAULT_OPTS', "--height 40% --layout=reverse --preview '(highlight -O ansi {} || cat {}) 2> /dev/null | head -500'"), ('TERMINAL_COLOR_GREEN', '\\e[0;32m'), ('RAW_TERMINAL_COLOR_WHITE', '\x1b[1;37m'), ('RAW_TERMINAL_COLOR_GRAY', '\x1b[1;30m'), ('TERMINAL_BG_COLOR_LIGHT_GREEN', '\\e[1;42m'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/code/ros2_control/install/lib:/home/<USER>/code/ecat_ws/install/ethercat_manager/lib:/home/<USER>/code/ecat_ws/install/ethercat_msgs/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/lib:/home/<USER>/code/ecat_ws/install/ethercat_driver/lib:/home/<USER>/code/ecat_ws/install/ethercat_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib::/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('LESS', '-R'), ('AMENT_CPPCHECK_ALLOW_SLOW_VERSIONS', 'true'), ('HOME', '/home/<USER>'), ('XMAKE_ROOTDIR', '/home/<USER>/.local/bin'), ('ROS_WS_CACHE_SOURCED_TIME', '0'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('TERMINAL_BG_COLOR_BLUE', '\\e[44m'), ('TERMINAL_COLOR_LIGHT_GREEN', '\\e[1;32m'), ('LSCOLORS', 'Gxfxcxdxdxegedabagacad'), ('RAW_TERMINAL_COLOR_BLACK', '\x1b[0;30m'), ('RAW_TERMINAL_COLOR_LIGHT_GRAY', '\x1b[0;37m'), ('TERMINAL_BG_COLOR_RED', '\\e[41m'), ('RTI_LICENSE_FILE', '/opt/rti.com/rti_connext_dds-5.3.1/rti_license.dat'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('TERMINAL_COLOR_BLUE', '\\e[0;34m'), ('PAGER', 'less'), ('TERMINAL_COLOR_RED', '\\e[0;31m'), ('LC_MONETARY', 'en_US.UTF-8'), ('TERMINAL_BG_COLOR_CYAN', '\\e[46m'), ('MANAGERPID', '2382'), ('TERMINAL_BG_COLOR_LIGHT_BLUE', '\\e[1;44m'), ('SYSTEMD_EXEC_PID', '2933'), ('TERMINAL_BG_COLOR_LIGHT_RED', '\\e[1;41m'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINAL_COLOR_LIGHT_BLUE', '\\e[1;34m'), ('TERMINAL_COLOR_CYAN', '\\e[0;36m'), ('RAW_TERMINAL_COLOR_BROWN', '\x1b[0;33m'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '104852'), ('TERMINAL_COLOR_LIGHT_RED', '\\e[1;31m'), ('TERMINAL_BG_COLOR_LIGHT_CYAN', '\\e[1;46m'), ('RAW_TERMINAL_COLOR_PURPLE', '\x1b[0;35m'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/code/ws_moveit2/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install:/home/<USER>/code/ws_tools/install:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install'), ('ROS_DISTRO', 'humble'), ('TERMINAL_COLOR_NC', '\\e[0m'), ('ARCHFLAGS', '-arch x86_64'), ('TERMINAL_COLOR_LIGHT_CYAN', '\\e[1;36m'), ('LOGNAME', 'hq'), ('OSH', '/home/<USER>/.oh-my-bash'), ('JOURNAL_STREAM', '8:39227'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('RAW_TERMINAL_COLOR_LIGHT_PURPLE', '\x1b[1;35m'), ('RAW_TERMINAL_COLOR_YELLOW', '\x1b[1;33m'), ('TERMINAL_COLOR_USER_CONFIRMATION', '\\e[0;34m'), ('USERNAME', 'hq'), ('TERMINAL_BG_COLOR_WHITE', '\\e[1;47m'), ('TERMINAL_BG_COLOR_GRAY', '\\e[1;40m'), ('TERM', 'xterm-kitty'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models:'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('TERMINAL_COLOR_WHITE', '\\e[1;37m'), ('TERMINAL_COLOR_GRAY', '\\e[1;30m'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.fzf/bin'), ('SESSION_MANAGER', 'local/ubuntu2204:@/tmp/.ICE-unix/2600,unix/ubuntu2204:/tmp/.ICE-unix/2600'), ('INVOCATION_ID', 'd66a9f60f2014cd8b9df27ca687295ae'), ('TERMINAL_BG_COLOR_LIGHT_GRAY', '\\e[47m'), ('PAPERSIZE', 'letter'), ('TERMINAL_BG_COLOR_BLACK', '\\e[40m'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'en_US.UTF-8'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('RTW_COLOR_NOTIFY_USER', '\\e[1;33m'), ('TERMINAL_COLOR_LIGHT_GRAY', '\\e[0;37m'), ('TERMINAL_COLOR_BLACK', '\\e[0;30m'), ('DISPLAY', ':0'), ('HISTSIZE', '2147450879'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'en_US.UTF-8'), ('TERMINAL_BG_COLOR_BROWN', '\\e[43m'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.CWO0B3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '0'), ('AMENT_PREFIX_PATH', '/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description:/opt/ros/humble'), ('SHELL', '/usr/bin/bash'), ('LC_NAME', 'en_US.UTF-8'), ('XMAKE_PROGRAM_FILE', '/home/<USER>/.local/bin/xmake'), ('TERMINAL_COLOR_BROWN', '\\e[0;33m'), ('TERMINAL_BG_COLOR_PURPLE', '\\e[45m'), ('ROS_WS', '/home/<USER>/code/pmac_robot/'), ('QT_ACCESSIBILITY', '1'), ('SSH_KEY_PATH', '~/.ssh/rsa_id'), ('RAW_TERMINAL_COLOR_GREEN', '\x1b[0;32m'), ('GDMSESSION', 'ubuntu'), ('KITTY_WINDOW_ID', '1'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('TERMINAL_COLOR_PURPLE', '\\e[0;35m'), ('TERMINAL_BG_COLOR_LIGHT_PURPLE', '\\e[1;45m'), ('LC_MEASUREMENT', 'en_US.UTF-8'), ('XMAKE_SHELL', 'bash'), ('RAW_TERMINAL_COLOR_LIGHT_GREEN', '\x1b[1;32m'), ('TERMINAL_COLOR_USER_NOTICE', '\\e[1;33m'), ('TERMINAL_BG_COLOR_YELLOW', '\\e[1;43m'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'en_US.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'ibus'), ('TERMINAL_COLOR_LIGHT_PURPLE', '\\e[1;35m'), ('TERMINAL_COLOR_YELLOW', '\\e[1;33m'), ('PWD', '/home/<USER>/code/pmac_robot/build/pmac_primitive_controller'), ('FZF_DEFAULT_COMMAND', 'ag --hidden --ignore .git -l -g ""'), ('RAW_TERMINAL_COLOR_BLUE', '\x1b[0;34m'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('RAW_TERMINAL_COLOR_RED', '\x1b[0;31m'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('RCUTILS_COLORIZED_OUTPUT', '1'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/code/ros2_control/build/rqt_joint_trajectory_controller:/home/<USER>/code/ros2_control/build/rqt_controller_manager:/home/<USER>/code/ros2_control/build/ros2controlcli:/home/<USER>/code/ros2_control/build/ros2_controllers_test_nodes:/home/<USER>/code/ros2_control/install/local/lib/python3.10/dist-packages:/home/<USER>/code/ros2_control/install/lib/python3.10/site-packages:/home/<USER>/code/ecat_ws/install/ethercat_msgs/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'en_US.UTF-8'), ('LC_PAPER', 'en_US.UTF-8'), ('RAW_TERMINAL_COLOR_CYAN', '\x1b[0;36m'), ('RAW_TERMINAL_COLOR_LIGHT_BLUE', '\x1b[1;34m'), ('RTW_COLOR_ERROR', '\\e[0;31m'), ('COLCON', '1'), ('RAW_TERMINAL_COLOR_LIGHT_RED', '\x1b[1;31m'), ('EDITOR', 'mvim'), ('CMAKE_PREFIX_PATH', '/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description'), ('RAW_TERMINAL_COLOR_NC', '\x1b[0m')]), 'shell': False}
[27.489926] (pmac_primitive_controller) StdoutLine: {'line': b'-- Install configuration: "RelWithDebInfo"\n'}
[27.490391] (pmac_primitive_controller) StdoutLine: {'line': b'-- Execute custom install script\n'}
[27.490781] (pmac_primitive_controller) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/include/pmac_primitive_controller/pmac_primitive_controller_parameters.hpp\n'}
[27.495715] (pmac_primitive_controller) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/include/pmac_primitive_controller/validate_pmac_primitive_controller_parameters.hpp\n'}
[27.500854] (pmac_primitive_controller) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller//pmac_primitive_controller.xml\n'}
[27.506170] (pmac_primitive_controller) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/lib/libpmac_primitive_controller.so\n'}
[27.511017] (pmac_primitive_controller) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/include/pmac_primitive_controller/pmac_primitive_controller/pmac_primitive_controller.hpp\n'}
[27.515804] (pmac_primitive_controller) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/include/pmac_primitive_controller/pmac_primitive_controller/validate_pmac_primitive_controller_parameters.hpp\n'}
[27.522011] (pmac_primitive_controller) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/include/pmac_primitive_controller/pmac_primitive_controller/visibility_control.h\n'}
[27.527197] (pmac_primitive_controller) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/environment/library_path.sh\n'}
[27.531910] (pmac_primitive_controller) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/environment/library_path.dsv\n'}
[27.537169] (pmac_primitive_controller) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/package_run_dependencies/pmac_primitive_controller\n'}
[27.542387] (pmac_primitive_controller) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/parent_prefix_path/pmac_primitive_controller\n'}
[27.547304] (pmac_primitive_controller) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/environment/ament_prefix_path.sh\n'}
[27.552797] (pmac_primitive_controller) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/environment/ament_prefix_path.dsv\n'}
[27.557889] (pmac_primitive_controller) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/environment/path.sh\n'}
[27.562861] (pmac_primitive_controller) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/environment/path.dsv\n'}
[27.568099] (pmac_primitive_controller) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/local_setup.bash\n'}
[27.573891] (pmac_primitive_controller) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/local_setup.sh\n'}
[27.578642] (pmac_primitive_controller) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/local_setup.zsh\n'}
[27.583455] (pmac_primitive_controller) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/local_setup.dsv\n'}
[27.583926] (-) TimerEvent: {}
[27.588928] (pmac_primitive_controller) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/package.dsv\n'}
[27.593966] (pmac_primitive_controller) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/packages/pmac_primitive_controller\n'}
[27.598814] (pmac_primitive_controller) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/controller_interface__pluginlib__plugin/pmac_primitive_controller\n'}
[27.604024] (pmac_primitive_controller) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[27.608914] (pmac_primitive_controller) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/cmake/ament_cmake_export_include_directories-extras.cmake\n'}
[27.613533] (pmac_primitive_controller) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/cmake/ament_cmake_export_libraries-extras.cmake\n'}
[27.618785] (pmac_primitive_controller) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/cmake/pmac_primitive_controllerConfig.cmake\n'}
[27.623670] (pmac_primitive_controller) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/cmake/pmac_primitive_controllerConfig-version.cmake\n'}
[27.628539] (pmac_primitive_controller) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_primitive_controller/package.xml\n'}
[27.634910] (pmac_primitive_controller) CommandEnded: {'returncode': 0}
[27.652028] (pmac_primitive_controller) JobEnded: {'identifier': 'pmac_primitive_controller', 'rc': 0}
[27.653792] (pmac_bringup) JobStarted: {'identifier': 'pmac_bringup'}
[27.672690] (pmac_bringup) JobProgress: {'identifier': 'pmac_bringup', 'progress': 'cmake'}
[27.673208] (pmac_bringup) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/code/pmac_robot/src/pmac_bringup', '-DCMAKE_BUILD_TYPE=RelWithDebInfo', '-DBUILD_TESTING=0', '-DCMAKE_EXPORT_COMPILE_COMMANDS=On', '-Wall', '-Wextra', '-Wpedantic', '-DAMENT_CMAKE_SYMLINK_INSTALL=1', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/code/pmac_robot/install'], 'cwd': '/home/<USER>/code/pmac_robot/build/pmac_bringup', 'env': OrderedDict([('RAW_TERMINAL_COLOR_LIGHT_CYAN', '\x1b[1;36m'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HISTFILESIZE', '2147450879'), ('XMAKE_PROGRAM_DIR', '/home/<USER>/.local/share/xmake'), ('LANGUAGE', 'en_US:en'), ('USER', 'hq'), ('TERMINAL_BG_COLOR_GREEN', '\\e[42m'), ('LC_TIME', 'en_US.UTF-8'), ('TERMINAL_COLOR_USER_INPUT_DECISION', '\\e[0;35m'), ('XDG_SESSION_TYPE', 'wayland'), ('FZF_DEFAULT_OPTS', "--height 40% --layout=reverse --preview '(highlight -O ansi {} || cat {}) 2> /dev/null | head -500'"), ('TERMINAL_COLOR_GREEN', '\\e[0;32m'), ('RAW_TERMINAL_COLOR_WHITE', '\x1b[1;37m'), ('RAW_TERMINAL_COLOR_GRAY', '\x1b[1;30m'), ('TERMINAL_BG_COLOR_LIGHT_GREEN', '\\e[1;42m'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/code/pmac_robot/install/lib:/home/<USER>/code/ros2_control/install/lib:/home/<USER>/code/ecat_ws/install/ethercat_manager/lib:/home/<USER>/code/ecat_ws/install/ethercat_msgs/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/lib:/home/<USER>/code/ecat_ws/install/ethercat_driver/lib:/home/<USER>/code/ecat_ws/install/ethercat_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins'), ('LESS', '-R'), ('AMENT_CPPCHECK_ALLOW_SLOW_VERSIONS', 'true'), ('HOME', '/home/<USER>'), ('XMAKE_ROOTDIR', '/home/<USER>/.local/bin'), ('ROS_WS_CACHE_SOURCED_TIME', '0'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('TERMINAL_BG_COLOR_BLUE', '\\e[44m'), ('TERMINAL_COLOR_LIGHT_GREEN', '\\e[1;32m'), ('LSCOLORS', 'Gxfxcxdxdxegedabagacad'), ('RAW_TERMINAL_COLOR_BLACK', '\x1b[0;30m'), ('RAW_TERMINAL_COLOR_LIGHT_GRAY', '\x1b[0;37m'), ('TERMINAL_BG_COLOR_RED', '\\e[41m'), ('RTI_LICENSE_FILE', '/opt/rti.com/rti_connext_dds-5.3.1/rti_license.dat'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('TERMINAL_COLOR_BLUE', '\\e[0;34m'), ('PAGER', 'less'), ('TERMINAL_COLOR_RED', '\\e[0;31m'), ('LC_MONETARY', 'en_US.UTF-8'), ('TERMINAL_BG_COLOR_CYAN', '\\e[46m'), ('MANAGERPID', '2382'), ('TERMINAL_BG_COLOR_LIGHT_BLUE', '\\e[1;44m'), ('SYSTEMD_EXEC_PID', '2933'), ('TERMINAL_BG_COLOR_LIGHT_RED', '\\e[1;41m'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINAL_COLOR_LIGHT_BLUE', '\\e[1;34m'), ('TERMINAL_COLOR_CYAN', '\\e[0;36m'), ('RAW_TERMINAL_COLOR_BROWN', '\x1b[0;33m'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '104852'), ('TERMINAL_COLOR_LIGHT_RED', '\\e[1;31m'), ('TERMINAL_BG_COLOR_LIGHT_CYAN', '\\e[1;46m'), ('RAW_TERMINAL_COLOR_PURPLE', '\x1b[0;35m'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/code/ws_moveit2/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install:/home/<USER>/code/ws_tools/install:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install'), ('ROS_DISTRO', 'humble'), ('TERMINAL_COLOR_NC', '\\e[0m'), ('ARCHFLAGS', '-arch x86_64'), ('TERMINAL_COLOR_LIGHT_CYAN', '\\e[1;36m'), ('LOGNAME', 'hq'), ('OSH', '/home/<USER>/.oh-my-bash'), ('JOURNAL_STREAM', '8:39227'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('RAW_TERMINAL_COLOR_LIGHT_PURPLE', '\x1b[1;35m'), ('RAW_TERMINAL_COLOR_YELLOW', '\x1b[1;33m'), ('TERMINAL_COLOR_USER_CONFIRMATION', '\\e[0;34m'), ('USERNAME', 'hq'), ('TERMINAL_BG_COLOR_WHITE', '\\e[1;47m'), ('TERMINAL_BG_COLOR_GRAY', '\\e[1;40m'), ('TERM', 'xterm-kitty'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models:'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('TERMINAL_COLOR_WHITE', '\\e[1;37m'), ('TERMINAL_COLOR_GRAY', '\\e[1;30m'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.fzf/bin'), ('SESSION_MANAGER', 'local/ubuntu2204:@/tmp/.ICE-unix/2600,unix/ubuntu2204:/tmp/.ICE-unix/2600'), ('INVOCATION_ID', 'd66a9f60f2014cd8b9df27ca687295ae'), ('TERMINAL_BG_COLOR_LIGHT_GRAY', '\\e[47m'), ('PAPERSIZE', 'letter'), ('TERMINAL_BG_COLOR_BLACK', '\\e[40m'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'en_US.UTF-8'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('RTW_COLOR_NOTIFY_USER', '\\e[1;33m'), ('TERMINAL_COLOR_LIGHT_GRAY', '\\e[0;37m'), ('TERMINAL_COLOR_BLACK', '\\e[0;30m'), ('DISPLAY', ':0'), ('HISTSIZE', '2147450879'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'en_US.UTF-8'), ('TERMINAL_BG_COLOR_BROWN', '\\e[43m'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.CWO0B3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '0'), ('AMENT_PREFIX_PATH', '/home/<USER>/code/pmac_robot/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description:/opt/ros/humble'), ('SHELL', '/usr/bin/bash'), ('LC_NAME', 'en_US.UTF-8'), ('XMAKE_PROGRAM_FILE', '/home/<USER>/.local/bin/xmake'), ('TERMINAL_COLOR_BROWN', '\\e[0;33m'), ('TERMINAL_BG_COLOR_PURPLE', '\\e[45m'), ('ROS_WS', '/home/<USER>/code/pmac_robot/'), ('QT_ACCESSIBILITY', '1'), ('SSH_KEY_PATH', '~/.ssh/rsa_id'), ('RAW_TERMINAL_COLOR_GREEN', '\x1b[0;32m'), ('GDMSESSION', 'ubuntu'), ('KITTY_WINDOW_ID', '1'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('TERMINAL_COLOR_PURPLE', '\\e[0;35m'), ('TERMINAL_BG_COLOR_LIGHT_PURPLE', '\\e[1;45m'), ('LC_MEASUREMENT', 'en_US.UTF-8'), ('XMAKE_SHELL', 'bash'), ('RAW_TERMINAL_COLOR_LIGHT_GREEN', '\x1b[1;32m'), ('TERMINAL_COLOR_USER_NOTICE', '\\e[1;33m'), ('TERMINAL_BG_COLOR_YELLOW', '\\e[1;43m'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'en_US.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'ibus'), ('TERMINAL_COLOR_LIGHT_PURPLE', '\\e[1;35m'), ('TERMINAL_COLOR_YELLOW', '\\e[1;33m'), ('PWD', '/home/<USER>/code/pmac_robot/build/pmac_bringup'), ('FZF_DEFAULT_COMMAND', 'ag --hidden --ignore .git -l -g ""'), ('RAW_TERMINAL_COLOR_BLUE', '\x1b[0;34m'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('RAW_TERMINAL_COLOR_RED', '\x1b[0;31m'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('RCUTILS_COLORIZED_OUTPUT', '1'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/code/ros2_control/build/rqt_joint_trajectory_controller:/home/<USER>/code/ros2_control/build/rqt_controller_manager:/home/<USER>/code/ros2_control/build/ros2controlcli:/home/<USER>/code/ros2_control/build/ros2_controllers_test_nodes:/home/<USER>/code/ros2_control/install/local/lib/python3.10/dist-packages:/home/<USER>/code/ros2_control/install/lib/python3.10/site-packages:/home/<USER>/code/ecat_ws/install/ethercat_msgs/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'en_US.UTF-8'), ('LC_PAPER', 'en_US.UTF-8'), ('RAW_TERMINAL_COLOR_CYAN', '\x1b[0;36m'), ('RAW_TERMINAL_COLOR_LIGHT_BLUE', '\x1b[1;34m'), ('RTW_COLOR_ERROR', '\\e[0;31m'), ('COLCON', '1'), ('RAW_TERMINAL_COLOR_LIGHT_RED', '\x1b[1;31m'), ('EDITOR', 'mvim'), ('CMAKE_PREFIX_PATH', '/home/<USER>/code/pmac_robot/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description'), ('RAW_TERMINAL_COLOR_NC', '\x1b[0m')]), 'shell': False}
[27.680678] (pmac_bringup) StderrLine: {'line': b'\x1b[0mCMake Deprecation Warning at CMakeLists.txt:1 (cmake_minimum_required):\n'}
[27.680908] (pmac_bringup) StderrLine: {'line': b'  Compatibility with CMake < 3.10 will be removed from a future version of\n'}
[27.681040] (pmac_bringup) StderrLine: {'line': b'  CMake.\n'}
[27.681156] (pmac_bringup) StderrLine: {'line': b'\n'}
[27.681305] (pmac_bringup) StderrLine: {'line': b'  Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax\n'}
[27.681452] (pmac_bringup) StderrLine: {'line': b'  to tell CMake that the project requires at least <min> but has been updated\n'}
[27.681624] (pmac_bringup) StderrLine: {'line': b'  to work with policies introduced by <max> or earlier.\n'}
[27.681778] (pmac_bringup) StderrLine: {'line': b'\n'}
[27.681920] (pmac_bringup) StderrLine: {'line': b'\x1b[0m\n'}
[27.684085] (-) TimerEvent: {}
[27.739313] (pmac_bringup) StdoutLine: {'line': b'-- The C compiler identification is GNU 11.4.0\n'}
[27.784258] (-) TimerEvent: {}
[27.822441] (pmac_bringup) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 11.4.0\n'}
[27.833008] (pmac_bringup) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[27.884420] (-) TimerEvent: {}
[27.905235] (pmac_bringup) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[27.923383] (pmac_bringup) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[27.923694] (pmac_bringup) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[27.924127] (pmac_bringup) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[27.938389] (pmac_bringup) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[27.984536] (-) TimerEvent: {}
[28.027239] (pmac_bringup) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[28.049850] (pmac_bringup) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[28.050191] (pmac_bringup) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[28.050923] (pmac_bringup) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[28.064266] (pmac_bringup) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[28.084656] (-) TimerEvent: {}
[28.184974] (-) TimerEvent: {}
[28.255591] (pmac_bringup) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter\n'}
[28.285095] (-) TimerEvent: {}
[28.335320] (pmac_bringup) StdoutLine: {'line': b'-- Override CMake install command with custom implementation using symlinks instead of copying resources\n'}
[28.385198] (-) TimerEvent: {}
[28.485488] (-) TimerEvent: {}
[28.523726] (pmac_bringup) StdoutLine: {'line': b'-- Configuring done (0.8s)\n'}
[28.529084] (pmac_bringup) StderrLine: {'line': b'\x1b[33mCMake Warning:\n'}
[28.529216] (pmac_bringup) StderrLine: {'line': b'  Manually-specified variables were not used by the project:\n'}
[28.529348] (pmac_bringup) StderrLine: {'line': b'\n'}
[28.529415] (pmac_bringup) StderrLine: {'line': b'    CMAKE_EXPORT_COMPILE_COMMANDS\n'}
[28.529479] (pmac_bringup) StderrLine: {'line': b'\n'}
[28.529541] (pmac_bringup) StderrLine: {'line': b'\x1b[0m\n'}
[28.529614] (pmac_bringup) StdoutLine: {'line': b'-- Generating done (0.0s)\n'}
[28.530594] (pmac_bringup) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/code/pmac_robot/build/pmac_bringup\n'}
[28.537313] (pmac_bringup) CommandEnded: {'returncode': 0}
[28.538324] (pmac_bringup) JobProgress: {'identifier': 'pmac_bringup', 'progress': 'build'}
[28.538516] (pmac_bringup) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/code/pmac_robot/build/pmac_bringup', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/code/pmac_robot/build/pmac_bringup', 'env': OrderedDict([('RAW_TERMINAL_COLOR_LIGHT_CYAN', '\x1b[1;36m'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HISTFILESIZE', '2147450879'), ('XMAKE_PROGRAM_DIR', '/home/<USER>/.local/share/xmake'), ('LANGUAGE', 'en_US:en'), ('USER', 'hq'), ('TERMINAL_BG_COLOR_GREEN', '\\e[42m'), ('LC_TIME', 'en_US.UTF-8'), ('TERMINAL_COLOR_USER_INPUT_DECISION', '\\e[0;35m'), ('XDG_SESSION_TYPE', 'wayland'), ('FZF_DEFAULT_OPTS', "--height 40% --layout=reverse --preview '(highlight -O ansi {} || cat {}) 2> /dev/null | head -500'"), ('TERMINAL_COLOR_GREEN', '\\e[0;32m'), ('RAW_TERMINAL_COLOR_WHITE', '\x1b[1;37m'), ('RAW_TERMINAL_COLOR_GRAY', '\x1b[1;30m'), ('TERMINAL_BG_COLOR_LIGHT_GREEN', '\\e[1;42m'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/code/pmac_robot/install/lib:/home/<USER>/code/ros2_control/install/lib:/home/<USER>/code/ecat_ws/install/ethercat_manager/lib:/home/<USER>/code/ecat_ws/install/ethercat_msgs/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/lib:/home/<USER>/code/ecat_ws/install/ethercat_driver/lib:/home/<USER>/code/ecat_ws/install/ethercat_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins'), ('LESS', '-R'), ('AMENT_CPPCHECK_ALLOW_SLOW_VERSIONS', 'true'), ('HOME', '/home/<USER>'), ('XMAKE_ROOTDIR', '/home/<USER>/.local/bin'), ('ROS_WS_CACHE_SOURCED_TIME', '0'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('TERMINAL_BG_COLOR_BLUE', '\\e[44m'), ('TERMINAL_COLOR_LIGHT_GREEN', '\\e[1;32m'), ('LSCOLORS', 'Gxfxcxdxdxegedabagacad'), ('RAW_TERMINAL_COLOR_BLACK', '\x1b[0;30m'), ('RAW_TERMINAL_COLOR_LIGHT_GRAY', '\x1b[0;37m'), ('TERMINAL_BG_COLOR_RED', '\\e[41m'), ('RTI_LICENSE_FILE', '/opt/rti.com/rti_connext_dds-5.3.1/rti_license.dat'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('TERMINAL_COLOR_BLUE', '\\e[0;34m'), ('PAGER', 'less'), ('TERMINAL_COLOR_RED', '\\e[0;31m'), ('LC_MONETARY', 'en_US.UTF-8'), ('TERMINAL_BG_COLOR_CYAN', '\\e[46m'), ('MANAGERPID', '2382'), ('TERMINAL_BG_COLOR_LIGHT_BLUE', '\\e[1;44m'), ('SYSTEMD_EXEC_PID', '2933'), ('TERMINAL_BG_COLOR_LIGHT_RED', '\\e[1;41m'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINAL_COLOR_LIGHT_BLUE', '\\e[1;34m'), ('TERMINAL_COLOR_CYAN', '\\e[0;36m'), ('RAW_TERMINAL_COLOR_BROWN', '\x1b[0;33m'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '104852'), ('TERMINAL_COLOR_LIGHT_RED', '\\e[1;31m'), ('TERMINAL_BG_COLOR_LIGHT_CYAN', '\\e[1;46m'), ('RAW_TERMINAL_COLOR_PURPLE', '\x1b[0;35m'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/code/ws_moveit2/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install:/home/<USER>/code/ws_tools/install:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install'), ('ROS_DISTRO', 'humble'), ('TERMINAL_COLOR_NC', '\\e[0m'), ('ARCHFLAGS', '-arch x86_64'), ('TERMINAL_COLOR_LIGHT_CYAN', '\\e[1;36m'), ('LOGNAME', 'hq'), ('OSH', '/home/<USER>/.oh-my-bash'), ('JOURNAL_STREAM', '8:39227'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('RAW_TERMINAL_COLOR_LIGHT_PURPLE', '\x1b[1;35m'), ('RAW_TERMINAL_COLOR_YELLOW', '\x1b[1;33m'), ('TERMINAL_COLOR_USER_CONFIRMATION', '\\e[0;34m'), ('USERNAME', 'hq'), ('TERMINAL_BG_COLOR_WHITE', '\\e[1;47m'), ('TERMINAL_BG_COLOR_GRAY', '\\e[1;40m'), ('TERM', 'xterm-kitty'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models:'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('TERMINAL_COLOR_WHITE', '\\e[1;37m'), ('TERMINAL_COLOR_GRAY', '\\e[1;30m'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.fzf/bin'), ('SESSION_MANAGER', 'local/ubuntu2204:@/tmp/.ICE-unix/2600,unix/ubuntu2204:/tmp/.ICE-unix/2600'), ('INVOCATION_ID', 'd66a9f60f2014cd8b9df27ca687295ae'), ('TERMINAL_BG_COLOR_LIGHT_GRAY', '\\e[47m'), ('PAPERSIZE', 'letter'), ('TERMINAL_BG_COLOR_BLACK', '\\e[40m'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'en_US.UTF-8'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('RTW_COLOR_NOTIFY_USER', '\\e[1;33m'), ('TERMINAL_COLOR_LIGHT_GRAY', '\\e[0;37m'), ('TERMINAL_COLOR_BLACK', '\\e[0;30m'), ('DISPLAY', ':0'), ('HISTSIZE', '2147450879'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'en_US.UTF-8'), ('TERMINAL_BG_COLOR_BROWN', '\\e[43m'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.CWO0B3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '0'), ('AMENT_PREFIX_PATH', '/home/<USER>/code/pmac_robot/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description:/opt/ros/humble'), ('SHELL', '/usr/bin/bash'), ('LC_NAME', 'en_US.UTF-8'), ('XMAKE_PROGRAM_FILE', '/home/<USER>/.local/bin/xmake'), ('TERMINAL_COLOR_BROWN', '\\e[0;33m'), ('TERMINAL_BG_COLOR_PURPLE', '\\e[45m'), ('ROS_WS', '/home/<USER>/code/pmac_robot/'), ('QT_ACCESSIBILITY', '1'), ('SSH_KEY_PATH', '~/.ssh/rsa_id'), ('RAW_TERMINAL_COLOR_GREEN', '\x1b[0;32m'), ('GDMSESSION', 'ubuntu'), ('KITTY_WINDOW_ID', '1'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('TERMINAL_COLOR_PURPLE', '\\e[0;35m'), ('TERMINAL_BG_COLOR_LIGHT_PURPLE', '\\e[1;45m'), ('LC_MEASUREMENT', 'en_US.UTF-8'), ('XMAKE_SHELL', 'bash'), ('RAW_TERMINAL_COLOR_LIGHT_GREEN', '\x1b[1;32m'), ('TERMINAL_COLOR_USER_NOTICE', '\\e[1;33m'), ('TERMINAL_BG_COLOR_YELLOW', '\\e[1;43m'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'en_US.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'ibus'), ('TERMINAL_COLOR_LIGHT_PURPLE', '\\e[1;35m'), ('TERMINAL_COLOR_YELLOW', '\\e[1;33m'), ('PWD', '/home/<USER>/code/pmac_robot/build/pmac_bringup'), ('FZF_DEFAULT_COMMAND', 'ag --hidden --ignore .git -l -g ""'), ('RAW_TERMINAL_COLOR_BLUE', '\x1b[0;34m'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('RAW_TERMINAL_COLOR_RED', '\x1b[0;31m'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('RCUTILS_COLORIZED_OUTPUT', '1'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/code/ros2_control/build/rqt_joint_trajectory_controller:/home/<USER>/code/ros2_control/build/rqt_controller_manager:/home/<USER>/code/ros2_control/build/ros2controlcli:/home/<USER>/code/ros2_control/build/ros2_controllers_test_nodes:/home/<USER>/code/ros2_control/install/local/lib/python3.10/dist-packages:/home/<USER>/code/ros2_control/install/lib/python3.10/site-packages:/home/<USER>/code/ecat_ws/install/ethercat_msgs/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'en_US.UTF-8'), ('LC_PAPER', 'en_US.UTF-8'), ('RAW_TERMINAL_COLOR_CYAN', '\x1b[0;36m'), ('RAW_TERMINAL_COLOR_LIGHT_BLUE', '\x1b[1;34m'), ('RTW_COLOR_ERROR', '\\e[0;31m'), ('COLCON', '1'), ('RAW_TERMINAL_COLOR_LIGHT_RED', '\x1b[1;31m'), ('EDITOR', 'mvim'), ('CMAKE_PREFIX_PATH', '/home/<USER>/code/pmac_robot/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description'), ('RAW_TERMINAL_COLOR_NC', '\x1b[0m')]), 'shell': False}
[28.563389] (pmac_bringup) CommandEnded: {'returncode': 0}
[28.564165] (pmac_bringup) JobProgress: {'identifier': 'pmac_bringup', 'progress': 'install'}
[28.565085] (pmac_bringup) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/code/pmac_robot/build/pmac_bringup'], 'cwd': '/home/<USER>/code/pmac_robot/build/pmac_bringup', 'env': OrderedDict([('RAW_TERMINAL_COLOR_LIGHT_CYAN', '\x1b[1;36m'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HISTFILESIZE', '2147450879'), ('XMAKE_PROGRAM_DIR', '/home/<USER>/.local/share/xmake'), ('LANGUAGE', 'en_US:en'), ('USER', 'hq'), ('TERMINAL_BG_COLOR_GREEN', '\\e[42m'), ('LC_TIME', 'en_US.UTF-8'), ('TERMINAL_COLOR_USER_INPUT_DECISION', '\\e[0;35m'), ('XDG_SESSION_TYPE', 'wayland'), ('FZF_DEFAULT_OPTS', "--height 40% --layout=reverse --preview '(highlight -O ansi {} || cat {}) 2> /dev/null | head -500'"), ('TERMINAL_COLOR_GREEN', '\\e[0;32m'), ('RAW_TERMINAL_COLOR_WHITE', '\x1b[1;37m'), ('RAW_TERMINAL_COLOR_GRAY', '\x1b[1;30m'), ('TERMINAL_BG_COLOR_LIGHT_GREEN', '\\e[1;42m'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/code/pmac_robot/install/lib:/home/<USER>/code/ros2_control/install/lib:/home/<USER>/code/ecat_ws/install/ethercat_manager/lib:/home/<USER>/code/ecat_ws/install/ethercat_msgs/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/lib:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/lib:/home/<USER>/code/ecat_ws/install/ethercat_driver/lib:/home/<USER>/code/ecat_ws/install/ethercat_interface/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins'), ('LESS', '-R'), ('AMENT_CPPCHECK_ALLOW_SLOW_VERSIONS', 'true'), ('HOME', '/home/<USER>'), ('XMAKE_ROOTDIR', '/home/<USER>/.local/bin'), ('ROS_WS_CACHE_SOURCED_TIME', '0'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('TERMINAL_BG_COLOR_BLUE', '\\e[44m'), ('TERMINAL_COLOR_LIGHT_GREEN', '\\e[1;32m'), ('LSCOLORS', 'Gxfxcxdxdxegedabagacad'), ('RAW_TERMINAL_COLOR_BLACK', '\x1b[0;30m'), ('RAW_TERMINAL_COLOR_LIGHT_GRAY', '\x1b[0;37m'), ('TERMINAL_BG_COLOR_RED', '\\e[41m'), ('RTI_LICENSE_FILE', '/opt/rti.com/rti_connext_dds-5.3.1/rti_license.dat'), ('GAZEBO_MASTER_URI', 'http://localhost:11345'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('TERMINAL_COLOR_BLUE', '\\e[0;34m'), ('PAGER', 'less'), ('TERMINAL_COLOR_RED', '\\e[0;31m'), ('LC_MONETARY', 'en_US.UTF-8'), ('TERMINAL_BG_COLOR_CYAN', '\\e[46m'), ('MANAGERPID', '2382'), ('TERMINAL_BG_COLOR_LIGHT_BLUE', '\\e[1;44m'), ('SYSTEMD_EXEC_PID', '2933'), ('TERMINAL_BG_COLOR_LIGHT_RED', '\\e[1;41m'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINAL_COLOR_LIGHT_BLUE', '\\e[1;34m'), ('TERMINAL_COLOR_CYAN', '\\e[0;36m'), ('RAW_TERMINAL_COLOR_BROWN', '\x1b[0;33m'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '104852'), ('TERMINAL_COLOR_LIGHT_RED', '\\e[1;31m'), ('TERMINAL_BG_COLOR_LIGHT_CYAN', '\\e[1;46m'), ('RAW_TERMINAL_COLOR_PURPLE', '\x1b[0;35m'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/code/ws_moveit2/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install:/home/<USER>/code/ws_tools/install:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install'), ('ROS_DISTRO', 'humble'), ('TERMINAL_COLOR_NC', '\\e[0m'), ('ARCHFLAGS', '-arch x86_64'), ('TERMINAL_COLOR_LIGHT_CYAN', '\\e[1;36m'), ('LOGNAME', 'hq'), ('OSH', '/home/<USER>/.oh-my-bash'), ('JOURNAL_STREAM', '8:39227'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('RAW_TERMINAL_COLOR_LIGHT_PURPLE', '\x1b[1;35m'), ('RAW_TERMINAL_COLOR_YELLOW', '\x1b[1;33m'), ('TERMINAL_COLOR_USER_CONFIRMATION', '\\e[0;34m'), ('USERNAME', 'hq'), ('TERMINAL_BG_COLOR_WHITE', '\\e[1;47m'), ('TERMINAL_BG_COLOR_GRAY', '\\e[1;40m'), ('TERM', 'xterm-kitty'), ('GAZEBO_MODEL_PATH', '/usr/share/gazebo/../../share/gazebo-11/models:'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('TERMINAL_COLOR_WHITE', '\\e[1;37m'), ('TERMINAL_COLOR_GRAY', '\\e[1;30m'), ('OGRE_RESOURCE_PATH', '/usr/lib/x86_64-linux-gnu/OGRE-1.9.0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.fzf/bin'), ('SESSION_MANAGER', 'local/ubuntu2204:@/tmp/.ICE-unix/2600,unix/ubuntu2204:/tmp/.ICE-unix/2600'), ('INVOCATION_ID', 'd66a9f60f2014cd8b9df27ca687295ae'), ('TERMINAL_BG_COLOR_LIGHT_GRAY', '\\e[47m'), ('PAPERSIZE', 'letter'), ('TERMINAL_BG_COLOR_BLACK', '\\e[40m'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'en_US.UTF-8'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('RTW_COLOR_NOTIFY_USER', '\\e[1;33m'), ('TERMINAL_COLOR_LIGHT_GRAY', '\\e[0;37m'), ('TERMINAL_COLOR_BLACK', '\\e[0;30m'), ('DISPLAY', ':0'), ('HISTSIZE', '2147450879'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'en_US.UTF-8'), ('TERMINAL_BG_COLOR_BROWN', '\\e[43m'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.CWO0B3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('ROS_DOMAIN_ID', '0'), ('AMENT_PREFIX_PATH', '/home/<USER>/code/pmac_robot/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description:/opt/ros/humble'), ('SHELL', '/usr/bin/bash'), ('LC_NAME', 'en_US.UTF-8'), ('XMAKE_PROGRAM_FILE', '/home/<USER>/.local/bin/xmake'), ('TERMINAL_COLOR_BROWN', '\\e[0;33m'), ('TERMINAL_BG_COLOR_PURPLE', '\\e[45m'), ('ROS_WS', '/home/<USER>/code/pmac_robot/'), ('QT_ACCESSIBILITY', '1'), ('SSH_KEY_PATH', '~/.ssh/rsa_id'), ('RAW_TERMINAL_COLOR_GREEN', '\x1b[0;32m'), ('GDMSESSION', 'ubuntu'), ('KITTY_WINDOW_ID', '1'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('TERMINAL_COLOR_PURPLE', '\\e[0;35m'), ('TERMINAL_BG_COLOR_LIGHT_PURPLE', '\\e[1;45m'), ('LC_MEASUREMENT', 'en_US.UTF-8'), ('XMAKE_SHELL', 'bash'), ('RAW_TERMINAL_COLOR_LIGHT_GREEN', '\x1b[1;32m'), ('TERMINAL_COLOR_USER_NOTICE', '\\e[1;33m'), ('TERMINAL_BG_COLOR_YELLOW', '\\e[1;43m'), ('GAZEBO_RESOURCE_PATH', '/usr/share/gazebo/../../share/gazebo-11:'), ('LC_IDENTIFICATION', 'en_US.UTF-8'), ('GAZEBO_MODEL_DATABASE_URI', 'http://models.gazebosim.org'), ('QT_IM_MODULE', 'ibus'), ('TERMINAL_COLOR_LIGHT_PURPLE', '\\e[1;35m'), ('TERMINAL_COLOR_YELLOW', '\\e[1;33m'), ('PWD', '/home/<USER>/code/pmac_robot/build/pmac_bringup'), ('FZF_DEFAULT_COMMAND', 'ag --hidden --ignore .git -l -g ""'), ('RAW_TERMINAL_COLOR_BLUE', '\x1b[0;34m'), ('LC_ALL', 'en_US.UTF-8'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('RAW_TERMINAL_COLOR_RED', '\x1b[0;31m'), ('GAZEBO_PLUGIN_PATH', '/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:'), ('RCUTILS_COLORIZED_OUTPUT', '1'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/code/ros2_control/build/rqt_joint_trajectory_controller:/home/<USER>/code/ros2_control/build/rqt_controller_manager:/home/<USER>/code/ros2_control/build/ros2controlcli:/home/<USER>/code/ros2_control/build/ros2_controllers_test_nodes:/home/<USER>/code/ros2_control/install/local/lib/python3.10/dist-packages:/home/<USER>/code/ros2_control/install/lib/python3.10/site-packages:/home/<USER>/code/ecat_ws/install/ethercat_msgs/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'en_US.UTF-8'), ('LC_PAPER', 'en_US.UTF-8'), ('RAW_TERMINAL_COLOR_CYAN', '\x1b[0;36m'), ('RAW_TERMINAL_COLOR_LIGHT_BLUE', '\x1b[1;34m'), ('RTW_COLOR_ERROR', '\\e[0;31m'), ('COLCON', '1'), ('RAW_TERMINAL_COLOR_LIGHT_RED', '\x1b[1;31m'), ('EDITOR', 'mvim'), ('CMAKE_PREFIX_PATH', '/home/<USER>/code/pmac_robot/install:/home/<USER>/code/ros2_control/install:/home/<USER>/code/ecat_ws/install/ethercat_slave_description:/home/<USER>/code/ecat_ws/install/ethercat_manager:/home/<USER>/code/ecat_ws/install/ethercat_msgs:/home/<USER>/code/ecat_ws/install/ethercat_motor_drive:/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples:/home/<USER>/code/ecat_ws/install/ethercat_force_sensor:/home/<USER>/code/ecat_ws/install/ethercat_generic_slave:/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2:/home/<USER>/code/ecat_ws/install/ethercat_driver:/home/<USER>/code/ecat_ws/install/ethercat_interface:/home/<USER>/code/ws_tools/install/ament_lint_auto:/opt/ros/humble:/home/<USER>/code/staubli_experimental/install:/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description'), ('RAW_TERMINAL_COLOR_NC', '\x1b[0m')]), 'shell': False}
[28.570257] (pmac_bringup) StdoutLine: {'line': b'-- Install configuration: "RelWithDebInfo"\n'}
[28.570749] (pmac_bringup) StdoutLine: {'line': b'-- Execute custom install script\n'}
[28.571184] (pmac_bringup) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/config/test_goal_publishers_config.yaml\n'}
[28.575936] (pmac_bringup) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/config/tx2_60_controllers.yaml\n'}
[28.580566] (pmac_bringup) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/test_forward_position_controller.launch.py\n'}
[28.585633] (pmac_bringup) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/test_joint_trajectory_controller.launch.py\n'}
[28.585857] (-) TimerEvent: {}
[28.590600] (pmac_bringup) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/tx2_60.launch.py\n'}
[28.595439] (pmac_bringup) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/package_run_dependencies/pmac_bringup\n'}
[28.600364] (pmac_bringup) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/parent_prefix_path/pmac_bringup\n'}
[28.606043] (pmac_bringup) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/environment/ament_prefix_path.sh\n'}
[28.610740] (pmac_bringup) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/environment/ament_prefix_path.dsv\n'}
[28.615612] (pmac_bringup) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/environment/path.sh\n'}
[28.620744] (pmac_bringup) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/environment/path.dsv\n'}
[28.625854] (pmac_bringup) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/local_setup.bash\n'}
[28.630510] (pmac_bringup) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/local_setup.sh\n'}
[28.635662] (pmac_bringup) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/local_setup.zsh\n'}
[28.640805] (pmac_bringup) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/local_setup.dsv\n'}
[28.645488] (pmac_bringup) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/package.dsv\n'}
[28.650264] (pmac_bringup) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/ament_index/resource_index/packages/pmac_bringup\n'}
[28.655551] (pmac_bringup) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/cmake/pmac_bringupConfig.cmake\n'}
[28.660340] (pmac_bringup) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/cmake/pmac_bringupConfig-version.cmake\n'}
[28.664916] (pmac_bringup) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/pmac_robot/install/share/pmac_bringup/package.xml\n'}
[28.671566] (pmac_bringup) CommandEnded: {'returncode': 0}
[28.685980] (-) TimerEvent: {}
[28.690690] (pmac_bringup) JobEnded: {'identifier': 'pmac_bringup', 'rc': 0}
[28.691475] (-) EventReactorShutdown: {}
