set(_AMENT_PACKAGE_NAME "pmac_primitive_controller")
set(pmac_primitive_controller_VERSION "0.0.0")
set(pmac_primitive_controller_MAINTAINER "hq.<PERSON><PERSON> <<PERSON><PERSON><PERSON><PERSON>@gmail.com>")
set(pmac_primitive_controller_BUILD_DEPENDS "generate_parameter_library" "control_msgs" "controller_interface" "hardware_interface" "pluginlib" "rclcpp" "rclcpp_lifecycle" "realtime_tools" "std_srvs")
set(pmac_primitive_controller_BUILDTOOL_DEPENDS "ament_cmake")
set(pmac_primitive_controller_BUILD_EXPORT_DEPENDS "control_msgs" "controller_interface" "hardware_interface" "pluginlib" "rclcpp" "rclcpp_lifecycle" "realtime_tools" "std_srvs")
set(pmac_primitive_controller_BUILDTOOL_EXPORT_DEPENDS )
set(pmac_primitive_controller_EXEC_DEPENDS "control_msgs" "controller_interface" "hardware_interface" "pluginlib" "rclcpp" "rclcpp_lifecycle" "realtime_tools" "std_srvs")
set(pmac_primitive_controller_TEST_DEPENDS "ament_lint_auto" "ament_cmake_gmock" "controller_manager" "hardware_interface" "ros2_control_test_assets")
set(pmac_primitive_controller_GROUP_DEPENDS )
set(pmac_primitive_controller_MEMBER_OF_GROUPS )
set(pmac_primitive_controller_DEPRECATED "")
set(pmac_primitive_controller_EXPORT_TAGS)
list(APPEND pmac_primitive_controller_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
