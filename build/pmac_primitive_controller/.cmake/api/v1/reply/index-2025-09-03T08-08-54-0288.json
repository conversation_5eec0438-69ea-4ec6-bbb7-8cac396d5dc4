{"cmake": {"generator": {"multiConfig": false, "name": "Unix Makefiles"}, "paths": {"cmake": "/usr/bin/cmake", "cpack": "/usr/bin/cpack", "ctest": "/usr/bin/ctest", "root": "/usr/share/cmake-4.1"}, "version": {"isDirty": false, "major": 4, "minor": 1, "patch": 1, "string": "4.1.1", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-db4a12b647d5e66a02fc.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}], "reply": {"client-colcon-cmake": {"codemodel-v2": {"jsonFile": "codemodel-v2-db4a12b647d5e66a02fc.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}}}}