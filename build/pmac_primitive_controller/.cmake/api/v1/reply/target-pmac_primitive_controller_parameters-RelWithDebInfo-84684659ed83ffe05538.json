{"backtrace": 2, "backtraceGraph": {"commands": ["add_library", "generate_parameter_library"], "files": ["/opt/ros/humble/share/generate_parameter_library/cmake/generate_parameter_library.cmake", "CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 27, "parent": 0}, {"command": 0, "file": 0, "line": 86, "parent": 1}]}, "id": "pmac_primitive_controller_parameters::@6890427a1f51a3e7e1df", "name": "pmac_primitive_controller_parameters", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0, 1, 2]}, {"name": "CMake Rules", "sourceIndexes": [3, 4]}], "sources": [{"backtrace": 2, "isGenerated": true, "path": "/home/<USER>/code/pmac_robot/build/pmac_primitive_controller/include/pmac_primitive_controller/pmac_primitive_controller_parameters.hpp", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "/home/<USER>/code/pmac_robot/build/pmac_primitive_controller/include/pmac_primitive_controller/validate_pmac_primitive_controller_parameters.hpp", "sourceGroupIndex": 0}, {"backtrace": 2, "isGenerated": true, "path": "/home/<USER>/code/pmac_robot/build/pmac_primitive_controller/include/pmac_primitive_controller_parameters.hpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/code/pmac_robot/build/pmac_primitive_controller/include/pmac_primitive_controller/pmac_primitive_controller_parameters.hpp.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/code/pmac_robot/build/pmac_primitive_controller/include/pmac_primitive_controller_parameters.hpp.rule", "sourceGroupIndex": 1}], "type": "INTERFACE_LIBRARY"}