{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-RelWithDebInfo-36da79ece4fcbc34bee3.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "pmac_primitive_controller", "targetIndexes": [0, 1, 2, 3]}], "targets": [{"directoryIndex": 0, "id": "pmac_primitive_controller::@6890427a1f51a3e7e1df", "jsonFile": "target-pmac_primitive_controller-RelWithDebInfo-c3a6fb1cf635a9939076.json", "name": "pmac_primitive_controller", "projectIndex": 0}, {"directoryIndex": 0, "id": "pmac_primitive_controller_parameters::@6890427a1f51a3e7e1df", "jsonFile": "target-pmac_primitive_controller_parameters-RelWithDebInfo-84684659ed83ffe05538.json", "name": "pmac_primitive_controller_parameters", "projectIndex": 0}, {"directoryIndex": 0, "id": "pmac_primitive_controller_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-pmac_primitive_controller_uninstall-RelWithDebInfo-d911b5be078230d93fee.json", "name": "pmac_primitive_controller_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-RelWithDebInfo-e9862b9ad30ab73e9fb5.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/code/pmac_robot/build/pmac_primitive_controller", "source": "/home/<USER>/code/pmac_robot/src/pmac_primitive_controller"}, "version": {"major": 2, "minor": 8}}