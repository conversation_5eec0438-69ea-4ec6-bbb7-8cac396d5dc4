{"artifacts": [{"path": "libpmac_primitive_controller.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "ament_target_dependencies", "generate_parameter_library", "set_target_properties", "include", "find_package", "add_compile_options", "target_compile_definitions", "target_include_directories"], "files": ["CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_target_dependencies/cmake/ament_target_dependencies.cmake", "/opt/ros/humble/share/generate_parameter_library/cmake/generate_parameter_library.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/export_control_msgs__rosidl_typesupport_fastrtps_cExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/ament_cmake_export_targets-extras.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/control_msgsConfig.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/export_control_msgs__rosidl_typesupport_fastrtps_cppExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/control_msgs__rosidl_typesupport_introspection_cExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/control_msgs__rosidl_typesupport_introspection_cppExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/control_msgs__rosidl_typesupport_cppExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/export_control_msgs__rosidl_generator_pyExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/control_msgs__rosidl_typesupport_cExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/export_control_msgs__rosidl_generator_cExport.cmake", "/opt/ros/humble/share/pluginlib/cmake/export_pluginlibExport.cmake", "/opt/ros/humble/share/pluginlib/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/pluginlib/cmake/pluginlibConfig.cmake", "/home/<USER>/code/ros2_control/install/share/hardware_interface/cmake/ament_cmake_export_dependencies-extras.cmake", "/home/<USER>/code/ros2_control/install/share/hardware_interface/cmake/hardware_interfaceConfig.cmake", "/home/<USER>/code/ros2_control/install/share/controller_interface/cmake/ament_cmake_export_dependencies-extras.cmake", "/home/<USER>/code/ros2_control/install/share/controller_interface/cmake/controller_interfaceConfig.cmake", "/opt/ros/humble/share/class_loader/cmake/class_loaderExport.cmake", "/opt/ros/humble/share/class_loader/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/class_loader/cmake/class_loaderConfig.cmake", "/opt/ros/humble/share/pluginlib/cmake/ament_cmake_export_dependencies-extras.cmake", "/home/<USER>/code/ros2_control/install/share/hardware_interface/cmake/export_hardware_interfaceExport.cmake", "/home/<USER>/code/ros2_control/install/share/hardware_interface/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rclcpp_lifecycle/cmake/rclcpp_lifecycleExport.cmake", "/opt/ros/humble/share/rclcpp_lifecycle/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rclcpp_lifecycle/cmake/rclcpp_lifecycleConfig.cmake", "/opt/ros/humble/share/generate_parameter_library/cmake/generate_parameter_library-extras.cmake", "/opt/ros/humble/share/generate_parameter_library/cmake/generate_parameter_libraryConfig.cmake", "/home/<USER>/code/ros2_control/install/share/realtime_tools/cmake/export_realtime_toolsExport.cmake", "/home/<USER>/code/ros2_control/install/share/realtime_tools/cmake/ament_cmake_export_targets-extras.cmake", "/home/<USER>/code/ros2_control/install/share/realtime_tools/cmake/realtime_toolsConfig.cmake", "/opt/ros/humble/share/rclcpp/cmake/rclcppExport.cmake", "/opt/ros/humble/share/rclcpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rclcpp/cmake/rclcppConfig.cmake", "/opt/ros/humble/share/parameter_traits/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/parameter_traits/cmake/parameter_traitsConfig.cmake", "/opt/ros/humble/share/rclcpp_action/cmake/rclcpp_actionExport.cmake", "/opt/ros/humble/share/rclcpp_action/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rclcpp_action/cmake/rclcpp_actionConfig.cmake", "/home/<USER>/code/ros2_control/install/share/realtime_tools/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/rcl_yaml_param_parserExport.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/rcl_yaml_param_parserConfig.cmake", "/opt/ros/humble/share/rcl/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rcl/cmake/rclConfig.cmake", "/opt/ros/humble/share/libstatistics_collector/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/libstatistics_collector/cmake/libstatistics_collectorConfig.cmake", "/opt/ros/humble/share/rclcpp/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rcl/cmake/rclExport.cmake", "/opt/ros/humble/share/rcl/cmake/ament_cmake_export_targets-extras.cmake", "/usr/lib/x86_64-linux-gnu/cmake/fmt/fmt-targets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/fmt/fmt-config.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppExport.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppConfig.cmake", "/opt/ros/humble/share/builtin_interfaces/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/builtin_interfaces/cmake/builtin_interfacesConfig.cmake", "/opt/ros/humble/share/rcutils/cmake/rcutilsExport.cmake", "/opt/ros/humble/share/rcutils/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcutils/cmake/rcutilsConfig.cmake", "/opt/ros/humble/share/rosidl_runtime_c/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rosidl_runtime_c/cmake/rosidl_runtime_cConfig.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 31, "parent": 0}, {"command": 2, "file": 0, "line": 40, "parent": 0}, {"command": 1, "file": 1, "line": 145, "parent": 2}, {"command": 3, "file": 0, "line": 27, "parent": 0}, {"command": 1, "file": 2, "line": 92, "parent": 4}, {"command": 6, "file": 0, "line": 23, "parent": 0}, {"file": 5, "parent": 6}, {"command": 5, "file": 5, "line": 41, "parent": 7}, {"file": 4, "parent": 8}, {"command": 5, "file": 4, "line": 9, "parent": 9}, {"file": 3, "parent": 10}, {"command": 4, "file": 3, "line": 61, "parent": 11}, {"command": 5, "file": 4, "line": 9, "parent": 9}, {"file": 6, "parent": 13}, {"command": 4, "file": 6, "line": 61, "parent": 14}, {"command": 5, "file": 4, "line": 9, "parent": 9}, {"file": 7, "parent": 16}, {"command": 4, "file": 7, "line": 61, "parent": 17}, {"command": 5, "file": 4, "line": 9, "parent": 9}, {"file": 8, "parent": 19}, {"command": 4, "file": 8, "line": 61, "parent": 20}, {"command": 5, "file": 4, "line": 9, "parent": 9}, {"file": 9, "parent": 22}, {"command": 4, "file": 9, "line": 61, "parent": 23}, {"command": 5, "file": 4, "line": 9, "parent": 9}, {"file": 10, "parent": 25}, {"command": 4, "file": 10, "line": 61, "parent": 26}, {"command": 5, "file": 4, "line": 9, "parent": 9}, {"file": 11, "parent": 28}, {"command": 4, "file": 11, "line": 61, "parent": 29}, {"command": 5, "file": 4, "line": 9, "parent": 9}, {"file": 12, "parent": 31}, {"command": 4, "file": 12, "line": 61, "parent": 32}, {"command": 6, "file": 0, "line": 23, "parent": 0}, {"file": 19, "parent": 34}, {"command": 5, "file": 19, "line": 41, "parent": 35}, {"file": 18, "parent": 36}, {"command": 6, "file": 18, "line": 21, "parent": 37}, {"file": 17, "parent": 38}, {"command": 5, "file": 17, "line": 41, "parent": 39}, {"file": 16, "parent": 40}, {"command": 6, "file": 16, "line": 21, "parent": 41}, {"file": 15, "parent": 42}, {"command": 5, "file": 15, "line": 41, "parent": 43}, {"file": 14, "parent": 44}, {"command": 5, "file": 14, "line": 9, "parent": 45}, {"file": 13, "parent": 46}, {"command": 4, "file": 13, "line": 56, "parent": 47}, {"command": 5, "file": 15, "line": 41, "parent": 43}, {"file": 23, "parent": 49}, {"command": 6, "file": 23, "line": 21, "parent": 50}, {"file": 22, "parent": 51}, {"command": 5, "file": 22, "line": 41, "parent": 52}, {"file": 21, "parent": 53}, {"command": 5, "file": 21, "line": 9, "parent": 54}, {"file": 20, "parent": 55}, {"command": 4, "file": 20, "line": 56, "parent": 56}, {"command": 5, "file": 17, "line": 41, "parent": 39}, {"file": 25, "parent": 58}, {"command": 5, "file": 25, "line": 9, "parent": 59}, {"file": 24, "parent": 60}, {"command": 4, "file": 24, "line": 61, "parent": 61}, {"command": 6, "file": 0, "line": 21, "parent": 0}, {"file": 30, "parent": 63}, {"command": 5, "file": 30, "line": 41, "parent": 64}, {"file": 29, "parent": 65}, {"command": 6, "file": 29, "line": 33, "parent": 66}, {"file": 28, "parent": 67}, {"command": 5, "file": 28, "line": 41, "parent": 68}, {"file": 27, "parent": 69}, {"command": 5, "file": 27, "line": 9, "parent": 70}, {"file": 26, "parent": 71}, {"command": 4, "file": 26, "line": 56, "parent": 72}, {"command": 6, "file": 0, "line": 23, "parent": 0}, {"file": 33, "parent": 74}, {"command": 5, "file": 33, "line": 41, "parent": 75}, {"file": 32, "parent": 76}, {"command": 5, "file": 32, "line": 9, "parent": 77}, {"file": 31, "parent": 78}, {"command": 4, "file": 31, "line": 61, "parent": 79}, {"command": 6, "file": 29, "line": 30, "parent": 66}, {"file": 38, "parent": 81}, {"command": 5, "file": 38, "line": 41, "parent": 82}, {"file": 37, "parent": 83}, {"command": 6, "file": 37, "line": 21, "parent": 84}, {"file": 36, "parent": 85}, {"command": 5, "file": 36, "line": 41, "parent": 86}, {"file": 35, "parent": 87}, {"command": 5, "file": 35, "line": 9, "parent": 88}, {"file": 34, "parent": 89}, {"command": 4, "file": 34, "line": 56, "parent": 90}, {"command": 5, "file": 33, "line": 41, "parent": 75}, {"file": 42, "parent": 92}, {"command": 6, "file": 42, "line": 21, "parent": 93}, {"file": 41, "parent": 94}, {"command": 5, "file": 41, "line": 41, "parent": 95}, {"file": 40, "parent": 96}, {"command": 5, "file": 40, "line": 9, "parent": 97}, {"file": 39, "parent": 98}, {"command": 4, "file": 39, "line": 56, "parent": 99}, {"command": 5, "file": 36, "line": 41, "parent": 86}, {"file": 50, "parent": 101}, {"command": 6, "file": 50, "line": 21, "parent": 102}, {"file": 49, "parent": 103}, {"command": 5, "file": 49, "line": 41, "parent": 104}, {"file": 48, "parent": 105}, {"command": 6, "file": 48, "line": 21, "parent": 106}, {"file": 47, "parent": 107}, {"command": 5, "file": 47, "line": 41, "parent": 108}, {"file": 46, "parent": 109}, {"command": 6, "file": 46, "line": 21, "parent": 110}, {"file": 45, "parent": 111}, {"command": 5, "file": 45, "line": 41, "parent": 112}, {"file": 44, "parent": 113}, {"command": 5, "file": 44, "line": 9, "parent": 114}, {"file": 43, "parent": 115}, {"command": 4, "file": 43, "line": 56, "parent": 116}, {"command": 5, "file": 47, "line": 41, "parent": 108}, {"file": 52, "parent": 118}, {"command": 5, "file": 52, "line": 9, "parent": 119}, {"file": 51, "parent": 120}, {"command": 4, "file": 51, "line": 56, "parent": 121}, {"command": 6, "file": 29, "line": 29, "parent": 66}, {"file": 54, "parent": 123}, {"command": 5, "file": 54, "line": 37, "parent": 124}, {"file": 53, "parent": 125}, {"command": 4, "file": 53, "line": 66, "parent": 126}, {"command": 6, "file": 48, "line": 21, "parent": 106}, {"file": 59, "parent": 128}, {"command": 5, "file": 59, "line": 41, "parent": 129}, {"file": 58, "parent": 130}, {"command": 6, "file": 58, "line": 21, "parent": 131}, {"file": 57, "parent": 132}, {"command": 5, "file": 57, "line": 41, "parent": 133}, {"file": 56, "parent": 134}, {"command": 5, "file": 56, "line": 9, "parent": 135}, {"file": 55, "parent": 136}, {"command": 4, "file": 55, "line": 56, "parent": 137}, {"command": 6, "file": 58, "line": 21, "parent": 131}, {"file": 64, "parent": 139}, {"command": 5, "file": 64, "line": 41, "parent": 140}, {"file": 63, "parent": 141}, {"command": 6, "file": 63, "line": 21, "parent": 142}, {"file": 62, "parent": 143}, {"command": 5, "file": 62, "line": 41, "parent": 144}, {"file": 61, "parent": 145}, {"command": 5, "file": 61, "line": 9, "parent": 146}, {"file": 60, "parent": 147}, {"command": 4, "file": 60, "line": 56, "parent": 148}, {"command": 1, "file": 0, "line": 39, "parent": 0}, {"command": 7, "file": 0, "line": 5, "parent": 0}, {"command": 8, "file": 0, "line": 41, "parent": 0}, {"command": 9, "file": 0, "line": 36, "parent": 0}, {"command": 9, "file": 1, "line": 141, "parent": 2}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-O2 -g -DNDEBUG -fPIC"}, {"backtrace": 151, "fragment": "-Wall"}, {"backtrace": 151, "fragment": "-Wextra"}, {"backtrace": 151, "fragment": "-Wpedantic"}], "defines": [{"backtrace": 150, "define": "DEFAULT_RMW_IMPLEMENTATION=rmw_fastrtps_cpp"}, {"backtrace": 150, "define": "FMT_LOCALE"}, {"backtrace": 150, "define": "FMT_SHARED"}, {"backtrace": 152, "define": "PMAC_PRIMITIVE_CONTROLLER_BUILDING_DLL"}, {"backtrace": 150, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}, {"define": "pmac_primitive_controller_EXPORTS"}], "includes": [{"backtrace": 153, "path": "/home/<USER>/code/pmac_robot/src/pmac_primitive_controller/include"}, {"backtrace": 150, "path": "/home/<USER>/code/pmac_robot/build/pmac_primitive_controller/include"}, {"backtrace": 154, "isSystem": true, "path": "/home/<USER>/code/ros2_control/install/include/control_msgs"}, {"backtrace": 154, "isSystem": true, "path": "/home/<USER>/code/ros2_control/install/include/controller_interface"}, {"backtrace": 154, "isSystem": true, "path": "/home/<USER>/code/ros2_control/install/include/hardware_interface"}, {"backtrace": 154, "isSystem": true, "path": "/home/<USER>/code/ros2_control/install/include/realtime_tools"}, {"backtrace": 154, "isSystem": true, "path": "/opt/ros/humble/include/pluginlib"}, {"backtrace": 154, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp"}, {"backtrace": 154, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp_lifecycle"}, {"backtrace": 154, "isSystem": true, "path": "/opt/ros/humble/include/std_srvs"}, {"backtrace": 150, "isSystem": true, "path": "/opt/ros/humble/include/parameter_traits"}, {"backtrace": 150, "isSystem": true, "path": "/opt/ros/humble/include/ament_index_cpp"}, {"backtrace": 150, "isSystem": true, "path": "/opt/ros/humble/include/libstatistics_collector"}, {"backtrace": 150, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 150, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 150, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 150, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 150, "isSystem": true, "path": "/opt/ros/humble/include/fastcdr"}, {"backtrace": 150, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_cpp"}, {"backtrace": 150, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 150, "isSystem": true, "path": "/opt/ros/humble/include/rmw"}, {"backtrace": 150, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 150, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_c"}, {"backtrace": 150, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_cpp"}, {"backtrace": 150, "isSystem": true, "path": "/opt/ros/humble/include/rcl"}, {"backtrace": 150, "isSystem": true, "path": "/opt/ros/humble/include/rcl_interfaces"}, {"backtrace": 150, "isSystem": true, "path": "/opt/ros/humble/include/rcl_logging_interface"}, {"backtrace": 150, "isSystem": true, "path": "/opt/ros/humble/include/rcl_yaml_param_parser"}, {"backtrace": 150, "isSystem": true, "path": "/opt/ros/humble/include/libyaml_vendor"}, {"backtrace": 150, "isSystem": true, "path": "/opt/ros/humble/include/tracetools"}, {"backtrace": 150, "isSystem": true, "path": "/opt/ros/humble/include/rcpputils"}, {"backtrace": 150, "isSystem": true, "path": "/opt/ros/humble/include/statistics_msgs"}, {"backtrace": 150, "isSystem": true, "path": "/opt/ros/humble/include/rosgraph_msgs"}, {"backtrace": 150, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_cpp"}, {"backtrace": 150, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_c"}, {"backtrace": 150, "isSystem": true, "path": "/opt/ros/humble/include/rsl"}, {"backtrace": 150, "isSystem": true, "path": "/usr/include/eigen3"}, {"backtrace": 150, "isSystem": true, "path": "/opt/ros/humble/include"}, {"backtrace": 150, "isSystem": true, "path": "/opt/ros/humble/include/rcl_lifecycle"}, {"backtrace": 150, "isSystem": true, "path": "/opt/ros/humble/include/lifecycle_msgs"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/action_msgs"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/unique_identifier_msgs"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/geometry_msgs"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/sensor_msgs"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/trajectory_msgs"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/class_loader"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp_action"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rcl_action"}], "language": "CXX", "sourceIndexes": [0]}], "dependencies": [{"backtrace": 150, "id": "pmac_primitive_controller_parameters::@6890427a1f51a3e7e1df"}], "id": "pmac_primitive_controller::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-shared", "role": "flags"}, {"fragment": "-Wl,-rpath,/home/<USER>/code/ros2_control/install/lib:/opt/ros/humble/lib", "role": "libraries"}, {"backtrace": 3, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontroller_interface.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/home/<USER>/code/ros2_control/install/lib/libfake_components.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/home/<USER>/code/ros2_control/install/lib/libmock_components.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/home/<USER>/code/ros2_control/install/lib/libhardware_interface.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/librclcpp_lifecycle.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/home/<USER>/code/ros2_control/install/lib/librealtime_tools.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/home/<USER>/code/ros2_control/install/lib/libthread_priority.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libstd_srvs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libstd_srvs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libstd_srvs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libstd_srvs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libstd_srvs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libstd_srvs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librsl.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 15, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 15, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 15, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 15, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 18, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 18, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 18, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 18, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 27, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 27, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 27, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 27, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 48, "fragment": "/opt/ros/humble/lib/libclass_loader.so", "role": "libraries"}, {"backtrace": 57, "fragment": "/usr/lib/x86_64-linux-gnu/libconsole_bridge.so.1.0", "role": "libraries"}, {"backtrace": 62, "fragment": "/usr/lib/x86_64-linux-gnu/libtinyxml2.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/librcl_lifecycle.so", "role": "libraries"}, {"backtrace": 62, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 62, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 62, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 62, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 62, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 62, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 62, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 62, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 80, "fragment": "/opt/ros/humble/lib/librclcpp_action.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/librclcpp.so", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/liblibstatistics_collector.so", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 100, "fragment": "/opt/ros/humble/lib/librcl_action.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 15, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 15, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 18, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 18, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 27, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 27, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/librcl.so", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 18, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 15, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 27, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/librcl_yaml_param_parser.so", "role": "libraries"}, {"backtrace": 117, "fragment": "/opt/ros/humble/lib/libyaml.so", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/libtracetools.so", "role": "libraries"}, {"backtrace": 122, "fragment": "/opt/ros/humble/lib/librmw_implementation.so", "role": "libraries"}, {"backtrace": 48, "fragment": "/opt/ros/humble/lib/libament_index_cpp.so", "role": "libraries"}, {"backtrace": 122, "fragment": "/opt/ros/humble/lib/librcl_logging_spdlog.so", "role": "libraries"}, {"backtrace": 122, "fragment": "/opt/ros/humble/lib/librcl_logging_interface.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libfmt.so.8.1.1", "role": "libraries"}, {"backtrace": 127, "fragment": "-Wl,--as-needed", "role": "libraries"}, {"backtrace": 80, "fragment": "-lcap", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libfastcdr.so.1.0.24", "role": "libraries"}, {"backtrace": 15, "fragment": "/opt/ros/humble/lib/librmw.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 18, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libstd_srvs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libstd_srvs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 138, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 62, "fragment": "/opt/ros/humble/lib/librcpputils.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"backtrace": 149, "fragment": "-ldl", "role": "libraries"}, {"backtrace": 27, "fragment": "/usr/lib/x86_64-linux-gnu/libpython3.10.so", "role": "libraries"}], "language": "CXX"}, "name": "pmac_primitive_controller", "nameOnDisk": "libpmac_primitive_controller.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/pmac_primitive_controller.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}