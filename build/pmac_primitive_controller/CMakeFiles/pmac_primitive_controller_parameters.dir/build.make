# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/code/pmac_robot/src/pmac_primitive_controller

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/code/pmac_robot/build/pmac_primitive_controller

# Utility rule file for pmac_primitive_controller_parameters.

# Include any custom commands dependencies for this target.
include CMakeFiles/pmac_primitive_controller_parameters.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/pmac_primitive_controller_parameters.dir/progress.make

include/pmac_primitive_controller/pmac_primitive_controller_parameters.hpp: /home/<USER>/code/pmac_robot/src/pmac_primitive_controller/src/pmac_primitive_controller.yaml
include/pmac_primitive_controller/pmac_primitive_controller_parameters.hpp: include/pmac_primitive_controller/validate_pmac_primitive_controller_parameters.hpp
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/pmac_robot/build/pmac_primitive_controller/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Running \`/opt/ros/humble/bin/generate_parameter_library_cpp /home/<USER>/code/pmac_robot/build/pmac_primitive_controller/include/pmac_primitive_controller/pmac_primitive_controller_parameters.hpp /home/<USER>/code/pmac_robot/src/pmac_primitive_controller/src/pmac_primitive_controller.yaml validate_pmac_primitive_controller_parameters.hpp\`"
	/opt/ros/humble/bin/generate_parameter_library_cpp /home/<USER>/code/pmac_robot/build/pmac_primitive_controller/include/pmac_primitive_controller/pmac_primitive_controller_parameters.hpp /home/<USER>/code/pmac_robot/src/pmac_primitive_controller/src/pmac_primitive_controller.yaml validate_pmac_primitive_controller_parameters.hpp

include/pmac_primitive_controller_parameters.hpp: include/pmac_primitive_controller/pmac_primitive_controller_parameters.hpp
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/code/pmac_robot/build/pmac_primitive_controller/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Creating deprecated header file /home/<USER>/code/pmac_robot/build/pmac_primitive_controller/include/pmac_primitive_controller_parameters.hpp"
	/usr/bin/cmake -E cat /home/<USER>/code/pmac_robot/build/pmac_primitive_controller/pmac_primitive_controller_parameters_pragma_warning /home/<USER>/code/pmac_robot/build/pmac_primitive_controller/include/pmac_primitive_controller/pmac_primitive_controller_parameters.hpp > /home/<USER>/code/pmac_robot/build/pmac_primitive_controller/include/pmac_primitive_controller_parameters.hpp

CMakeFiles/pmac_primitive_controller_parameters.dir/codegen:
.PHONY : CMakeFiles/pmac_primitive_controller_parameters.dir/codegen

pmac_primitive_controller_parameters: include/pmac_primitive_controller/pmac_primitive_controller_parameters.hpp
pmac_primitive_controller_parameters: include/pmac_primitive_controller_parameters.hpp
pmac_primitive_controller_parameters: CMakeFiles/pmac_primitive_controller_parameters.dir/build.make
.PHONY : pmac_primitive_controller_parameters

# Rule to build all files generated by this target.
CMakeFiles/pmac_primitive_controller_parameters.dir/build: pmac_primitive_controller_parameters
.PHONY : CMakeFiles/pmac_primitive_controller_parameters.dir/build

CMakeFiles/pmac_primitive_controller_parameters.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/pmac_primitive_controller_parameters.dir/cmake_clean.cmake
.PHONY : CMakeFiles/pmac_primitive_controller_parameters.dir/clean

CMakeFiles/pmac_primitive_controller_parameters.dir/depend:
	cd /home/<USER>/code/pmac_robot/build/pmac_primitive_controller && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/code/pmac_robot/src/pmac_primitive_controller /home/<USER>/code/pmac_robot/src/pmac_primitive_controller /home/<USER>/code/pmac_robot/build/pmac_primitive_controller /home/<USER>/code/pmac_robot/build/pmac_primitive_controller /home/<USER>/code/pmac_robot/build/pmac_primitive_controller/CMakeFiles/pmac_primitive_controller_parameters.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/pmac_primitive_controller_parameters.dir/depend

