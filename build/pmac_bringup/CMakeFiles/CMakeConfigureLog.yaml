
---
events:
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineSystem.cmake:12 (find_program)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_UNAME"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "uname"
    candidate_directories:
      - "/home/<USER>/code/pmac_robot/install/bin/"
      - "/home/<USER>/code/pmac_robot/install/sbin/"
      - "/home/<USER>/code/pmac_robot/install/"
      - "/home/<USER>/code/ros2_control/install/bin/"
      - "/home/<USER>/code/ros2_control/install/sbin/"
      - "/home/<USER>/code/ros2_control/install/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_manager/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_manager/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_manager/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_msgs/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_msgs/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_msgs/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_interface/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_interface/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_interface/"
      - "/home/<USER>/code/ws_tools/install/ament_lint_auto/bin/"
      - "/home/<USER>/code/ws_tools/install/ament_lint_auto/sbin/"
      - "/home/<USER>/code/ws_tools/install/ament_lint_auto/"
      - "/opt/ros/humble/bin/"
      - "/opt/ros/humble/sbin/"
      - "/opt/ros/humble/"
      - "/home/<USER>/code/staubli_experimental/install/bin/"
      - "/home/<USER>/code/staubli_experimental/install/sbin/"
      - "/home/<USER>/code/staubli_experimental/install/"
      - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description/bin/"
      - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description/sbin/"
      - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description/"
      - "/opt/ros/humble/bin/"
      - "/home/<USER>/.local/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/usr/bin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
      - "/home/<USER>/.fzf/bin/"
    searched_directories:
      - "/home/<USER>/code/pmac_robot/install/bin/uname"
      - "/home/<USER>/code/pmac_robot/install/sbin/uname"
      - "/home/<USER>/code/pmac_robot/install/uname"
      - "/home/<USER>/code/ros2_control/install/bin/uname"
      - "/home/<USER>/code/ros2_control/install/sbin/uname"
      - "/home/<USER>/code/ros2_control/install/uname"
      - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/bin/uname"
      - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/sbin/uname"
      - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/uname"
      - "/home/<USER>/code/ecat_ws/install/ethercat_manager/bin/uname"
      - "/home/<USER>/code/ecat_ws/install/ethercat_manager/sbin/uname"
      - "/home/<USER>/code/ecat_ws/install/ethercat_manager/uname"
      - "/home/<USER>/code/ecat_ws/install/ethercat_msgs/bin/uname"
      - "/home/<USER>/code/ecat_ws/install/ethercat_msgs/sbin/uname"
      - "/home/<USER>/code/ecat_ws/install/ethercat_msgs/uname"
      - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/bin/uname"
      - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/sbin/uname"
      - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/uname"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/bin/uname"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/sbin/uname"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/uname"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/bin/uname"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/sbin/uname"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/uname"
      - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/bin/uname"
      - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/sbin/uname"
      - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/uname"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/bin/uname"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/sbin/uname"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/uname"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/bin/uname"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/sbin/uname"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/uname"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver/bin/uname"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver/sbin/uname"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver/uname"
      - "/home/<USER>/code/ecat_ws/install/ethercat_interface/bin/uname"
      - "/home/<USER>/code/ecat_ws/install/ethercat_interface/sbin/uname"
      - "/home/<USER>/code/ecat_ws/install/ethercat_interface/uname"
      - "/home/<USER>/code/ws_tools/install/ament_lint_auto/bin/uname"
      - "/home/<USER>/code/ws_tools/install/ament_lint_auto/sbin/uname"
      - "/home/<USER>/code/ws_tools/install/ament_lint_auto/uname"
      - "/opt/ros/humble/bin/uname"
      - "/opt/ros/humble/sbin/uname"
      - "/opt/ros/humble/uname"
      - "/home/<USER>/code/staubli_experimental/install/bin/uname"
      - "/home/<USER>/code/staubli_experimental/install/sbin/uname"
      - "/home/<USER>/code/staubli_experimental/install/uname"
      - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description/bin/uname"
      - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description/sbin/uname"
      - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description/uname"
      - "/opt/ros/humble/bin/uname"
      - "/home/<USER>/.local/bin/uname"
      - "/usr/local/sbin/uname"
      - "/usr/local/bin/uname"
      - "/usr/sbin/uname"
    found: "/usr/bin/uname"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineSystem.cmake:212 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Linux - 6.1.134-rt51 - x86_64
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake-4.1/Modules/CMakeUnixFindMake.cmake:5 (find_program)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_MAKE_PROGRAM"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gmake"
      - "make"
      - "smake"
    candidate_directories:
      - "/home/<USER>/code/pmac_robot/install/bin/"
      - "/home/<USER>/code/pmac_robot/install/sbin/"
      - "/home/<USER>/code/pmac_robot/install/"
      - "/home/<USER>/code/ros2_control/install/bin/"
      - "/home/<USER>/code/ros2_control/install/sbin/"
      - "/home/<USER>/code/ros2_control/install/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_manager/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_manager/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_manager/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_msgs/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_msgs/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_msgs/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_interface/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_interface/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_interface/"
      - "/home/<USER>/code/ws_tools/install/ament_lint_auto/bin/"
      - "/home/<USER>/code/ws_tools/install/ament_lint_auto/sbin/"
      - "/home/<USER>/code/ws_tools/install/ament_lint_auto/"
      - "/opt/ros/humble/bin/"
      - "/opt/ros/humble/sbin/"
      - "/opt/ros/humble/"
      - "/home/<USER>/code/staubli_experimental/install/bin/"
      - "/home/<USER>/code/staubli_experimental/install/sbin/"
      - "/home/<USER>/code/staubli_experimental/install/"
      - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description/bin/"
      - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description/sbin/"
      - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description/"
      - "/opt/ros/humble/bin/"
      - "/home/<USER>/.local/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/usr/bin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
      - "/home/<USER>/.fzf/bin/"
    searched_directories:
      - "/home/<USER>/code/pmac_robot/install/bin/gmake"
      - "/home/<USER>/code/pmac_robot/install/sbin/gmake"
      - "/home/<USER>/code/pmac_robot/install/gmake"
      - "/home/<USER>/code/ros2_control/install/bin/gmake"
      - "/home/<USER>/code/ros2_control/install/sbin/gmake"
      - "/home/<USER>/code/ros2_control/install/gmake"
      - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/bin/gmake"
      - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/sbin/gmake"
      - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/gmake"
      - "/home/<USER>/code/ecat_ws/install/ethercat_manager/bin/gmake"
      - "/home/<USER>/code/ecat_ws/install/ethercat_manager/sbin/gmake"
      - "/home/<USER>/code/ecat_ws/install/ethercat_manager/gmake"
      - "/home/<USER>/code/ecat_ws/install/ethercat_msgs/bin/gmake"
      - "/home/<USER>/code/ecat_ws/install/ethercat_msgs/sbin/gmake"
      - "/home/<USER>/code/ecat_ws/install/ethercat_msgs/gmake"
      - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/bin/gmake"
      - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/sbin/gmake"
      - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/gmake"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/bin/gmake"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/sbin/gmake"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/gmake"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/bin/gmake"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/sbin/gmake"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/gmake"
      - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/bin/gmake"
      - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/sbin/gmake"
      - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/gmake"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/bin/gmake"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/sbin/gmake"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/gmake"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/bin/gmake"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/sbin/gmake"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/gmake"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver/bin/gmake"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver/sbin/gmake"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver/gmake"
      - "/home/<USER>/code/ecat_ws/install/ethercat_interface/bin/gmake"
      - "/home/<USER>/code/ecat_ws/install/ethercat_interface/sbin/gmake"
      - "/home/<USER>/code/ecat_ws/install/ethercat_interface/gmake"
      - "/home/<USER>/code/ws_tools/install/ament_lint_auto/bin/gmake"
      - "/home/<USER>/code/ws_tools/install/ament_lint_auto/sbin/gmake"
      - "/home/<USER>/code/ws_tools/install/ament_lint_auto/gmake"
      - "/opt/ros/humble/bin/gmake"
      - "/opt/ros/humble/sbin/gmake"
      - "/opt/ros/humble/gmake"
      - "/home/<USER>/code/staubli_experimental/install/bin/gmake"
      - "/home/<USER>/code/staubli_experimental/install/sbin/gmake"
      - "/home/<USER>/code/staubli_experimental/install/gmake"
      - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description/bin/gmake"
      - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description/sbin/gmake"
      - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description/gmake"
      - "/opt/ros/humble/bin/gmake"
      - "/home/<USER>/.local/bin/gmake"
      - "/usr/local/sbin/gmake"
      - "/usr/local/bin/gmake"
      - "/usr/sbin/gmake"
    found: "/usr/bin/gmake"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCompiler.cmake:73 (find_program)"
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:64 (_cmake_find_compiler)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_C_COMPILER"
    description: "C compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "cc"
      - "gcc"
      - "cl"
      - "bcc"
      - "xlc"
      - "icx"
      - "clang"
    candidate_directories:
      - "/home/<USER>/code/pmac_robot/install/bin/"
      - "/home/<USER>/code/pmac_robot/install/sbin/"
      - "/home/<USER>/code/pmac_robot/install/"
      - "/home/<USER>/code/ros2_control/install/bin/"
      - "/home/<USER>/code/ros2_control/install/sbin/"
      - "/home/<USER>/code/ros2_control/install/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_manager/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_manager/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_manager/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_msgs/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_msgs/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_msgs/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_interface/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_interface/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_interface/"
      - "/home/<USER>/code/ws_tools/install/ament_lint_auto/bin/"
      - "/home/<USER>/code/ws_tools/install/ament_lint_auto/sbin/"
      - "/home/<USER>/code/ws_tools/install/ament_lint_auto/"
      - "/opt/ros/humble/bin/"
      - "/opt/ros/humble/sbin/"
      - "/opt/ros/humble/"
      - "/home/<USER>/code/staubli_experimental/install/bin/"
      - "/home/<USER>/code/staubli_experimental/install/sbin/"
      - "/home/<USER>/code/staubli_experimental/install/"
      - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description/bin/"
      - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description/sbin/"
      - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description/"
      - "/opt/ros/humble/bin/"
      - "/home/<USER>/.local/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/usr/bin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
      - "/home/<USER>/.fzf/bin/"
    searched_directories:
      - "/home/<USER>/code/pmac_robot/install/bin/cc"
      - "/home/<USER>/code/pmac_robot/install/sbin/cc"
      - "/home/<USER>/code/pmac_robot/install/cc"
      - "/home/<USER>/code/ros2_control/install/bin/cc"
      - "/home/<USER>/code/ros2_control/install/sbin/cc"
      - "/home/<USER>/code/ros2_control/install/cc"
      - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/bin/cc"
      - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/sbin/cc"
      - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/cc"
      - "/home/<USER>/code/ecat_ws/install/ethercat_manager/bin/cc"
      - "/home/<USER>/code/ecat_ws/install/ethercat_manager/sbin/cc"
      - "/home/<USER>/code/ecat_ws/install/ethercat_manager/cc"
      - "/home/<USER>/code/ecat_ws/install/ethercat_msgs/bin/cc"
      - "/home/<USER>/code/ecat_ws/install/ethercat_msgs/sbin/cc"
      - "/home/<USER>/code/ecat_ws/install/ethercat_msgs/cc"
      - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/bin/cc"
      - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/sbin/cc"
      - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/cc"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/bin/cc"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/sbin/cc"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/cc"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/bin/cc"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/sbin/cc"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/cc"
      - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/bin/cc"
      - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/sbin/cc"
      - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/cc"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/bin/cc"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/sbin/cc"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/cc"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/bin/cc"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/sbin/cc"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/cc"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver/bin/cc"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver/sbin/cc"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver/cc"
      - "/home/<USER>/code/ecat_ws/install/ethercat_interface/bin/cc"
      - "/home/<USER>/code/ecat_ws/install/ethercat_interface/sbin/cc"
      - "/home/<USER>/code/ecat_ws/install/ethercat_interface/cc"
      - "/home/<USER>/code/ws_tools/install/ament_lint_auto/bin/cc"
      - "/home/<USER>/code/ws_tools/install/ament_lint_auto/sbin/cc"
      - "/home/<USER>/code/ws_tools/install/ament_lint_auto/cc"
      - "/opt/ros/humble/bin/cc"
      - "/opt/ros/humble/sbin/cc"
      - "/opt/ros/humble/cc"
      - "/home/<USER>/code/staubli_experimental/install/bin/cc"
      - "/home/<USER>/code/staubli_experimental/install/sbin/cc"
      - "/home/<USER>/code/staubli_experimental/install/cc"
      - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description/bin/cc"
      - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description/sbin/cc"
      - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description/cc"
      - "/opt/ros/humble/bin/cc"
      - "/home/<USER>/.local/bin/cc"
      - "/usr/local/sbin/cc"
      - "/usr/local/bin/cc"
      - "/usr/sbin/cc"
    found: "/usr/bin/cc"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "/usr/share/cmake-4.1/Modules/"
    found: "/usr/share/cmake-4.1/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: /usr/bin/cc 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out"
      
      The C compiler identification is GNU, found in:
        /home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/4.1.1/CompilerIdC/a.out
      
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_AR"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ar"
    candidate_directories:
      - "/usr/bin/"
      - "/opt/ros/humble/bin/"
      - "/home/<USER>/.local/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
      - "/home/<USER>/.fzf/bin/"
    found: "/usr/bin/ar"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_RANLIB"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ranlib"
    candidate_directories:
      - "/usr/bin/"
      - "/opt/ros/humble/bin/"
      - "/home/<USER>/.local/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
      - "/home/<USER>/.fzf/bin/"
    found: "/usr/bin/ranlib"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_STRIP"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "strip"
    candidate_directories:
      - "/usr/bin/"
      - "/opt/ros/humble/bin/"
      - "/home/<USER>/.local/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
      - "/home/<USER>/.fzf/bin/"
    found: "/usr/bin/strip"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_LINKER"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ld"
    candidate_directories:
      - "/usr/bin/"
      - "/opt/ros/humble/bin/"
      - "/home/<USER>/.local/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
      - "/home/<USER>/.fzf/bin/"
    found: "/usr/bin/ld"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_NM"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "nm"
    candidate_directories:
      - "/usr/bin/"
      - "/opt/ros/humble/bin/"
      - "/home/<USER>/.local/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
      - "/home/<USER>/.fzf/bin/"
    found: "/usr/bin/nm"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_OBJDUMP"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "objdump"
    candidate_directories:
      - "/usr/bin/"
      - "/opt/ros/humble/bin/"
      - "/home/<USER>/.local/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
      - "/home/<USER>/.fzf/bin/"
    found: "/usr/bin/objdump"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_OBJCOPY"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "objcopy"
    candidate_directories:
      - "/usr/bin/"
      - "/opt/ros/humble/bin/"
      - "/home/<USER>/.local/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
      - "/home/<USER>/.fzf/bin/"
    found: "/usr/bin/objcopy"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_READELF"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "readelf"
    candidate_directories:
      - "/usr/bin/"
      - "/opt/ros/humble/bin/"
      - "/home/<USER>/.local/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
      - "/home/<USER>/.fzf/bin/"
    found: "/usr/bin/readelf"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_DLLTOOL"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "dlltool"
    candidate_directories:
      - "/usr/bin/"
      - "/opt/ros/humble/bin/"
      - "/home/<USER>/.local/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
      - "/home/<USER>/.fzf/bin/"
    searched_directories:
      - "/usr/bin/dlltool"
      - "/opt/ros/humble/bin/dlltool"
      - "/home/<USER>/.local/bin/dlltool"
      - "/usr/local/sbin/dlltool"
      - "/usr/local/bin/dlltool"
      - "/usr/sbin/dlltool"
      - "/sbin/dlltool"
      - "/bin/dlltool"
      - "/usr/games/dlltool"
      - "/usr/local/games/dlltool"
      - "/snap/bin/dlltool"
      - "/home/<USER>/.fzf/bin/dlltool"
    found: false
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_ADDR2LINE"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "addr2line"
    candidate_directories:
      - "/usr/bin/"
      - "/opt/ros/humble/bin/"
      - "/home/<USER>/.local/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
      - "/home/<USER>/.fzf/bin/"
    found: "/usr/bin/addr2line"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_TAPI"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "tapi"
    candidate_directories:
      - "/usr/bin/"
      - "/opt/ros/humble/bin/"
      - "/home/<USER>/.local/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
      - "/home/<USER>/.fzf/bin/"
    searched_directories:
      - "/usr/bin/tapi"
      - "/opt/ros/humble/bin/tapi"
      - "/home/<USER>/.local/bin/tapi"
      - "/usr/local/sbin/tapi"
      - "/usr/local/bin/tapi"
      - "/usr/sbin/tapi"
      - "/sbin/tapi"
      - "/bin/tapi"
      - "/usr/games/tapi"
      - "/usr/local/games/tapi"
      - "/snap/bin/tapi"
      - "/home/<USER>/.fzf/bin/tapi"
    found: false
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:18 (find_program)"
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:201 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_C_COMPILER_AR"
    description: "A wrapper around 'ar' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ar-11.4"
      - "gcc-ar-11"
      - "gcc-ar11"
      - "gcc-ar"
    candidate_directories:
      - "/usr/bin/"
      - "/opt/ros/humble/bin/"
      - "/home/<USER>/.local/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
      - "/home/<USER>/.fzf/bin/"
    searched_directories:
      - "/usr/bin/gcc-ar-11.4"
      - "/opt/ros/humble/bin/gcc-ar-11.4"
      - "/home/<USER>/.local/bin/gcc-ar-11.4"
      - "/usr/local/sbin/gcc-ar-11.4"
      - "/usr/local/bin/gcc-ar-11.4"
      - "/usr/sbin/gcc-ar-11.4"
      - "/sbin/gcc-ar-11.4"
      - "/bin/gcc-ar-11.4"
      - "/usr/games/gcc-ar-11.4"
      - "/usr/local/games/gcc-ar-11.4"
      - "/snap/bin/gcc-ar-11.4"
      - "/home/<USER>/.fzf/bin/gcc-ar-11.4"
    found: "/usr/bin/gcc-ar-11"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:30 (find_program)"
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:201 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_C_COMPILER_RANLIB"
    description: "A wrapper around 'ranlib' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ranlib-11.4"
      - "gcc-ranlib-11"
      - "gcc-ranlib11"
      - "gcc-ranlib"
    candidate_directories:
      - "/usr/bin/"
      - "/opt/ros/humble/bin/"
      - "/home/<USER>/.local/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
      - "/home/<USER>/.fzf/bin/"
    searched_directories:
      - "/usr/bin/gcc-ranlib-11.4"
      - "/opt/ros/humble/bin/gcc-ranlib-11.4"
      - "/home/<USER>/.local/bin/gcc-ranlib-11.4"
      - "/usr/local/sbin/gcc-ranlib-11.4"
      - "/usr/local/bin/gcc-ranlib-11.4"
      - "/usr/sbin/gcc-ranlib-11.4"
      - "/sbin/gcc-ranlib-11.4"
      - "/bin/gcc-ranlib-11.4"
      - "/usr/games/gcc-ranlib-11.4"
      - "/usr/local/games/gcc-ranlib-11.4"
      - "/snap/bin/gcc-ranlib-11.4"
      - "/home/<USER>/.fzf/bin/gcc-ranlib-11.4"
    found: "/usr/bin/gcc-ranlib-11"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCompiler.cmake:54 (find_program)"
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:69 (_cmake_find_compiler)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_CXX_COMPILER"
    description: "CXX compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "c++"
      - "CC"
      - "g++"
      - "aCC"
      - "cl"
      - "bcc"
      - "xlC"
      - "icpx"
      - "icx"
      - "clang++"
    candidate_directories:
      - "/usr/bin/"
    found: "/usr/bin/c++"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "/usr/share/cmake-4.1/Modules/"
    found: "/usr/share/cmake-4.1/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: /usr/bin/c++ 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out"
      
      The CXX compiler identification is GNU, found in:
        /home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/4.1.1/CompilerIdCXX/a.out
      
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:18 (find_program)"
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:207 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_CXX_COMPILER_AR"
    description: "A wrapper around 'ar' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ar-11.4"
      - "gcc-ar-11"
      - "gcc-ar11"
      - "gcc-ar"
    candidate_directories:
      - "/usr/bin/"
      - "/opt/ros/humble/bin/"
      - "/home/<USER>/.local/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
      - "/home/<USER>/.fzf/bin/"
    searched_directories:
      - "/usr/bin/gcc-ar-11.4"
      - "/opt/ros/humble/bin/gcc-ar-11.4"
      - "/home/<USER>/.local/bin/gcc-ar-11.4"
      - "/usr/local/sbin/gcc-ar-11.4"
      - "/usr/local/bin/gcc-ar-11.4"
      - "/usr/sbin/gcc-ar-11.4"
      - "/sbin/gcc-ar-11.4"
      - "/bin/gcc-ar-11.4"
      - "/usr/games/gcc-ar-11.4"
      - "/usr/local/games/gcc-ar-11.4"
      - "/snap/bin/gcc-ar-11.4"
      - "/home/<USER>/.fzf/bin/gcc-ar-11.4"
    found: "/usr/bin/gcc-ar-11"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:30 (find_program)"
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:207 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_CXX_COMPILER_RANLIB"
    description: "A wrapper around 'ranlib' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ranlib-11.4"
      - "gcc-ranlib-11"
      - "gcc-ranlib11"
      - "gcc-ranlib"
    candidate_directories:
      - "/usr/bin/"
      - "/opt/ros/humble/bin/"
      - "/home/<USER>/.local/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
      - "/home/<USER>/.fzf/bin/"
    searched_directories:
      - "/usr/bin/gcc-ranlib-11.4"
      - "/opt/ros/humble/bin/gcc-ranlib-11.4"
      - "/home/<USER>/.local/bin/gcc-ranlib-11.4"
      - "/usr/local/sbin/gcc-ranlib-11.4"
      - "/usr/local/bin/gcc-ranlib-11.4"
      - "/usr/sbin/gcc-ranlib-11.4"
      - "/sbin/gcc-ranlib-11.4"
      - "/bin/gcc-ranlib-11.4"
      - "/usr/games/gcc-ranlib-11.4"
      - "/usr/local/games/gcc-ranlib-11.4"
      - "/snap/bin/gcc-ranlib-11.4"
      - "/home/<USER>/.fzf/bin/gcc-ranlib-11.4"
    found: "/usr/bin/gcc-ranlib-11"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "/usr/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/CMakeScratch/TryCompile-xSZaVb"
      binary: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/CMakeScratch/TryCompile-xSZaVb"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/CMakeScratch/TryCompile-xSZaVb'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_d42f8/fast
        /usr/bin/gmake  -f CMakeFiles/cmTC_d42f8.dir/build.make CMakeFiles/cmTC_d42f8.dir/build
        gmake[1]: Entering directory '/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/CMakeScratch/TryCompile-xSZaVb'
        Building C object CMakeFiles/cmTC_d42f8.dir/CMakeCCompilerABI.c.o
        /usr/bin/cc   -v -o CMakeFiles/cmTC_d42f8.dir/CMakeCCompilerABI.c.o -c /usr/share/cmake-4.1/Modules/CMakeCCompilerABI.c
        Using built-in specs.
        COLLECT_GCC=/usr/bin/cc
        OFFLOAD_TARGET_NAMES=nvptx-none:amdgcn-amdhsa
        OFFLOAD_TARGET_DEFAULT=1
        Target: x86_64-linux-gnu
        Configured with: ../src/configure -v --with-pkgversion='Ubuntu 11.4.0-1ubuntu1~22.04.2' --with-bugurl=file:///usr/share/doc/gcc-11/README.Bugs --enable-languages=c,ada,c++,go,brig,d,fortran,objc,obj-c++,m2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-11 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --enable-cet --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-11-2Y5pKs/gcc-11-11.4.0/debian/tmp-nvptx/usr,amdgcn-amdhsa=/build/gcc-11-2Y5pKs/gcc-11-11.4.0/debian/tmp-gcn/usr --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu --with-build-config=bootstrap-lto-lean --enable-link-serialization=2
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 11.4.0 (Ubuntu 11.4.0-1ubuntu1~22.04.2) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_d42f8.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_d42f8.dir/'
         /usr/lib/gcc/x86_64-linux-gnu/11/cc1 -quiet -v -imultiarch x86_64-linux-gnu /usr/share/cmake-4.1/Modules/CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles/cmTC_d42f8.dir/ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mtune=generic -march=x86-64 -version -fasynchronous-unwind-tables -fstack-protector-strong -Wformat -Wformat-security -fstack-clash-protection -fcf-protection -o /tmp/ccUyu6Cw.s
        GNU C17 (Ubuntu 11.4.0-1ubuntu1~22.04.2) version 11.4.0 (x86_64-linux-gnu)
        	compiled by GNU C version 11.4.0, GMP version 6.2.1, MPFR version 4.1.0, MPC version 1.2.1, isl version isl-0.24-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring nonexistent directory "/usr/local/include/x86_64-linux-gnu"
        ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/11/include-fixed"
        ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/11/../../../../x86_64-linux-gnu/include"
        #include "..." search starts here:
        #include <...> search starts here:
         /usr/lib/gcc/x86_64-linux-gnu/11/include
         /usr/local/include
         /usr/include/x86_64-linux-gnu
         /usr/include
        End of search list.
        GNU C17 (Ubuntu 11.4.0-1ubuntu1~22.04.2) version 11.4.0 (x86_64-linux-gnu)
        	compiled by GNU C version 11.4.0, GMP version 6.2.1, MPFR version 4.1.0, MPC version 1.2.1, isl version isl-0.24-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        Compiler executable checksum: 4011c2103cba78949d7e02d0f0047a3d
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_d42f8.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_d42f8.dir/'
         as -v --64 -o CMakeFiles/cmTC_d42f8.dir/CMakeCCompilerABI.c.o /tmp/ccUyu6Cw.s
        GNU assembler version 2.38 (x86_64-linux-gnu) using BFD version (GNU Binutils for Ubuntu) 2.38
        COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/11/:/usr/lib/gcc/x86_64-linux-gnu/11/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/11/:/usr/lib/gcc/x86_64-linux-gnu/
        LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/11/:/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/11/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/11/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_d42f8.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_d42f8.dir/CMakeCCompilerABI.c.'
        Linking C executable cmTC_d42f8
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_d42f8.dir/link.txt --verbose=1
        Using built-in specs.
        COLLECT_GCC=/usr/bin/cc
        COLLECT_LTO_WRAPPER=/usr/lib/gcc/x86_64-linux-gnu/11/lto-wrapper
        OFFLOAD_TARGET_NAMES=nvptx-none:amdgcn-amdhsa
        OFFLOAD_TARGET_DEFAULT=1
        Target: x86_64-linux-gnu
        Configured with: ../src/configure -v --with-pkgversion='Ubuntu 11.4.0-1ubuntu1~22.04.2' --with-bugurl=file:///usr/share/doc/gcc-11/README.Bugs --enable-languages=c,ada,c++,go,brig,d,fortran,objc,obj-c++,m2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-11 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --enable-cet --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-11-2Y5pKs/gcc-11-11.4.0/debian/tmp-nvptx/usr,amdgcn-amdhsa=/build/gcc-11-2Y5pKs/gcc-11-11.4.0/debian/tmp-gcn/usr --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu --with-build-config=bootstrap-lto-lean --enable-link-serialization=2
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 11.4.0 (Ubuntu 11.4.0-1ubuntu1~22.04.2) 
        COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/11/:/usr/lib/gcc/x86_64-linux-gnu/11/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/11/:/usr/lib/gcc/x86_64-linux-gnu/
        LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/11/:/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/11/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/11/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_d42f8' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_d42f8.'
         /usr/lib/gcc/x86_64-linux-gnu/11/collect2 -plugin /usr/lib/gcc/x86_64-linux-gnu/11/liblto_plugin.so -plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/11/lto-wrapper -plugin-opt=-fresolution=/tmp/cc2VBkoE.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -pie -z now -z relro -o cmTC_d42f8 /usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/Scrt1.o /usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/11/crtbeginS.o -L/usr/lib/gcc/x86_64-linux-gnu/11 -L/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/11/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/11/../../.. -v CMakeFiles/cmTC_d42f8.dir/CMakeCCompilerABI.c.o -lgcc --push-state --as-needed -lgcc_s --pop-state -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /usr/lib/gcc/x86_64-linux-gnu/11/crtendS.o /usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/crtn.o
        collect2 version 11.4.0
        /usr/bin/ld -plugin /usr/lib/gcc/x86_64-linux-gnu/11/liblto_plugin.so -plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/11/lto-wrapper -plugin-opt=-fresolution=/tmp/cc2VBkoE.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -pie -z now -z relro -o cmTC_d42f8 /usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/Scrt1.o /usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/11/crtbeginS.o -L/usr/lib/gcc/x86_64-linux-gnu/11 -L/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/11/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/11/../../.. -v CMakeFiles/cmTC_d42f8.dir/CMakeCCompilerABI.c.o -lgcc --push-state --as-needed -lgcc_s --pop-state -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /usr/lib/gcc/x86_64-linux-gnu/11/crtendS.o /usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/crtn.o
        GNU ld (GNU Binutils for Ubuntu) 2.38
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_d42f8' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_d42f8.'
        /usr/bin/cc  -v -Wl,-v CMakeFiles/cmTC_d42f8.dir/CMakeCCompilerABI.c.o -o cmTC_d42f8
        gmake[1]: Leaving directory '/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/CMakeScratch/TryCompile-xSZaVb'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:217 (message)"
      - "/usr/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/usr/lib/gcc/x86_64-linux-gnu/11/include]
          add: [/usr/local/include]
          add: [/usr/include/x86_64-linux-gnu]
          add: [/usr/include]
        end of search list found
        collapse include dir [/usr/lib/gcc/x86_64-linux-gnu/11/include] ==> [/usr/lib/gcc/x86_64-linux-gnu/11/include]
        collapse include dir [/usr/local/include] ==> [/usr/local/include]
        collapse include dir [/usr/include/x86_64-linux-gnu] ==> [/usr/include/x86_64-linux-gnu]
        collapse include dir [/usr/include] ==> [/usr/include]
        implicit include dirs: [/usr/lib/gcc/x86_64-linux-gnu/11/include;/usr/local/include;/usr/include/x86_64-linux-gnu;/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "/usr/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: '/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/CMakeScratch/TryCompile-xSZaVb']
        ignore line: []
        ignore line: [Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_d42f8/fast]
        ignore line: [/usr/bin/gmake  -f CMakeFiles/cmTC_d42f8.dir/build.make CMakeFiles/cmTC_d42f8.dir/build]
        ignore line: [gmake[1]: Entering directory '/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/CMakeScratch/TryCompile-xSZaVb']
        ignore line: [Building C object CMakeFiles/cmTC_d42f8.dir/CMakeCCompilerABI.c.o]
        ignore line: [/usr/bin/cc   -v -o CMakeFiles/cmTC_d42f8.dir/CMakeCCompilerABI.c.o -c /usr/share/cmake-4.1/Modules/CMakeCCompilerABI.c]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/usr/bin/cc]
        ignore line: [OFFLOAD_TARGET_NAMES=nvptx-none:amdgcn-amdhsa]
        ignore line: [OFFLOAD_TARGET_DEFAULT=1]
        ignore line: [Target: x86_64-linux-gnu]
        ignore line: [Configured with: ../src/configure -v --with-pkgversion='Ubuntu 11.4.0-1ubuntu1~22.04.2' --with-bugurl=file:///usr/share/doc/gcc-11/README.Bugs --enable-languages=c ada c++ go brig d fortran objc obj-c++ m2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-11 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --enable-cet --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32 m64 mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-11-2Y5pKs/gcc-11-11.4.0/debian/tmp-nvptx/usr amdgcn-amdhsa=/build/gcc-11-2Y5pKs/gcc-11-11.4.0/debian/tmp-gcn/usr --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu --with-build-config=bootstrap-lto-lean --enable-link-serialization=2]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 11.4.0 (Ubuntu 11.4.0-1ubuntu1~22.04.2) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_d42f8.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_d42f8.dir/']
        ignore line: [ /usr/lib/gcc/x86_64-linux-gnu/11/cc1 -quiet -v -imultiarch x86_64-linux-gnu /usr/share/cmake-4.1/Modules/CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles/cmTC_d42f8.dir/ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mtune=generic -march=x86-64 -version -fasynchronous-unwind-tables -fstack-protector-strong -Wformat -Wformat-security -fstack-clash-protection -fcf-protection -o /tmp/ccUyu6Cw.s]
        ignore line: [GNU C17 (Ubuntu 11.4.0-1ubuntu1~22.04.2) version 11.4.0 (x86_64-linux-gnu)]
        ignore line: [	compiled by GNU C version 11.4.0  GMP version 6.2.1  MPFR version 4.1.0  MPC version 1.2.1  isl version isl-0.24-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring nonexistent directory "/usr/local/include/x86_64-linux-gnu"]
        ignore line: [ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/11/include-fixed"]
        ignore line: [ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/11/../../../../x86_64-linux-gnu/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /usr/lib/gcc/x86_64-linux-gnu/11/include]
        ignore line: [ /usr/local/include]
        ignore line: [ /usr/include/x86_64-linux-gnu]
        ignore line: [ /usr/include]
        ignore line: [End of search list.]
        ignore line: [GNU C17 (Ubuntu 11.4.0-1ubuntu1~22.04.2) version 11.4.0 (x86_64-linux-gnu)]
        ignore line: [	compiled by GNU C version 11.4.0  GMP version 6.2.1  MPFR version 4.1.0  MPC version 1.2.1  isl version isl-0.24-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [Compiler executable checksum: 4011c2103cba78949d7e02d0f0047a3d]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_d42f8.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_d42f8.dir/']
        ignore line: [ as -v --64 -o CMakeFiles/cmTC_d42f8.dir/CMakeCCompilerABI.c.o /tmp/ccUyu6Cw.s]
        ignore line: [GNU assembler version 2.38 (x86_64-linux-gnu) using BFD version (GNU Binutils for Ubuntu) 2.38]
        ignore line: [COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/11/:/usr/lib/gcc/x86_64-linux-gnu/11/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/11/:/usr/lib/gcc/x86_64-linux-gnu/]
        ignore line: [LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/11/:/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/11/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/11/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_d42f8.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_d42f8.dir/CMakeCCompilerABI.c.']
        ignore line: [Linking C executable cmTC_d42f8]
        ignore line: [/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_d42f8.dir/link.txt --verbose=1]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/usr/bin/cc]
        ignore line: [COLLECT_LTO_WRAPPER=/usr/lib/gcc/x86_64-linux-gnu/11/lto-wrapper]
        ignore line: [OFFLOAD_TARGET_NAMES=nvptx-none:amdgcn-amdhsa]
        ignore line: [OFFLOAD_TARGET_DEFAULT=1]
        ignore line: [Target: x86_64-linux-gnu]
        ignore line: [Configured with: ../src/configure -v --with-pkgversion='Ubuntu 11.4.0-1ubuntu1~22.04.2' --with-bugurl=file:///usr/share/doc/gcc-11/README.Bugs --enable-languages=c ada c++ go brig d fortran objc obj-c++ m2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-11 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --enable-cet --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32 m64 mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-11-2Y5pKs/gcc-11-11.4.0/debian/tmp-nvptx/usr amdgcn-amdhsa=/build/gcc-11-2Y5pKs/gcc-11-11.4.0/debian/tmp-gcn/usr --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu --with-build-config=bootstrap-lto-lean --enable-link-serialization=2]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 11.4.0 (Ubuntu 11.4.0-1ubuntu1~22.04.2) ]
        ignore line: [COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/11/:/usr/lib/gcc/x86_64-linux-gnu/11/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/11/:/usr/lib/gcc/x86_64-linux-gnu/]
        ignore line: [LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/11/:/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/11/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/11/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_d42f8' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_d42f8.']
        link line: [ /usr/lib/gcc/x86_64-linux-gnu/11/collect2 -plugin /usr/lib/gcc/x86_64-linux-gnu/11/liblto_plugin.so -plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/11/lto-wrapper -plugin-opt=-fresolution=/tmp/cc2VBkoE.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -pie -z now -z relro -o cmTC_d42f8 /usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/Scrt1.o /usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/11/crtbeginS.o -L/usr/lib/gcc/x86_64-linux-gnu/11 -L/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/11/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/11/../../.. -v CMakeFiles/cmTC_d42f8.dir/CMakeCCompilerABI.c.o -lgcc --push-state --as-needed -lgcc_s --pop-state -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /usr/lib/gcc/x86_64-linux-gnu/11/crtendS.o /usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/crtn.o]
          arg [/usr/lib/gcc/x86_64-linux-gnu/11/collect2] ==> ignore
          arg [-plugin] ==> ignore
          arg [/usr/lib/gcc/x86_64-linux-gnu/11/liblto_plugin.so] ==> ignore
          arg [-plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/11/lto-wrapper] ==> ignore
          arg [-plugin-opt=-fresolution=/tmp/cc2VBkoE.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [--build-id] ==> ignore
          arg [--eh-frame-hdr] ==> ignore
          arg [-m] ==> ignore
          arg [elf_x86_64] ==> ignore
          arg [--hash-style=gnu] ==> ignore
          arg [--as-needed] ==> ignore
          arg [-dynamic-linker] ==> ignore
          arg [/lib64/ld-linux-x86-64.so.2] ==> ignore
          arg [-pie] ==> ignore
          arg [-znow] ==> ignore
          arg [-zrelro] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_d42f8] ==> ignore
          arg [/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/Scrt1.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/Scrt1.o]
          arg [/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/crti.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/crti.o]
          arg [/usr/lib/gcc/x86_64-linux-gnu/11/crtbeginS.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/11/crtbeginS.o]
          arg [-L/usr/lib/gcc/x86_64-linux-gnu/11] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/11]
          arg [-L/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu]
          arg [-L/usr/lib/gcc/x86_64-linux-gnu/11/../../../../lib] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/11/../../../../lib]
          arg [-L/lib/x86_64-linux-gnu] ==> dir [/lib/x86_64-linux-gnu]
          arg [-L/lib/../lib] ==> dir [/lib/../lib]
          arg [-L/usr/lib/x86_64-linux-gnu] ==> dir [/usr/lib/x86_64-linux-gnu]
          arg [-L/usr/lib/../lib] ==> dir [/usr/lib/../lib]
          arg [-L/usr/lib/gcc/x86_64-linux-gnu/11/../../..] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/11/../../..]
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_d42f8.dir/CMakeCCompilerABI.c.o] ==> ignore
          arg [-lgcc] ==> lib [gcc]
          arg [--push-state] ==> ignore
          arg [--as-needed] ==> ignore
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [--pop-state] ==> ignore
          arg [-lc] ==> lib [c]
          arg [-lgcc] ==> lib [gcc]
          arg [--push-state] ==> ignore
          arg [--as-needed] ==> ignore
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [--pop-state] ==> ignore
          arg [/usr/lib/gcc/x86_64-linux-gnu/11/crtendS.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/11/crtendS.o]
          arg [/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/crtn.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/crtn.o]
        ignore line: [collect2 version 11.4.0]
        ignore line: [/usr/bin/ld -plugin /usr/lib/gcc/x86_64-linux-gnu/11/liblto_plugin.so -plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/11/lto-wrapper -plugin-opt=-fresolution=/tmp/cc2VBkoE.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -pie -z now -z relro -o cmTC_d42f8 /usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/Scrt1.o /usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/11/crtbeginS.o -L/usr/lib/gcc/x86_64-linux-gnu/11 -L/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/11/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/11/../../.. -v CMakeFiles/cmTC_d42f8.dir/CMakeCCompilerABI.c.o -lgcc --push-state --as-needed -lgcc_s --pop-state -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /usr/lib/gcc/x86_64-linux-gnu/11/crtendS.o /usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/crtn.o]
        linker tool for 'C': /usr/bin/ld
        collapse obj [/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/Scrt1.o] ==> [/usr/lib/x86_64-linux-gnu/Scrt1.o]
        collapse obj [/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/crti.o] ==> [/usr/lib/x86_64-linux-gnu/crti.o]
        collapse obj [/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/crtn.o] ==> [/usr/lib/x86_64-linux-gnu/crtn.o]
        collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/11] ==> [/usr/lib/gcc/x86_64-linux-gnu/11]
        collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu] ==> [/usr/lib/x86_64-linux-gnu]
        collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/11/../../../../lib] ==> [/usr/lib]
        collapse library dir [/lib/x86_64-linux-gnu] ==> [/lib/x86_64-linux-gnu]
        collapse library dir [/lib/../lib] ==> [/lib]
        collapse library dir [/usr/lib/x86_64-linux-gnu] ==> [/usr/lib/x86_64-linux-gnu]
        collapse library dir [/usr/lib/../lib] ==> [/usr/lib]
        collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/11/../../..] ==> [/usr/lib]
        implicit libs: [gcc;gcc_s;c;gcc;gcc_s]
        implicit objs: [/usr/lib/x86_64-linux-gnu/Scrt1.o;/usr/lib/x86_64-linux-gnu/crti.o;/usr/lib/gcc/x86_64-linux-gnu/11/crtbeginS.o;/usr/lib/gcc/x86_64-linux-gnu/11/crtendS.o;/usr/lib/x86_64-linux-gnu/crtn.o]
        implicit dirs: [/usr/lib/gcc/x86_64-linux-gnu/11;/usr/lib/x86_64-linux-gnu;/usr/lib;/lib/x86_64-linux-gnu;/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "/usr/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "/usr/bin/ld" "-v"
      GNU ld (GNU Binutils for Ubuntu) 2.38
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "/usr/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/CMakeScratch/TryCompile-uZCosJ"
      binary: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/CMakeScratch/TryCompile-uZCosJ"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/CMakeScratch/TryCompile-uZCosJ'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_ddd68/fast
        /usr/bin/gmake  -f CMakeFiles/cmTC_ddd68.dir/build.make CMakeFiles/cmTC_ddd68.dir/build
        gmake[1]: Entering directory '/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/CMakeScratch/TryCompile-uZCosJ'
        Building CXX object CMakeFiles/cmTC_ddd68.dir/CMakeCXXCompilerABI.cpp.o
        /usr/bin/c++   -v -o CMakeFiles/cmTC_ddd68.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake-4.1/Modules/CMakeCXXCompilerABI.cpp
        Using built-in specs.
        COLLECT_GCC=/usr/bin/c++
        OFFLOAD_TARGET_NAMES=nvptx-none:amdgcn-amdhsa
        OFFLOAD_TARGET_DEFAULT=1
        Target: x86_64-linux-gnu
        Configured with: ../src/configure -v --with-pkgversion='Ubuntu 11.4.0-1ubuntu1~22.04.2' --with-bugurl=file:///usr/share/doc/gcc-11/README.Bugs --enable-languages=c,ada,c++,go,brig,d,fortran,objc,obj-c++,m2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-11 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --enable-cet --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-11-2Y5pKs/gcc-11-11.4.0/debian/tmp-nvptx/usr,amdgcn-amdhsa=/build/gcc-11-2Y5pKs/gcc-11-11.4.0/debian/tmp-gcn/usr --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu --with-build-config=bootstrap-lto-lean --enable-link-serialization=2
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 11.4.0 (Ubuntu 11.4.0-1ubuntu1~22.04.2) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_ddd68.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_ddd68.dir/'
         /usr/lib/gcc/x86_64-linux-gnu/11/cc1plus -quiet -v -imultiarch x86_64-linux-gnu -D_GNU_SOURCE /usr/share/cmake-4.1/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles/cmTC_ddd68.dir/ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -mtune=generic -march=x86-64 -version -fasynchronous-unwind-tables -fstack-protector-strong -Wformat -Wformat-security -fstack-clash-protection -fcf-protection -o /tmp/cccIdvRm.s
        GNU C++17 (Ubuntu 11.4.0-1ubuntu1~22.04.2) version 11.4.0 (x86_64-linux-gnu)
        	compiled by GNU C version 11.4.0, GMP version 6.2.1, MPFR version 4.1.0, MPC version 1.2.1, isl version isl-0.24-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "/usr/include/x86_64-linux-gnu/c++/11"
        ignoring nonexistent directory "/usr/local/include/x86_64-linux-gnu"
        ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/11/include-fixed"
        ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/11/../../../../x86_64-linux-gnu/include"
        #include "..." search starts here:
        #include <...> search starts here:
         /usr/include/c++/11
         /usr/include/x86_64-linux-gnu/c++/11
         /usr/include/c++/11/backward
         /usr/lib/gcc/x86_64-linux-gnu/11/include
         /usr/local/include
         /usr/include/x86_64-linux-gnu
         /usr/include
        End of search list.
        GNU C++17 (Ubuntu 11.4.0-1ubuntu1~22.04.2) version 11.4.0 (x86_64-linux-gnu)
        	compiled by GNU C version 11.4.0, GMP version 6.2.1, MPFR version 4.1.0, MPC version 1.2.1, isl version isl-0.24-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        Compiler executable checksum: 6c87588fc345655b93b8c25f48f88886
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_ddd68.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_ddd68.dir/'
         as -v --64 -o CMakeFiles/cmTC_ddd68.dir/CMakeCXXCompilerABI.cpp.o /tmp/cccIdvRm.s
        GNU assembler version 2.38 (x86_64-linux-gnu) using BFD version (GNU Binutils for Ubuntu) 2.38
        COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/11/:/usr/lib/gcc/x86_64-linux-gnu/11/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/11/:/usr/lib/gcc/x86_64-linux-gnu/
        LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/11/:/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/11/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/11/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_ddd68.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_ddd68.dir/CMakeCXXCompilerABI.cpp.'
        Linking CXX executable cmTC_ddd68
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_ddd68.dir/link.txt --verbose=1
        Using built-in specs.
        COLLECT_GCC=/usr/bin/c++
        COLLECT_LTO_WRAPPER=/usr/lib/gcc/x86_64-linux-gnu/11/lto-wrapper
        OFFLOAD_TARGET_NAMES=nvptx-none:amdgcn-amdhsa
        OFFLOAD_TARGET_DEFAULT=1
        Target: x86_64-linux-gnu
        Configured with: ../src/configure -v --with-pkgversion='Ubuntu 11.4.0-1ubuntu1~22.04.2' --with-bugurl=file:///usr/share/doc/gcc-11/README.Bugs --enable-languages=c,ada,c++,go,brig,d,fortran,objc,obj-c++,m2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-11 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --enable-cet --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-11-2Y5pKs/gcc-11-11.4.0/debian/tmp-nvptx/usr,amdgcn-amdhsa=/build/gcc-11-2Y5pKs/gcc-11-11.4.0/debian/tmp-gcn/usr --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu --with-build-config=bootstrap-lto-lean --enable-link-serialization=2
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 11.4.0 (Ubuntu 11.4.0-1ubuntu1~22.04.2) 
        COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/11/:/usr/lib/gcc/x86_64-linux-gnu/11/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/11/:/usr/lib/gcc/x86_64-linux-gnu/
        LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/11/:/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/11/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/11/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_ddd68' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_ddd68.'
         /usr/lib/gcc/x86_64-linux-gnu/11/collect2 -plugin /usr/lib/gcc/x86_64-linux-gnu/11/liblto_plugin.so -plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/11/lto-wrapper -plugin-opt=-fresolution=/tmp/cceuZC3D.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -pie -z now -z relro -o cmTC_ddd68 /usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/Scrt1.o /usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/11/crtbeginS.o -L/usr/lib/gcc/x86_64-linux-gnu/11 -L/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/11/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/11/../../.. -v CMakeFiles/cmTC_ddd68.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/x86_64-linux-gnu/11/crtendS.o /usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/crtn.o
        collect2 version 11.4.0
        /usr/bin/ld -plugin /usr/lib/gcc/x86_64-linux-gnu/11/liblto_plugin.so -plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/11/lto-wrapper -plugin-opt=-fresolution=/tmp/cceuZC3D.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -pie -z now -z relro -o cmTC_ddd68 /usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/Scrt1.o /usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/11/crtbeginS.o -L/usr/lib/gcc/x86_64-linux-gnu/11 -L/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/11/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/11/../../.. -v CMakeFiles/cmTC_ddd68.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/x86_64-linux-gnu/11/crtendS.o /usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/crtn.o
        GNU ld (GNU Binutils for Ubuntu) 2.38
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_ddd68' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_ddd68.'
        /usr/bin/c++  -v -Wl,-v CMakeFiles/cmTC_ddd68.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_ddd68
        gmake[1]: Leaving directory '/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/CMakeScratch/TryCompile-uZCosJ'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:217 (message)"
      - "/usr/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/usr/include/c++/11]
          add: [/usr/include/x86_64-linux-gnu/c++/11]
          add: [/usr/include/c++/11/backward]
          add: [/usr/lib/gcc/x86_64-linux-gnu/11/include]
          add: [/usr/local/include]
          add: [/usr/include/x86_64-linux-gnu]
          add: [/usr/include]
        end of search list found
        collapse include dir [/usr/include/c++/11] ==> [/usr/include/c++/11]
        collapse include dir [/usr/include/x86_64-linux-gnu/c++/11] ==> [/usr/include/x86_64-linux-gnu/c++/11]
        collapse include dir [/usr/include/c++/11/backward] ==> [/usr/include/c++/11/backward]
        collapse include dir [/usr/lib/gcc/x86_64-linux-gnu/11/include] ==> [/usr/lib/gcc/x86_64-linux-gnu/11/include]
        collapse include dir [/usr/local/include] ==> [/usr/local/include]
        collapse include dir [/usr/include/x86_64-linux-gnu] ==> [/usr/include/x86_64-linux-gnu]
        collapse include dir [/usr/include] ==> [/usr/include]
        implicit include dirs: [/usr/include/c++/11;/usr/include/x86_64-linux-gnu/c++/11;/usr/include/c++/11/backward;/usr/lib/gcc/x86_64-linux-gnu/11/include;/usr/local/include;/usr/include/x86_64-linux-gnu;/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "/usr/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: '/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/CMakeScratch/TryCompile-uZCosJ']
        ignore line: []
        ignore line: [Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_ddd68/fast]
        ignore line: [/usr/bin/gmake  -f CMakeFiles/cmTC_ddd68.dir/build.make CMakeFiles/cmTC_ddd68.dir/build]
        ignore line: [gmake[1]: Entering directory '/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/CMakeScratch/TryCompile-uZCosJ']
        ignore line: [Building CXX object CMakeFiles/cmTC_ddd68.dir/CMakeCXXCompilerABI.cpp.o]
        ignore line: [/usr/bin/c++   -v -o CMakeFiles/cmTC_ddd68.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake-4.1/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/usr/bin/c++]
        ignore line: [OFFLOAD_TARGET_NAMES=nvptx-none:amdgcn-amdhsa]
        ignore line: [OFFLOAD_TARGET_DEFAULT=1]
        ignore line: [Target: x86_64-linux-gnu]
        ignore line: [Configured with: ../src/configure -v --with-pkgversion='Ubuntu 11.4.0-1ubuntu1~22.04.2' --with-bugurl=file:///usr/share/doc/gcc-11/README.Bugs --enable-languages=c ada c++ go brig d fortran objc obj-c++ m2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-11 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --enable-cet --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32 m64 mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-11-2Y5pKs/gcc-11-11.4.0/debian/tmp-nvptx/usr amdgcn-amdhsa=/build/gcc-11-2Y5pKs/gcc-11-11.4.0/debian/tmp-gcn/usr --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu --with-build-config=bootstrap-lto-lean --enable-link-serialization=2]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 11.4.0 (Ubuntu 11.4.0-1ubuntu1~22.04.2) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_ddd68.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_ddd68.dir/']
        ignore line: [ /usr/lib/gcc/x86_64-linux-gnu/11/cc1plus -quiet -v -imultiarch x86_64-linux-gnu -D_GNU_SOURCE /usr/share/cmake-4.1/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles/cmTC_ddd68.dir/ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -mtune=generic -march=x86-64 -version -fasynchronous-unwind-tables -fstack-protector-strong -Wformat -Wformat-security -fstack-clash-protection -fcf-protection -o /tmp/cccIdvRm.s]
        ignore line: [GNU C++17 (Ubuntu 11.4.0-1ubuntu1~22.04.2) version 11.4.0 (x86_64-linux-gnu)]
        ignore line: [	compiled by GNU C version 11.4.0  GMP version 6.2.1  MPFR version 4.1.0  MPC version 1.2.1  isl version isl-0.24-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "/usr/include/x86_64-linux-gnu/c++/11"]
        ignore line: [ignoring nonexistent directory "/usr/local/include/x86_64-linux-gnu"]
        ignore line: [ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/11/include-fixed"]
        ignore line: [ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/11/../../../../x86_64-linux-gnu/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /usr/include/c++/11]
        ignore line: [ /usr/include/x86_64-linux-gnu/c++/11]
        ignore line: [ /usr/include/c++/11/backward]
        ignore line: [ /usr/lib/gcc/x86_64-linux-gnu/11/include]
        ignore line: [ /usr/local/include]
        ignore line: [ /usr/include/x86_64-linux-gnu]
        ignore line: [ /usr/include]
        ignore line: [End of search list.]
        ignore line: [GNU C++17 (Ubuntu 11.4.0-1ubuntu1~22.04.2) version 11.4.0 (x86_64-linux-gnu)]
        ignore line: [	compiled by GNU C version 11.4.0  GMP version 6.2.1  MPFR version 4.1.0  MPC version 1.2.1  isl version isl-0.24-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [Compiler executable checksum: 6c87588fc345655b93b8c25f48f88886]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_ddd68.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_ddd68.dir/']
        ignore line: [ as -v --64 -o CMakeFiles/cmTC_ddd68.dir/CMakeCXXCompilerABI.cpp.o /tmp/cccIdvRm.s]
        ignore line: [GNU assembler version 2.38 (x86_64-linux-gnu) using BFD version (GNU Binutils for Ubuntu) 2.38]
        ignore line: [COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/11/:/usr/lib/gcc/x86_64-linux-gnu/11/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/11/:/usr/lib/gcc/x86_64-linux-gnu/]
        ignore line: [LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/11/:/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/11/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/11/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_ddd68.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_ddd68.dir/CMakeCXXCompilerABI.cpp.']
        ignore line: [Linking CXX executable cmTC_ddd68]
        ignore line: [/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_ddd68.dir/link.txt --verbose=1]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/usr/bin/c++]
        ignore line: [COLLECT_LTO_WRAPPER=/usr/lib/gcc/x86_64-linux-gnu/11/lto-wrapper]
        ignore line: [OFFLOAD_TARGET_NAMES=nvptx-none:amdgcn-amdhsa]
        ignore line: [OFFLOAD_TARGET_DEFAULT=1]
        ignore line: [Target: x86_64-linux-gnu]
        ignore line: [Configured with: ../src/configure -v --with-pkgversion='Ubuntu 11.4.0-1ubuntu1~22.04.2' --with-bugurl=file:///usr/share/doc/gcc-11/README.Bugs --enable-languages=c ada c++ go brig d fortran objc obj-c++ m2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-11 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --enable-cet --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32 m64 mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-11-2Y5pKs/gcc-11-11.4.0/debian/tmp-nvptx/usr amdgcn-amdhsa=/build/gcc-11-2Y5pKs/gcc-11-11.4.0/debian/tmp-gcn/usr --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu --with-build-config=bootstrap-lto-lean --enable-link-serialization=2]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 11.4.0 (Ubuntu 11.4.0-1ubuntu1~22.04.2) ]
        ignore line: [COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/11/:/usr/lib/gcc/x86_64-linux-gnu/11/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/11/:/usr/lib/gcc/x86_64-linux-gnu/]
        ignore line: [LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/11/:/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/11/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/11/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_ddd68' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_ddd68.']
        link line: [ /usr/lib/gcc/x86_64-linux-gnu/11/collect2 -plugin /usr/lib/gcc/x86_64-linux-gnu/11/liblto_plugin.so -plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/11/lto-wrapper -plugin-opt=-fresolution=/tmp/cceuZC3D.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -pie -z now -z relro -o cmTC_ddd68 /usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/Scrt1.o /usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/11/crtbeginS.o -L/usr/lib/gcc/x86_64-linux-gnu/11 -L/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/11/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/11/../../.. -v CMakeFiles/cmTC_ddd68.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/x86_64-linux-gnu/11/crtendS.o /usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/crtn.o]
          arg [/usr/lib/gcc/x86_64-linux-gnu/11/collect2] ==> ignore
          arg [-plugin] ==> ignore
          arg [/usr/lib/gcc/x86_64-linux-gnu/11/liblto_plugin.so] ==> ignore
          arg [-plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/11/lto-wrapper] ==> ignore
          arg [-plugin-opt=-fresolution=/tmp/cceuZC3D.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [--build-id] ==> ignore
          arg [--eh-frame-hdr] ==> ignore
          arg [-m] ==> ignore
          arg [elf_x86_64] ==> ignore
          arg [--hash-style=gnu] ==> ignore
          arg [--as-needed] ==> ignore
          arg [-dynamic-linker] ==> ignore
          arg [/lib64/ld-linux-x86-64.so.2] ==> ignore
          arg [-pie] ==> ignore
          arg [-znow] ==> ignore
          arg [-zrelro] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_ddd68] ==> ignore
          arg [/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/Scrt1.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/Scrt1.o]
          arg [/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/crti.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/crti.o]
          arg [/usr/lib/gcc/x86_64-linux-gnu/11/crtbeginS.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/11/crtbeginS.o]
          arg [-L/usr/lib/gcc/x86_64-linux-gnu/11] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/11]
          arg [-L/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu]
          arg [-L/usr/lib/gcc/x86_64-linux-gnu/11/../../../../lib] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/11/../../../../lib]
          arg [-L/lib/x86_64-linux-gnu] ==> dir [/lib/x86_64-linux-gnu]
          arg [-L/lib/../lib] ==> dir [/lib/../lib]
          arg [-L/usr/lib/x86_64-linux-gnu] ==> dir [/usr/lib/x86_64-linux-gnu]
          arg [-L/usr/lib/../lib] ==> dir [/usr/lib/../lib]
          arg [-L/usr/lib/gcc/x86_64-linux-gnu/11/../../..] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/11/../../..]
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_ddd68.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
          arg [-lstdc++] ==> lib [stdc++]
          arg [-lm] ==> lib [m]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lc] ==> lib [c]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [/usr/lib/gcc/x86_64-linux-gnu/11/crtendS.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/11/crtendS.o]
          arg [/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/crtn.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/crtn.o]
        ignore line: [collect2 version 11.4.0]
        ignore line: [/usr/bin/ld -plugin /usr/lib/gcc/x86_64-linux-gnu/11/liblto_plugin.so -plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/11/lto-wrapper -plugin-opt=-fresolution=/tmp/cceuZC3D.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -pie -z now -z relro -o cmTC_ddd68 /usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/Scrt1.o /usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/11/crtbeginS.o -L/usr/lib/gcc/x86_64-linux-gnu/11 -L/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/11/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/11/../../.. -v CMakeFiles/cmTC_ddd68.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/x86_64-linux-gnu/11/crtendS.o /usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/crtn.o]
        linker tool for 'CXX': /usr/bin/ld
        collapse obj [/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/Scrt1.o] ==> [/usr/lib/x86_64-linux-gnu/Scrt1.o]
        collapse obj [/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/crti.o] ==> [/usr/lib/x86_64-linux-gnu/crti.o]
        collapse obj [/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu/crtn.o] ==> [/usr/lib/x86_64-linux-gnu/crtn.o]
        collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/11] ==> [/usr/lib/gcc/x86_64-linux-gnu/11]
        collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/11/../../../x86_64-linux-gnu] ==> [/usr/lib/x86_64-linux-gnu]
        collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/11/../../../../lib] ==> [/usr/lib]
        collapse library dir [/lib/x86_64-linux-gnu] ==> [/lib/x86_64-linux-gnu]
        collapse library dir [/lib/../lib] ==> [/lib]
        collapse library dir [/usr/lib/x86_64-linux-gnu] ==> [/usr/lib/x86_64-linux-gnu]
        collapse library dir [/usr/lib/../lib] ==> [/usr/lib]
        collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/11/../../..] ==> [/usr/lib]
        implicit libs: [stdc++;m;gcc_s;gcc;c;gcc_s;gcc]
        implicit objs: [/usr/lib/x86_64-linux-gnu/Scrt1.o;/usr/lib/x86_64-linux-gnu/crti.o;/usr/lib/gcc/x86_64-linux-gnu/11/crtbeginS.o;/usr/lib/gcc/x86_64-linux-gnu/11/crtendS.o;/usr/lib/x86_64-linux-gnu/crtn.o]
        implicit dirs: [/usr/lib/gcc/x86_64-linux-gnu/11;/usr/lib/x86_64-linux-gnu;/usr/lib;/lib/x86_64-linux-gnu;/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "/usr/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "/usr/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "/usr/bin/ld" "-v"
      GNU ld (GNU Binutils for Ubuntu) 2.38
  -
    kind: "find_package-v1"
    backtrace:
      - "/opt/ros/humble/share/ament_cmake/cmake/ament_cmake_export_dependencies-extras.cmake:5 (find_package)"
      - "/opt/ros/humble/share/ament_cmake/cmake/ament_cmakeConfig.cmake:41 (include)"
      - "CMakeLists.txt:9 (find_package)"
    name: "ament_cmake_libraries"
    configs:
      -
        filename: "ament_cmake_librariesConfig.cmake"
        kind: "cmake"
      -
        filename: "ament_cmake_libraries-config.cmake"
        kind: "cmake"
    version_request:
      exact: false
    settings:
      required: "required_explicit"
      quiet: true
      global: false
      policy_scope: true
      bypass_provider: false
      names:
        - "ament_cmake_libraries"
      path_suffixes:
        - ""
      paths:
        CMAKE_FIND_USE_CMAKE_PATH: true
        CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
        CMAKE_FIND_USE_INSTALL_PREFIX: true
        CMAKE_FIND_USE_PACKAGE_ROOT_PATH: true
        CMAKE_FIND_USE_CMAKE_PACKAGE_REGISTRY: true
        CMAKE_FIND_USE_SYSTEM_PACKAGE_REGISTRY: true
        CMAKE_FIND_ROOT_PATH_MODE: "BOTH"
    candidates:
      -
        path: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/pkgRedirects/ament_cmake_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/pkgRedirects/ament_cmake_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/install/ament_cmake_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/install/ament_cmake_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ros2_control/install/ament_cmake_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ros2_control/install/ament_cmake_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/ament_cmake_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/ament_cmake_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_manager/ament_cmake_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_manager/ament_cmake_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_msgs/ament_cmake_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_msgs/ament_cmake_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/ament_cmake_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/ament_cmake_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/ament_cmake_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/ament_cmake_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/ament_cmake_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/ament_cmake_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/ament_cmake_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/ament_cmake_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/ament_cmake_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/ament_cmake_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/ament_cmake_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/ament_cmake_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver/ament_cmake_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver/ament_cmake_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_interface/ament_cmake_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_interface/ament_cmake_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ws_tools/install/ament_lint_auto/ament_cmake_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ws_tools/install/ament_lint_auto/ament_cmake_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/ament_cmake_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/ament_cmake_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/cmake/ament_cmake_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/cmake/ament_cmake_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_libraries/ament_cmake_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_libraries/ament_cmake_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
    found:
      path: "/opt/ros/humble/share/ament_cmake_libraries/cmake/ament_cmake_librariesConfig.cmake"
      mode: "config"
      version: "1.3.12"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/home/<USER>/code/pmac_robot/install"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/ros/humble/share/ament_cmake_core/cmake/core/python.cmake:31 (find_program)"
      - "/opt/ros/humble/share/ament_cmake_core/cmake/core/all.cmake:54 (include)"
      - "/opt/ros/humble/share/ament_cmake_core/cmake/ament_cmake_core-extras.cmake:17 (include)"
      - "/opt/ros/humble/share/ament_cmake_core/cmake/ament_cmake_coreConfig.cmake:41 (include)"
      - "/opt/ros/humble/share/ament_cmake/cmake/ament_cmake_export_dependencies-extras.cmake:15 (find_package)"
      - "/opt/ros/humble/share/ament_cmake/cmake/ament_cmakeConfig.cmake:41 (include)"
      - "CMakeLists.txt:9 (find_package)"
    mode: "program"
    variable: "Python3_EXECUTABLE"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "python3"
    candidate_directories:
      - "/home/<USER>/code/pmac_robot/install/bin/"
      - "/home/<USER>/code/pmac_robot/install/sbin/"
      - "/home/<USER>/code/pmac_robot/install/"
      - "/home/<USER>/code/ros2_control/install/bin/"
      - "/home/<USER>/code/ros2_control/install/sbin/"
      - "/home/<USER>/code/ros2_control/install/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_manager/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_manager/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_manager/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_msgs/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_msgs/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_msgs/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_interface/bin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_interface/sbin/"
      - "/home/<USER>/code/ecat_ws/install/ethercat_interface/"
      - "/home/<USER>/code/ws_tools/install/ament_lint_auto/bin/"
      - "/home/<USER>/code/ws_tools/install/ament_lint_auto/sbin/"
      - "/home/<USER>/code/ws_tools/install/ament_lint_auto/"
      - "/opt/ros/humble/bin/"
      - "/opt/ros/humble/sbin/"
      - "/opt/ros/humble/"
      - "/home/<USER>/code/staubli_experimental/install/bin/"
      - "/home/<USER>/code/staubli_experimental/install/sbin/"
      - "/home/<USER>/code/staubli_experimental/install/"
      - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description/bin/"
      - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description/sbin/"
      - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description/"
      - "/opt/ros/humble/bin/"
      - "/home/<USER>/.local/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/sbin/"
      - "/usr/bin/"
      - "/sbin/"
      - "/bin/"
      - "/usr/games/"
      - "/usr/local/games/"
      - "/snap/bin/"
      - "/home/<USER>/.fzf/bin/"
      - "/usr/local/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/"
      - "/usr/bin/"
      - "/usr/sbin/"
      - "/usr/"
      - "/bin/"
      - "/sbin/"
      - "/usr/X11R6/bin/"
      - "/usr/X11R6/sbin/"
      - "/usr/X11R6/"
      - "/usr/pkg/bin/"
      - "/usr/pkg/sbin/"
      - "/usr/pkg/"
      - "/opt/bin/"
      - "/opt/sbin/"
      - "/opt/"
    searched_directories:
      - "/home/<USER>/code/pmac_robot/install/bin/python3"
      - "/home/<USER>/code/pmac_robot/install/sbin/python3"
      - "/home/<USER>/code/pmac_robot/install/python3"
      - "/home/<USER>/code/ros2_control/install/bin/python3"
      - "/home/<USER>/code/ros2_control/install/sbin/python3"
      - "/home/<USER>/code/ros2_control/install/python3"
      - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/bin/python3"
      - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/sbin/python3"
      - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/python3"
      - "/home/<USER>/code/ecat_ws/install/ethercat_manager/bin/python3"
      - "/home/<USER>/code/ecat_ws/install/ethercat_manager/sbin/python3"
      - "/home/<USER>/code/ecat_ws/install/ethercat_manager/python3"
      - "/home/<USER>/code/ecat_ws/install/ethercat_msgs/bin/python3"
      - "/home/<USER>/code/ecat_ws/install/ethercat_msgs/sbin/python3"
      - "/home/<USER>/code/ecat_ws/install/ethercat_msgs/python3"
      - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/bin/python3"
      - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/sbin/python3"
      - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/python3"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/bin/python3"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/sbin/python3"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/python3"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/bin/python3"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/sbin/python3"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/python3"
      - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/bin/python3"
      - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/sbin/python3"
      - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/python3"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/bin/python3"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/sbin/python3"
      - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/python3"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/bin/python3"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/sbin/python3"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/python3"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver/bin/python3"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver/sbin/python3"
      - "/home/<USER>/code/ecat_ws/install/ethercat_driver/python3"
      - "/home/<USER>/code/ecat_ws/install/ethercat_interface/bin/python3"
      - "/home/<USER>/code/ecat_ws/install/ethercat_interface/sbin/python3"
      - "/home/<USER>/code/ecat_ws/install/ethercat_interface/python3"
      - "/home/<USER>/code/ws_tools/install/ament_lint_auto/bin/python3"
      - "/home/<USER>/code/ws_tools/install/ament_lint_auto/sbin/python3"
      - "/home/<USER>/code/ws_tools/install/ament_lint_auto/python3"
      - "/opt/ros/humble/bin/python3"
      - "/opt/ros/humble/sbin/python3"
      - "/opt/ros/humble/python3"
      - "/home/<USER>/code/staubli_experimental/install/bin/python3"
      - "/home/<USER>/code/staubli_experimental/install/sbin/python3"
      - "/home/<USER>/code/staubli_experimental/install/python3"
      - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description/bin/python3"
      - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description/sbin/python3"
      - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description/python3"
      - "/opt/ros/humble/bin/python3"
      - "/home/<USER>/.local/bin/python3"
      - "/usr/local/sbin/python3"
      - "/usr/local/bin/python3"
      - "/usr/sbin/python3"
    found: "/usr/bin/python3"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/home/<USER>/code/pmac_robot/install"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
  -
    kind: "find_package-v1"
    backtrace:
      - "/opt/ros/humble/share/ament_cmake/cmake/ament_cmake_export_dependencies-extras.cmake:15 (find_package)"
      - "/opt/ros/humble/share/ament_cmake/cmake/ament_cmakeConfig.cmake:41 (include)"
      - "CMakeLists.txt:9 (find_package)"
    name: "ament_cmake_core"
    configs:
      -
        filename: "ament_cmake_coreConfig.cmake"
        kind: "cmake"
      -
        filename: "ament_cmake_core-config.cmake"
        kind: "cmake"
    version_request:
      exact: false
    settings:
      required: "required_explicit"
      quiet: true
      global: false
      policy_scope: true
      bypass_provider: false
      names:
        - "ament_cmake_core"
      path_suffixes:
        - ""
      paths:
        CMAKE_FIND_USE_CMAKE_PATH: true
        CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
        CMAKE_FIND_USE_INSTALL_PREFIX: true
        CMAKE_FIND_USE_PACKAGE_ROOT_PATH: true
        CMAKE_FIND_USE_CMAKE_PACKAGE_REGISTRY: true
        CMAKE_FIND_USE_SYSTEM_PACKAGE_REGISTRY: true
        CMAKE_FIND_ROOT_PATH_MODE: "BOTH"
    candidates:
      -
        path: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/pkgRedirects/ament_cmake_coreConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/pkgRedirects/ament_cmake_core-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/install/ament_cmake_coreConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/install/ament_cmake_core-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ros2_control/install/ament_cmake_coreConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ros2_control/install/ament_cmake_core-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/ament_cmake_coreConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/ament_cmake_core-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_manager/ament_cmake_coreConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_manager/ament_cmake_core-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_msgs/ament_cmake_coreConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_msgs/ament_cmake_core-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/ament_cmake_coreConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/ament_cmake_core-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/ament_cmake_coreConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/ament_cmake_core-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/ament_cmake_coreConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/ament_cmake_core-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/ament_cmake_coreConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/ament_cmake_core-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/ament_cmake_coreConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/ament_cmake_core-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/ament_cmake_coreConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/ament_cmake_core-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver/ament_cmake_coreConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver/ament_cmake_core-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_interface/ament_cmake_coreConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_interface/ament_cmake_core-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ws_tools/install/ament_lint_auto/ament_cmake_coreConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ws_tools/install/ament_lint_auto/ament_cmake_core-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/ament_cmake_coreConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/ament_cmake_core-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/cmake/ament_cmake_coreConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/cmake/ament_cmake_core-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_core/ament_cmake_coreConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_core/ament_cmake_core-config.cmake"
        mode: "config"
        reason: "no_exist"
    found:
      path: "/opt/ros/humble/share/ament_cmake_core/cmake/ament_cmake_coreConfig.cmake"
      mode: "config"
      version: "1.3.12"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/home/<USER>/code/pmac_robot/install"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
  -
    kind: "find_package-v1"
    backtrace:
      - "/opt/ros/humble/share/ament_cmake/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)"
      - "/opt/ros/humble/share/ament_cmake/cmake/ament_cmakeConfig.cmake:41 (include)"
      - "CMakeLists.txt:9 (find_package)"
    name: "ament_cmake_export_definitions"
    configs:
      -
        filename: "ament_cmake_export_definitionsConfig.cmake"
        kind: "cmake"
      -
        filename: "ament_cmake_export_definitions-config.cmake"
        kind: "cmake"
    version_request:
      exact: false
    settings:
      required: "required_explicit"
      quiet: true
      global: false
      policy_scope: true
      bypass_provider: false
      names:
        - "ament_cmake_export_definitions"
      path_suffixes:
        - ""
      paths:
        CMAKE_FIND_USE_CMAKE_PATH: true
        CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
        CMAKE_FIND_USE_INSTALL_PREFIX: true
        CMAKE_FIND_USE_PACKAGE_ROOT_PATH: true
        CMAKE_FIND_USE_CMAKE_PACKAGE_REGISTRY: true
        CMAKE_FIND_USE_SYSTEM_PACKAGE_REGISTRY: true
        CMAKE_FIND_ROOT_PATH_MODE: "BOTH"
    candidates:
      -
        path: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/pkgRedirects/ament_cmake_export_definitionsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/pkgRedirects/ament_cmake_export_definitions-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/install/ament_cmake_export_definitionsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/install/ament_cmake_export_definitions-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ros2_control/install/ament_cmake_export_definitionsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ros2_control/install/ament_cmake_export_definitions-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/ament_cmake_export_definitionsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/ament_cmake_export_definitions-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_manager/ament_cmake_export_definitionsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_manager/ament_cmake_export_definitions-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_msgs/ament_cmake_export_definitionsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_msgs/ament_cmake_export_definitions-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/ament_cmake_export_definitionsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/ament_cmake_export_definitions-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/ament_cmake_export_definitionsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/ament_cmake_export_definitions-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/ament_cmake_export_definitionsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/ament_cmake_export_definitions-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/ament_cmake_export_definitionsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/ament_cmake_export_definitions-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/ament_cmake_export_definitionsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/ament_cmake_export_definitions-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/ament_cmake_export_definitionsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/ament_cmake_export_definitions-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver/ament_cmake_export_definitionsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver/ament_cmake_export_definitions-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_interface/ament_cmake_export_definitionsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_interface/ament_cmake_export_definitions-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ws_tools/install/ament_lint_auto/ament_cmake_export_definitionsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ws_tools/install/ament_lint_auto/ament_cmake_export_definitions-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/ament_cmake_export_definitionsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/ament_cmake_export_definitions-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/cmake/ament_cmake_export_definitionsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/cmake/ament_cmake_export_definitions-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_export_definitions/ament_cmake_export_definitionsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_export_definitions/ament_cmake_export_definitions-config.cmake"
        mode: "config"
        reason: "no_exist"
    found:
      path: "/opt/ros/humble/share/ament_cmake_export_definitions/cmake/ament_cmake_export_definitionsConfig.cmake"
      mode: "config"
      version: "1.3.12"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/home/<USER>/code/pmac_robot/install"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
  -
    kind: "find_package-v1"
    backtrace:
      - "/opt/ros/humble/share/ament_cmake/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)"
      - "/opt/ros/humble/share/ament_cmake/cmake/ament_cmakeConfig.cmake:41 (include)"
      - "CMakeLists.txt:9 (find_package)"
    name: "ament_cmake_export_dependencies"
    configs:
      -
        filename: "ament_cmake_export_dependenciesConfig.cmake"
        kind: "cmake"
      -
        filename: "ament_cmake_export_dependencies-config.cmake"
        kind: "cmake"
    version_request:
      exact: false
    settings:
      required: "required_explicit"
      quiet: true
      global: false
      policy_scope: true
      bypass_provider: false
      names:
        - "ament_cmake_export_dependencies"
      path_suffixes:
        - ""
      paths:
        CMAKE_FIND_USE_CMAKE_PATH: true
        CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
        CMAKE_FIND_USE_INSTALL_PREFIX: true
        CMAKE_FIND_USE_PACKAGE_ROOT_PATH: true
        CMAKE_FIND_USE_CMAKE_PACKAGE_REGISTRY: true
        CMAKE_FIND_USE_SYSTEM_PACKAGE_REGISTRY: true
        CMAKE_FIND_ROOT_PATH_MODE: "BOTH"
    candidates:
      -
        path: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/pkgRedirects/ament_cmake_export_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/pkgRedirects/ament_cmake_export_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/install/ament_cmake_export_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/install/ament_cmake_export_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ros2_control/install/ament_cmake_export_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ros2_control/install/ament_cmake_export_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/ament_cmake_export_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/ament_cmake_export_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_manager/ament_cmake_export_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_manager/ament_cmake_export_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_msgs/ament_cmake_export_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_msgs/ament_cmake_export_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/ament_cmake_export_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/ament_cmake_export_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/ament_cmake_export_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/ament_cmake_export_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/ament_cmake_export_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/ament_cmake_export_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/ament_cmake_export_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/ament_cmake_export_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/ament_cmake_export_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/ament_cmake_export_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/ament_cmake_export_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/ament_cmake_export_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver/ament_cmake_export_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver/ament_cmake_export_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_interface/ament_cmake_export_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_interface/ament_cmake_export_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ws_tools/install/ament_lint_auto/ament_cmake_export_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ws_tools/install/ament_lint_auto/ament_cmake_export_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/ament_cmake_export_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/ament_cmake_export_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/cmake/ament_cmake_export_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/cmake/ament_cmake_export_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_export_dependencies/ament_cmake_export_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_export_dependencies/ament_cmake_export_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
    found:
      path: "/opt/ros/humble/share/ament_cmake_export_dependencies/cmake/ament_cmake_export_dependenciesConfig.cmake"
      mode: "config"
      version: "1.3.12"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/home/<USER>/code/pmac_robot/install"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
  -
    kind: "find_package-v1"
    backtrace:
      - "/opt/ros/humble/share/ament_cmake/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)"
      - "/opt/ros/humble/share/ament_cmake/cmake/ament_cmakeConfig.cmake:41 (include)"
      - "CMakeLists.txt:9 (find_package)"
    name: "ament_cmake_export_include_directories"
    configs:
      -
        filename: "ament_cmake_export_include_directoriesConfig.cmake"
        kind: "cmake"
      -
        filename: "ament_cmake_export_include_directories-config.cmake"
        kind: "cmake"
    version_request:
      exact: false
    settings:
      required: "required_explicit"
      quiet: true
      global: false
      policy_scope: true
      bypass_provider: false
      names:
        - "ament_cmake_export_include_directories"
      path_suffixes:
        - ""
      paths:
        CMAKE_FIND_USE_CMAKE_PATH: true
        CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
        CMAKE_FIND_USE_INSTALL_PREFIX: true
        CMAKE_FIND_USE_PACKAGE_ROOT_PATH: true
        CMAKE_FIND_USE_CMAKE_PACKAGE_REGISTRY: true
        CMAKE_FIND_USE_SYSTEM_PACKAGE_REGISTRY: true
        CMAKE_FIND_ROOT_PATH_MODE: "BOTH"
    candidates:
      -
        path: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/pkgRedirects/ament_cmake_export_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/pkgRedirects/ament_cmake_export_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/install/ament_cmake_export_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/install/ament_cmake_export_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ros2_control/install/ament_cmake_export_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ros2_control/install/ament_cmake_export_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/ament_cmake_export_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/ament_cmake_export_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_manager/ament_cmake_export_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_manager/ament_cmake_export_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_msgs/ament_cmake_export_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_msgs/ament_cmake_export_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/ament_cmake_export_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/ament_cmake_export_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/ament_cmake_export_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/ament_cmake_export_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/ament_cmake_export_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/ament_cmake_export_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/ament_cmake_export_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/ament_cmake_export_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/ament_cmake_export_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/ament_cmake_export_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/ament_cmake_export_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/ament_cmake_export_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver/ament_cmake_export_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver/ament_cmake_export_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_interface/ament_cmake_export_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_interface/ament_cmake_export_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ws_tools/install/ament_lint_auto/ament_cmake_export_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ws_tools/install/ament_lint_auto/ament_cmake_export_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/ament_cmake_export_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/ament_cmake_export_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/cmake/ament_cmake_export_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/cmake/ament_cmake_export_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_export_include_directories/ament_cmake_export_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_export_include_directories/ament_cmake_export_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
    found:
      path: "/opt/ros/humble/share/ament_cmake_export_include_directories/cmake/ament_cmake_export_include_directoriesConfig.cmake"
      mode: "config"
      version: "1.3.12"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/home/<USER>/code/pmac_robot/install"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
  -
    kind: "find_package-v1"
    backtrace:
      - "/opt/ros/humble/share/ament_cmake/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)"
      - "/opt/ros/humble/share/ament_cmake/cmake/ament_cmakeConfig.cmake:41 (include)"
      - "CMakeLists.txt:9 (find_package)"
    name: "ament_cmake_export_interfaces"
    configs:
      -
        filename: "ament_cmake_export_interfacesConfig.cmake"
        kind: "cmake"
      -
        filename: "ament_cmake_export_interfaces-config.cmake"
        kind: "cmake"
    version_request:
      exact: false
    settings:
      required: "required_explicit"
      quiet: true
      global: false
      policy_scope: true
      bypass_provider: false
      names:
        - "ament_cmake_export_interfaces"
      path_suffixes:
        - ""
      paths:
        CMAKE_FIND_USE_CMAKE_PATH: true
        CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
        CMAKE_FIND_USE_INSTALL_PREFIX: true
        CMAKE_FIND_USE_PACKAGE_ROOT_PATH: true
        CMAKE_FIND_USE_CMAKE_PACKAGE_REGISTRY: true
        CMAKE_FIND_USE_SYSTEM_PACKAGE_REGISTRY: true
        CMAKE_FIND_ROOT_PATH_MODE: "BOTH"
    candidates:
      -
        path: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/pkgRedirects/ament_cmake_export_interfacesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/pkgRedirects/ament_cmake_export_interfaces-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/install/ament_cmake_export_interfacesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/install/ament_cmake_export_interfaces-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ros2_control/install/ament_cmake_export_interfacesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ros2_control/install/ament_cmake_export_interfaces-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/ament_cmake_export_interfacesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/ament_cmake_export_interfaces-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_manager/ament_cmake_export_interfacesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_manager/ament_cmake_export_interfaces-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_msgs/ament_cmake_export_interfacesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_msgs/ament_cmake_export_interfaces-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/ament_cmake_export_interfacesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/ament_cmake_export_interfaces-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/ament_cmake_export_interfacesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/ament_cmake_export_interfaces-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/ament_cmake_export_interfacesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/ament_cmake_export_interfaces-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/ament_cmake_export_interfacesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/ament_cmake_export_interfaces-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/ament_cmake_export_interfacesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/ament_cmake_export_interfaces-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/ament_cmake_export_interfacesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/ament_cmake_export_interfaces-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver/ament_cmake_export_interfacesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver/ament_cmake_export_interfaces-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_interface/ament_cmake_export_interfacesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_interface/ament_cmake_export_interfaces-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ws_tools/install/ament_lint_auto/ament_cmake_export_interfacesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ws_tools/install/ament_lint_auto/ament_cmake_export_interfaces-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/ament_cmake_export_interfacesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/ament_cmake_export_interfaces-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/cmake/ament_cmake_export_interfacesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/cmake/ament_cmake_export_interfaces-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_export_interfaces/ament_cmake_export_interfacesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_export_interfaces/ament_cmake_export_interfaces-config.cmake"
        mode: "config"
        reason: "no_exist"
    found:
      path: "/opt/ros/humble/share/ament_cmake_export_interfaces/cmake/ament_cmake_export_interfacesConfig.cmake"
      mode: "config"
      version: "1.3.12"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/home/<USER>/code/pmac_robot/install"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
  -
    kind: "find_package-v1"
    backtrace:
      - "/opt/ros/humble/share/ament_cmake/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)"
      - "/opt/ros/humble/share/ament_cmake/cmake/ament_cmakeConfig.cmake:41 (include)"
      - "CMakeLists.txt:9 (find_package)"
    name: "ament_cmake_export_libraries"
    configs:
      -
        filename: "ament_cmake_export_librariesConfig.cmake"
        kind: "cmake"
      -
        filename: "ament_cmake_export_libraries-config.cmake"
        kind: "cmake"
    version_request:
      exact: false
    settings:
      required: "required_explicit"
      quiet: true
      global: false
      policy_scope: true
      bypass_provider: false
      names:
        - "ament_cmake_export_libraries"
      path_suffixes:
        - ""
      paths:
        CMAKE_FIND_USE_CMAKE_PATH: true
        CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
        CMAKE_FIND_USE_INSTALL_PREFIX: true
        CMAKE_FIND_USE_PACKAGE_ROOT_PATH: true
        CMAKE_FIND_USE_CMAKE_PACKAGE_REGISTRY: true
        CMAKE_FIND_USE_SYSTEM_PACKAGE_REGISTRY: true
        CMAKE_FIND_ROOT_PATH_MODE: "BOTH"
    candidates:
      -
        path: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/pkgRedirects/ament_cmake_export_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/pkgRedirects/ament_cmake_export_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/install/ament_cmake_export_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/install/ament_cmake_export_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ros2_control/install/ament_cmake_export_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ros2_control/install/ament_cmake_export_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/ament_cmake_export_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/ament_cmake_export_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_manager/ament_cmake_export_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_manager/ament_cmake_export_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_msgs/ament_cmake_export_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_msgs/ament_cmake_export_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/ament_cmake_export_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/ament_cmake_export_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/ament_cmake_export_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/ament_cmake_export_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/ament_cmake_export_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/ament_cmake_export_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/ament_cmake_export_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/ament_cmake_export_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/ament_cmake_export_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/ament_cmake_export_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/ament_cmake_export_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/ament_cmake_export_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver/ament_cmake_export_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver/ament_cmake_export_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_interface/ament_cmake_export_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_interface/ament_cmake_export_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ws_tools/install/ament_lint_auto/ament_cmake_export_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ws_tools/install/ament_lint_auto/ament_cmake_export_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/ament_cmake_export_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/ament_cmake_export_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/cmake/ament_cmake_export_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/cmake/ament_cmake_export_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_export_libraries/ament_cmake_export_librariesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_export_libraries/ament_cmake_export_libraries-config.cmake"
        mode: "config"
        reason: "no_exist"
    found:
      path: "/opt/ros/humble/share/ament_cmake_export_libraries/cmake/ament_cmake_export_librariesConfig.cmake"
      mode: "config"
      version: "1.3.12"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/home/<USER>/code/pmac_robot/install"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
  -
    kind: "find_package-v1"
    backtrace:
      - "/opt/ros/humble/share/ament_cmake/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)"
      - "/opt/ros/humble/share/ament_cmake/cmake/ament_cmakeConfig.cmake:41 (include)"
      - "CMakeLists.txt:9 (find_package)"
    name: "ament_cmake_export_link_flags"
    configs:
      -
        filename: "ament_cmake_export_link_flagsConfig.cmake"
        kind: "cmake"
      -
        filename: "ament_cmake_export_link_flags-config.cmake"
        kind: "cmake"
    version_request:
      exact: false
    settings:
      required: "required_explicit"
      quiet: true
      global: false
      policy_scope: true
      bypass_provider: false
      names:
        - "ament_cmake_export_link_flags"
      path_suffixes:
        - ""
      paths:
        CMAKE_FIND_USE_CMAKE_PATH: true
        CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
        CMAKE_FIND_USE_INSTALL_PREFIX: true
        CMAKE_FIND_USE_PACKAGE_ROOT_PATH: true
        CMAKE_FIND_USE_CMAKE_PACKAGE_REGISTRY: true
        CMAKE_FIND_USE_SYSTEM_PACKAGE_REGISTRY: true
        CMAKE_FIND_ROOT_PATH_MODE: "BOTH"
    candidates:
      -
        path: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/pkgRedirects/ament_cmake_export_link_flagsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/pkgRedirects/ament_cmake_export_link_flags-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/install/ament_cmake_export_link_flagsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/install/ament_cmake_export_link_flags-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ros2_control/install/ament_cmake_export_link_flagsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ros2_control/install/ament_cmake_export_link_flags-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/ament_cmake_export_link_flagsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/ament_cmake_export_link_flags-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_manager/ament_cmake_export_link_flagsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_manager/ament_cmake_export_link_flags-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_msgs/ament_cmake_export_link_flagsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_msgs/ament_cmake_export_link_flags-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/ament_cmake_export_link_flagsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/ament_cmake_export_link_flags-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/ament_cmake_export_link_flagsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/ament_cmake_export_link_flags-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/ament_cmake_export_link_flagsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/ament_cmake_export_link_flags-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/ament_cmake_export_link_flagsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/ament_cmake_export_link_flags-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/ament_cmake_export_link_flagsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/ament_cmake_export_link_flags-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/ament_cmake_export_link_flagsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/ament_cmake_export_link_flags-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver/ament_cmake_export_link_flagsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver/ament_cmake_export_link_flags-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_interface/ament_cmake_export_link_flagsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_interface/ament_cmake_export_link_flags-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ws_tools/install/ament_lint_auto/ament_cmake_export_link_flagsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ws_tools/install/ament_lint_auto/ament_cmake_export_link_flags-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/ament_cmake_export_link_flagsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/ament_cmake_export_link_flags-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/cmake/ament_cmake_export_link_flagsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/cmake/ament_cmake_export_link_flags-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_export_link_flags/ament_cmake_export_link_flagsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_export_link_flags/ament_cmake_export_link_flags-config.cmake"
        mode: "config"
        reason: "no_exist"
    found:
      path: "/opt/ros/humble/share/ament_cmake_export_link_flags/cmake/ament_cmake_export_link_flagsConfig.cmake"
      mode: "config"
      version: "1.3.12"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/home/<USER>/code/pmac_robot/install"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
  -
    kind: "find_package-v1"
    backtrace:
      - "/opt/ros/humble/share/ament_cmake/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)"
      - "/opt/ros/humble/share/ament_cmake/cmake/ament_cmakeConfig.cmake:41 (include)"
      - "CMakeLists.txt:9 (find_package)"
    name: "ament_cmake_export_targets"
    configs:
      -
        filename: "ament_cmake_export_targetsConfig.cmake"
        kind: "cmake"
      -
        filename: "ament_cmake_export_targets-config.cmake"
        kind: "cmake"
    version_request:
      exact: false
    settings:
      required: "required_explicit"
      quiet: true
      global: false
      policy_scope: true
      bypass_provider: false
      names:
        - "ament_cmake_export_targets"
      path_suffixes:
        - ""
      paths:
        CMAKE_FIND_USE_CMAKE_PATH: true
        CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
        CMAKE_FIND_USE_INSTALL_PREFIX: true
        CMAKE_FIND_USE_PACKAGE_ROOT_PATH: true
        CMAKE_FIND_USE_CMAKE_PACKAGE_REGISTRY: true
        CMAKE_FIND_USE_SYSTEM_PACKAGE_REGISTRY: true
        CMAKE_FIND_ROOT_PATH_MODE: "BOTH"
    candidates:
      -
        path: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/pkgRedirects/ament_cmake_export_targetsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/pkgRedirects/ament_cmake_export_targets-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/install/ament_cmake_export_targetsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/install/ament_cmake_export_targets-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ros2_control/install/ament_cmake_export_targetsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ros2_control/install/ament_cmake_export_targets-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/ament_cmake_export_targetsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/ament_cmake_export_targets-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_manager/ament_cmake_export_targetsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_manager/ament_cmake_export_targets-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_msgs/ament_cmake_export_targetsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_msgs/ament_cmake_export_targets-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/ament_cmake_export_targetsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/ament_cmake_export_targets-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/ament_cmake_export_targetsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/ament_cmake_export_targets-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/ament_cmake_export_targetsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/ament_cmake_export_targets-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/ament_cmake_export_targetsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/ament_cmake_export_targets-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/ament_cmake_export_targetsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/ament_cmake_export_targets-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/ament_cmake_export_targetsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/ament_cmake_export_targets-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver/ament_cmake_export_targetsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver/ament_cmake_export_targets-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_interface/ament_cmake_export_targetsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_interface/ament_cmake_export_targets-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ws_tools/install/ament_lint_auto/ament_cmake_export_targetsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ws_tools/install/ament_lint_auto/ament_cmake_export_targets-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/ament_cmake_export_targetsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/ament_cmake_export_targets-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/cmake/ament_cmake_export_targetsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/cmake/ament_cmake_export_targets-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_export_targets/ament_cmake_export_targetsConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_export_targets/ament_cmake_export_targets-config.cmake"
        mode: "config"
        reason: "no_exist"
    found:
      path: "/opt/ros/humble/share/ament_cmake_export_targets/cmake/ament_cmake_export_targetsConfig.cmake"
      mode: "config"
      version: "1.3.12"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/home/<USER>/code/pmac_robot/install"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
  -
    kind: "find_package-v1"
    backtrace:
      - "/opt/ros/humble/share/ament_cmake/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)"
      - "/opt/ros/humble/share/ament_cmake/cmake/ament_cmakeConfig.cmake:41 (include)"
      - "CMakeLists.txt:9 (find_package)"
    name: "ament_cmake_gen_version_h"
    configs:
      -
        filename: "ament_cmake_gen_version_hConfig.cmake"
        kind: "cmake"
      -
        filename: "ament_cmake_gen_version_h-config.cmake"
        kind: "cmake"
    version_request:
      exact: false
    settings:
      required: "required_explicit"
      quiet: true
      global: false
      policy_scope: true
      bypass_provider: false
      names:
        - "ament_cmake_gen_version_h"
      path_suffixes:
        - ""
      paths:
        CMAKE_FIND_USE_CMAKE_PATH: true
        CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
        CMAKE_FIND_USE_INSTALL_PREFIX: true
        CMAKE_FIND_USE_PACKAGE_ROOT_PATH: true
        CMAKE_FIND_USE_CMAKE_PACKAGE_REGISTRY: true
        CMAKE_FIND_USE_SYSTEM_PACKAGE_REGISTRY: true
        CMAKE_FIND_ROOT_PATH_MODE: "BOTH"
    candidates:
      -
        path: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/pkgRedirects/ament_cmake_gen_version_hConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/pkgRedirects/ament_cmake_gen_version_h-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/install/ament_cmake_gen_version_hConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/install/ament_cmake_gen_version_h-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ros2_control/install/ament_cmake_gen_version_hConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ros2_control/install/ament_cmake_gen_version_h-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/ament_cmake_gen_version_hConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/ament_cmake_gen_version_h-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_manager/ament_cmake_gen_version_hConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_manager/ament_cmake_gen_version_h-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_msgs/ament_cmake_gen_version_hConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_msgs/ament_cmake_gen_version_h-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/ament_cmake_gen_version_hConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/ament_cmake_gen_version_h-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/ament_cmake_gen_version_hConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/ament_cmake_gen_version_h-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/ament_cmake_gen_version_hConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/ament_cmake_gen_version_h-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/ament_cmake_gen_version_hConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/ament_cmake_gen_version_h-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/ament_cmake_gen_version_hConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/ament_cmake_gen_version_h-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/ament_cmake_gen_version_hConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/ament_cmake_gen_version_h-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver/ament_cmake_gen_version_hConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver/ament_cmake_gen_version_h-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_interface/ament_cmake_gen_version_hConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_interface/ament_cmake_gen_version_h-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ws_tools/install/ament_lint_auto/ament_cmake_gen_version_hConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ws_tools/install/ament_lint_auto/ament_cmake_gen_version_h-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/ament_cmake_gen_version_hConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/ament_cmake_gen_version_h-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/cmake/ament_cmake_gen_version_hConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/cmake/ament_cmake_gen_version_h-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_gen_version_h/ament_cmake_gen_version_hConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_gen_version_h/ament_cmake_gen_version_h-config.cmake"
        mode: "config"
        reason: "no_exist"
    found:
      path: "/opt/ros/humble/share/ament_cmake_gen_version_h/cmake/ament_cmake_gen_version_hConfig.cmake"
      mode: "config"
      version: "1.3.12"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/home/<USER>/code/pmac_robot/install"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
  -
    kind: "find_package-v1"
    backtrace:
      - "/opt/ros/humble/share/ament_cmake/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)"
      - "/opt/ros/humble/share/ament_cmake/cmake/ament_cmakeConfig.cmake:41 (include)"
      - "CMakeLists.txt:9 (find_package)"
    name: "ament_cmake_python"
    configs:
      -
        filename: "ament_cmake_pythonConfig.cmake"
        kind: "cmake"
      -
        filename: "ament_cmake_python-config.cmake"
        kind: "cmake"
    version_request:
      exact: false
    settings:
      required: "required_explicit"
      quiet: true
      global: false
      policy_scope: true
      bypass_provider: false
      names:
        - "ament_cmake_python"
      path_suffixes:
        - ""
      paths:
        CMAKE_FIND_USE_CMAKE_PATH: true
        CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
        CMAKE_FIND_USE_INSTALL_PREFIX: true
        CMAKE_FIND_USE_PACKAGE_ROOT_PATH: true
        CMAKE_FIND_USE_CMAKE_PACKAGE_REGISTRY: true
        CMAKE_FIND_USE_SYSTEM_PACKAGE_REGISTRY: true
        CMAKE_FIND_ROOT_PATH_MODE: "BOTH"
    candidates:
      -
        path: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/pkgRedirects/ament_cmake_pythonConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/pkgRedirects/ament_cmake_python-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/install/ament_cmake_pythonConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/install/ament_cmake_python-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ros2_control/install/ament_cmake_pythonConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ros2_control/install/ament_cmake_python-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/ament_cmake_pythonConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/ament_cmake_python-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_manager/ament_cmake_pythonConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_manager/ament_cmake_python-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_msgs/ament_cmake_pythonConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_msgs/ament_cmake_python-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/ament_cmake_pythonConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/ament_cmake_python-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/ament_cmake_pythonConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/ament_cmake_python-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/ament_cmake_pythonConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/ament_cmake_python-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/ament_cmake_pythonConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/ament_cmake_python-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/ament_cmake_pythonConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/ament_cmake_python-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/ament_cmake_pythonConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/ament_cmake_python-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver/ament_cmake_pythonConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver/ament_cmake_python-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_interface/ament_cmake_pythonConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_interface/ament_cmake_python-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ws_tools/install/ament_lint_auto/ament_cmake_pythonConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ws_tools/install/ament_lint_auto/ament_cmake_python-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/ament_cmake_pythonConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/ament_cmake_python-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/cmake/ament_cmake_pythonConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/cmake/ament_cmake_python-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_python/ament_cmake_pythonConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_python/ament_cmake_python-config.cmake"
        mode: "config"
        reason: "no_exist"
    found:
      path: "/opt/ros/humble/share/ament_cmake_python/cmake/ament_cmake_pythonConfig.cmake"
      mode: "config"
      version: "1.3.12"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/home/<USER>/code/pmac_robot/install"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
  -
    kind: "find_package-v1"
    backtrace:
      - "/opt/ros/humble/share/ament_cmake_target_dependencies/cmake/ament_cmake_target_dependencies-extras.cmake:19 (find_package)"
      - "/opt/ros/humble/share/ament_cmake_target_dependencies/cmake/ament_cmake_target_dependenciesConfig.cmake:41 (include)"
      - "/opt/ros/humble/share/ament_cmake/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)"
      - "/opt/ros/humble/share/ament_cmake/cmake/ament_cmakeConfig.cmake:41 (include)"
      - "CMakeLists.txt:9 (find_package)"
    name: "ament_cmake_include_directories"
    configs:
      -
        filename: "ament_cmake_include_directoriesConfig.cmake"
        kind: "cmake"
      -
        filename: "ament_cmake_include_directories-config.cmake"
        kind: "cmake"
    version_request:
      exact: false
    settings:
      required: "required_explicit"
      quiet: true
      global: false
      policy_scope: true
      bypass_provider: false
      names:
        - "ament_cmake_include_directories"
      path_suffixes:
        - ""
      paths:
        CMAKE_FIND_USE_CMAKE_PATH: true
        CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
        CMAKE_FIND_USE_INSTALL_PREFIX: true
        CMAKE_FIND_USE_PACKAGE_ROOT_PATH: true
        CMAKE_FIND_USE_CMAKE_PACKAGE_REGISTRY: true
        CMAKE_FIND_USE_SYSTEM_PACKAGE_REGISTRY: true
        CMAKE_FIND_ROOT_PATH_MODE: "BOTH"
    candidates:
      -
        path: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/pkgRedirects/ament_cmake_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/pkgRedirects/ament_cmake_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/install/ament_cmake_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/install/ament_cmake_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ros2_control/install/ament_cmake_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ros2_control/install/ament_cmake_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/ament_cmake_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/ament_cmake_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_manager/ament_cmake_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_manager/ament_cmake_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_msgs/ament_cmake_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_msgs/ament_cmake_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/ament_cmake_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/ament_cmake_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/ament_cmake_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/ament_cmake_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/ament_cmake_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/ament_cmake_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/ament_cmake_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/ament_cmake_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/ament_cmake_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/ament_cmake_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/ament_cmake_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/ament_cmake_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver/ament_cmake_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver/ament_cmake_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_interface/ament_cmake_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_interface/ament_cmake_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ws_tools/install/ament_lint_auto/ament_cmake_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ws_tools/install/ament_lint_auto/ament_cmake_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/ament_cmake_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/ament_cmake_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/cmake/ament_cmake_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/cmake/ament_cmake_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_include_directories/ament_cmake_include_directoriesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_include_directories/ament_cmake_include_directories-config.cmake"
        mode: "config"
        reason: "no_exist"
    found:
      path: "/opt/ros/humble/share/ament_cmake_include_directories/cmake/ament_cmake_include_directoriesConfig.cmake"
      mode: "config"
      version: "1.3.12"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/home/<USER>/code/pmac_robot/install"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
  -
    kind: "find_package-v1"
    backtrace:
      - "/opt/ros/humble/share/ament_cmake/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)"
      - "/opt/ros/humble/share/ament_cmake/cmake/ament_cmakeConfig.cmake:41 (include)"
      - "CMakeLists.txt:9 (find_package)"
    name: "ament_cmake_target_dependencies"
    configs:
      -
        filename: "ament_cmake_target_dependenciesConfig.cmake"
        kind: "cmake"
      -
        filename: "ament_cmake_target_dependencies-config.cmake"
        kind: "cmake"
    version_request:
      exact: false
    settings:
      required: "required_explicit"
      quiet: true
      global: false
      policy_scope: true
      bypass_provider: false
      names:
        - "ament_cmake_target_dependencies"
      path_suffixes:
        - ""
      paths:
        CMAKE_FIND_USE_CMAKE_PATH: true
        CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
        CMAKE_FIND_USE_INSTALL_PREFIX: true
        CMAKE_FIND_USE_PACKAGE_ROOT_PATH: true
        CMAKE_FIND_USE_CMAKE_PACKAGE_REGISTRY: true
        CMAKE_FIND_USE_SYSTEM_PACKAGE_REGISTRY: true
        CMAKE_FIND_ROOT_PATH_MODE: "BOTH"
    candidates:
      -
        path: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/pkgRedirects/ament_cmake_target_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/pkgRedirects/ament_cmake_target_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/install/ament_cmake_target_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/install/ament_cmake_target_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ros2_control/install/ament_cmake_target_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ros2_control/install/ament_cmake_target_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/ament_cmake_target_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/ament_cmake_target_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_manager/ament_cmake_target_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_manager/ament_cmake_target_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_msgs/ament_cmake_target_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_msgs/ament_cmake_target_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/ament_cmake_target_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/ament_cmake_target_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/ament_cmake_target_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/ament_cmake_target_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/ament_cmake_target_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/ament_cmake_target_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/ament_cmake_target_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/ament_cmake_target_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/ament_cmake_target_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/ament_cmake_target_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/ament_cmake_target_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/ament_cmake_target_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver/ament_cmake_target_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver/ament_cmake_target_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_interface/ament_cmake_target_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_interface/ament_cmake_target_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ws_tools/install/ament_lint_auto/ament_cmake_target_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ws_tools/install/ament_lint_auto/ament_cmake_target_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/ament_cmake_target_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/ament_cmake_target_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/cmake/ament_cmake_target_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/cmake/ament_cmake_target_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_target_dependencies/ament_cmake_target_dependenciesConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_target_dependencies/ament_cmake_target_dependencies-config.cmake"
        mode: "config"
        reason: "no_exist"
    found:
      path: "/opt/ros/humble/share/ament_cmake_target_dependencies/cmake/ament_cmake_target_dependenciesConfig.cmake"
      mode: "config"
      version: "1.3.12"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/home/<USER>/code/pmac_robot/install"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
  -
    kind: "find_package-v1"
    backtrace:
      - "/opt/ros/humble/share/ament_cmake/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)"
      - "/opt/ros/humble/share/ament_cmake/cmake/ament_cmakeConfig.cmake:41 (include)"
      - "CMakeLists.txt:9 (find_package)"
    name: "ament_cmake_test"
    configs:
      -
        filename: "ament_cmake_testConfig.cmake"
        kind: "cmake"
      -
        filename: "ament_cmake_test-config.cmake"
        kind: "cmake"
    version_request:
      exact: false
    settings:
      required: "required_explicit"
      quiet: true
      global: false
      policy_scope: true
      bypass_provider: false
      names:
        - "ament_cmake_test"
      path_suffixes:
        - ""
      paths:
        CMAKE_FIND_USE_CMAKE_PATH: true
        CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
        CMAKE_FIND_USE_INSTALL_PREFIX: true
        CMAKE_FIND_USE_PACKAGE_ROOT_PATH: true
        CMAKE_FIND_USE_CMAKE_PACKAGE_REGISTRY: true
        CMAKE_FIND_USE_SYSTEM_PACKAGE_REGISTRY: true
        CMAKE_FIND_ROOT_PATH_MODE: "BOTH"
    candidates:
      -
        path: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/pkgRedirects/ament_cmake_testConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/pkgRedirects/ament_cmake_test-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/install/ament_cmake_testConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/install/ament_cmake_test-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ros2_control/install/ament_cmake_testConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ros2_control/install/ament_cmake_test-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/ament_cmake_testConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/ament_cmake_test-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_manager/ament_cmake_testConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_manager/ament_cmake_test-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_msgs/ament_cmake_testConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_msgs/ament_cmake_test-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/ament_cmake_testConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/ament_cmake_test-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/ament_cmake_testConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/ament_cmake_test-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/ament_cmake_testConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/ament_cmake_test-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/ament_cmake_testConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/ament_cmake_test-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/ament_cmake_testConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/ament_cmake_test-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/ament_cmake_testConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/ament_cmake_test-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver/ament_cmake_testConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver/ament_cmake_test-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_interface/ament_cmake_testConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_interface/ament_cmake_test-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ws_tools/install/ament_lint_auto/ament_cmake_testConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ws_tools/install/ament_lint_auto/ament_cmake_test-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/ament_cmake_testConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/ament_cmake_test-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/cmake/ament_cmake_testConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/cmake/ament_cmake_test-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_test/ament_cmake_testConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_test/ament_cmake_test-config.cmake"
        mode: "config"
        reason: "no_exist"
    found:
      path: "/opt/ros/humble/share/ament_cmake_test/cmake/ament_cmake_testConfig.cmake"
      mode: "config"
      version: "1.3.12"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/home/<USER>/code/pmac_robot/install"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
  -
    kind: "find_package-v1"
    backtrace:
      - "/opt/ros/humble/share/ament_cmake/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)"
      - "/opt/ros/humble/share/ament_cmake/cmake/ament_cmakeConfig.cmake:41 (include)"
      - "CMakeLists.txt:9 (find_package)"
    name: "ament_cmake_version"
    configs:
      -
        filename: "ament_cmake_versionConfig.cmake"
        kind: "cmake"
      -
        filename: "ament_cmake_version-config.cmake"
        kind: "cmake"
    version_request:
      exact: false
    settings:
      required: "required_explicit"
      quiet: true
      global: false
      policy_scope: true
      bypass_provider: false
      names:
        - "ament_cmake_version"
      path_suffixes:
        - ""
      paths:
        CMAKE_FIND_USE_CMAKE_PATH: true
        CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
        CMAKE_FIND_USE_INSTALL_PREFIX: true
        CMAKE_FIND_USE_PACKAGE_ROOT_PATH: true
        CMAKE_FIND_USE_CMAKE_PACKAGE_REGISTRY: true
        CMAKE_FIND_USE_SYSTEM_PACKAGE_REGISTRY: true
        CMAKE_FIND_ROOT_PATH_MODE: "BOTH"
    candidates:
      -
        path: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/pkgRedirects/ament_cmake_versionConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/pkgRedirects/ament_cmake_version-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/install/ament_cmake_versionConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/install/ament_cmake_version-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ros2_control/install/ament_cmake_versionConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ros2_control/install/ament_cmake_version-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/ament_cmake_versionConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/ament_cmake_version-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_manager/ament_cmake_versionConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_manager/ament_cmake_version-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_msgs/ament_cmake_versionConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_msgs/ament_cmake_version-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/ament_cmake_versionConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/ament_cmake_version-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/ament_cmake_versionConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/ament_cmake_version-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/ament_cmake_versionConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/ament_cmake_version-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/ament_cmake_versionConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/ament_cmake_version-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/ament_cmake_versionConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/ament_cmake_version-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/ament_cmake_versionConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/ament_cmake_version-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver/ament_cmake_versionConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver/ament_cmake_version-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_interface/ament_cmake_versionConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_interface/ament_cmake_version-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ws_tools/install/ament_lint_auto/ament_cmake_versionConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ws_tools/install/ament_lint_auto/ament_cmake_version-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/ament_cmake_versionConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/ament_cmake_version-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/cmake/ament_cmake_versionConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/cmake/ament_cmake_version-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_version/ament_cmake_versionConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_version/ament_cmake_version-config.cmake"
        mode: "config"
        reason: "no_exist"
    found:
      path: "/opt/ros/humble/share/ament_cmake_version/cmake/ament_cmake_versionConfig.cmake"
      mode: "config"
      version: "1.3.12"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/home/<USER>/code/pmac_robot/install"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
  -
    kind: "find_package-v1"
    backtrace:
      - "CMakeLists.txt:9 (find_package)"
    name: "ament_cmake"
    configs:
      -
        filename: "ament_cmakeConfig.cmake"
        kind: "cmake"
      -
        filename: "ament_cmake-config.cmake"
        kind: "cmake"
    version_request:
      exact: false
    settings:
      required: "required_explicit"
      quiet: false
      global: false
      policy_scope: true
      bypass_provider: false
      names:
        - "ament_cmake"
      path_suffixes:
        - ""
      paths:
        CMAKE_FIND_USE_CMAKE_PATH: true
        CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
        CMAKE_FIND_USE_INSTALL_PREFIX: true
        CMAKE_FIND_USE_PACKAGE_ROOT_PATH: true
        CMAKE_FIND_USE_CMAKE_PACKAGE_REGISTRY: true
        CMAKE_FIND_USE_SYSTEM_PACKAGE_REGISTRY: true
        CMAKE_FIND_ROOT_PATH_MODE: "BOTH"
    candidates:
      -
        path: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/pkgRedirects/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/build/pmac_bringup/CMakeFiles/pkgRedirects/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/install/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/pmac_robot/install/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ros2_control/install/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ros2_control/install/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_slave_description/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_manager/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_manager/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_msgs/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_msgs/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_driver/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_interface/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ecat_ws/install/ethercat_interface/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ws_tools/install/ament_lint_auto/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/home/<USER>/code/ws_tools/install/ament_lint_auto/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/cmake/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/cmake/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_gmock/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_gmock/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_cppcheck/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_cppcheck/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_export_definitions/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_export_definitions/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_lint_cmake/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_lint_cmake/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_export_targets/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_export_targets/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_export_include_directories/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_export_include_directories/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_gtest/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_gtest/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_flake8/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_flake8/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_pep257/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_pep257/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_ros/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_ros/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_include_directories/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_include_directories/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_pytest/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_pytest/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_export_link_flags/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_export_link_flags/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_export_interfaces/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_export_interfaces/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_target_dependencies/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_target_dependencies/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_test/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_test/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_gen_version_h/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_gen_version_h/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_uncrustify/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_uncrustify/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_auto/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_auto/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_xmllint/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_xmllint/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_cpplint/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_cpplint/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_version/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_version/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_export_libraries/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_export_libraries/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_export_dependencies/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_export_dependencies/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_core/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_core/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_python/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_python/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_copyright/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_copyright/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_libraries/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_libraries/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_gmock/cmake/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_gmock/cmake/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_cppcheck/cmake/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_cppcheck/cmake/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_export_definitions/cmake/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_export_definitions/cmake/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_lint_cmake/cmake/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_lint_cmake/cmake/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_export_targets/cmake/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_export_targets/cmake/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_export_include_directories/cmake/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_export_include_directories/cmake/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_gtest/cmake/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_gtest/cmake/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_flake8/cmake/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_flake8/cmake/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_pep257/cmake/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_pep257/cmake/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_include_directories/cmake/ament_cmakeConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "/opt/ros/humble/share/ament_cmake_include_directories/cmake/ament_cmake-config.cmake"
        mode: "config"
        reason: "no_exist"
    found:
      path: "/opt/ros/humble/share/ament_cmake/cmake/ament_cmakeConfig.cmake"
      mode: "config"
      version: "1.3.12"
    search_context:
      ENV{CMAKE_PREFIX_PATH}:
        - "/home/<USER>/code/pmac_robot/install"
        - "/home/<USER>/code/ros2_control/install"
        - "/home/<USER>/code/ecat_ws/install/ethercat_slave_description"
        - "/home/<USER>/code/ecat_ws/install/ethercat_manager"
        - "/home/<USER>/code/ecat_ws/install/ethercat_msgs"
        - "/home/<USER>/code/ecat_ws/install/ethercat_motor_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_cia402_drive"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2_examples"
        - "/home/<USER>/code/ecat_ws/install/ethercat_force_sensor"
        - "/home/<USER>/code/ecat_ws/install/ethercat_generic_slave"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver_ros2"
        - "/home/<USER>/code/ecat_ws/install/ethercat_driver"
        - "/home/<USER>/code/ecat_ws/install/ethercat_interface"
        - "/home/<USER>/code/ws_tools/install/ament_lint_auto"
        - "/opt/ros/humble"
        - "/home/<USER>/code/staubli_experimental/install"
        - "/home/<USER>/code/Universal_Robots_ROS2_Driver/install/ur_description"
      ENV{PATH}:
        - "/opt/ros/humble/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/sbin"
        - "/usr/bin"
        - "/sbin"
        - "/bin"
        - "/usr/games"
        - "/usr/local/games"
        - "/snap/bin"
        - "/snap/bin"
        - "/home/<USER>/.fzf/bin"
      CMAKE_INSTALL_PREFIX: "/home/<USER>/code/pmac_robot/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/home/<USER>/code/pmac_robot/install"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
...
