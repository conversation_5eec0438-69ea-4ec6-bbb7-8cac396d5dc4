# generated from ament/cmake/core/templates/nameConfig.cmake.in

# prevent multiple inclusion
if(_pmac_hardware_interface_CONFIG_INCLUDED)
  # ensure to keep the found flag the same
  if(NOT DEFINED pmac_hardware_interface_FOUND)
    # explicitly set it to FALSE, otherwise <PERSON><PERSON><PERSON> will set it to TRUE
    set(pmac_hardware_interface_FOUND FALSE)
  elseif(NOT pmac_hardware_interface_FOUND)
    # use separate condition to avoid uninitialized variable warning
    set(pmac_hardware_interface_FOUND FALSE)
  endif()
  return()
endif()
set(_pmac_hardware_interface_CONFIG_INCLUDED TRUE)

# output package information
if(NOT pmac_hardware_interface_FIND_QUIETLY)
  message(STATUS "Found pmac_hardware_interface: 0.0.0 (${pmac_hardware_interface_DIR})")
endif()

# warn when using a deprecated package
if(NOT "" STREQUAL "")
  set(_msg "Package 'pmac_hardware_interface' is deprecated")
  # append custom deprecation text if available
  if(NOT "" STREQUAL "TRUE")
    set(_msg "${_msg} ()")
  endif()
  # optionally quiet the deprecation message
  if(NOT ${pmac_hardware_interface_DEPRECATED_QUIET})
    message(DEPRECATION "${_msg}")
  endif()
endif()

# flag package as ament-based to distinguish it after being find_package()-ed
set(pmac_hardware_interface_FOUND_AMENT_PACKAGE TRUE)

# include all config extra files
set(_extras "ament_cmake_export_include_directories-extras.cmake;ament_cmake_export_libraries-extras.cmake;ament_cmake_export_dependencies-extras.cmake")
foreach(_extra ${_extras})
  include("${pmac_hardware_interface_DIR}/${_extra}")
endforeach()
