set(_AMENT_PACKAGE_NAME "pmac_hardware_interface")
set(pmac_hardware_interface_VERSION "0.0.0")
set(pmac_hardware_interface_MAINTAINER "hq.<PERSON>hao <<PERSON><PERSON><PERSON><PERSON>@gmail.com>")
set(pmac_hardware_interface_BUILD_DEPENDS "hardware_interface" "pluginlib" "rclcpp" "rclcpp_lifecycle")
set(pmac_hardware_interface_BUILDTOOL_DEPENDS "ament_cmake")
set(pmac_hardware_interface_BUILD_EXPORT_DEPENDS "hardware_interface" "pluginlib" "rclcpp" "rclcpp_lifecycle")
set(pmac_hardware_interface_BUILDTOOL_EXPORT_DEPENDS )
set(pmac_hardware_interface_EXEC_DEPENDS "hardware_interface" "pluginlib" "rclcpp" "rclcpp_lifecycle")
set(pmac_hardware_interface_TEST_DEPENDS "ament_lint_auto" "ament_lint_common" "ament_cmake_gmock" "ros2_control_test_assets")
set(pmac_hardware_interface_GROUP_DEPENDS )
set(pmac_hardware_interface_MEMBER_OF_GROUPS )
set(pmac_hardware_interface_DEPRECATED "")
set(pmac_hardware_interface_EXPORT_TAGS)
list(APPEND pmac_hardware_interface_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
