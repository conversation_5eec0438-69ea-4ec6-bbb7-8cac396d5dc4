[{"directory": "/home/<USER>/code/pmac_robot/build/pmac_hardware_interface", "command": "/usr/bin/c++ -DDEFAULT_RMW_IMPLEMENTATION=rmw_fastrtps_cpp -DPLUGINLIB__DISABLE_BOOST_FUNCTIONS -DRCUTILS_ENABLE_FAULT_INJECTION -Dpmac_hardware_interface_EXPORTS -I/home/<USER>/code/pmac_robot/src/pmac_hardware_interface/include -isystem /home/<USER>/code/ros2_control/install/include/hardware_interface -isystem /opt/ros/humble/include/rclcpp -isystem /opt/ros/humble/include/rclcpp_lifecycle -isystem /home/<USER>/code/ros2_control/install/include/control_msgs -isystem /opt/ros/humble/include/action_msgs -isystem /opt/ros/humble/include/builtin_interfaces -isystem /opt/ros/humble/include/rosidl_runtime_c -isystem /opt/ros/humble/include/rcutils -isystem /opt/ros/humble/include/rosidl_typesupport_interface -isystem /opt/ros/humble/include/unique_identifier_msgs -isystem /opt/ros/humble/include/geometry_msgs -isystem /opt/ros/humble/include/std_msgs -isystem /opt/ros/humble/include/sensor_msgs -isystem /opt/ros/humble/include/trajectory_msgs -isystem /opt/ros/humble/include/fastcdr -isystem /opt/ros/humble/include/rosidl_runtime_cpp -isystem /opt/ros/humble/include/rosidl_typesupport_fastrtps_cpp -isystem /opt/ros/humble/include/rmw -isystem /opt/ros/humble/include/rosidl_typesupport_fastrtps_c -isystem /opt/ros/humble/include/rosidl_typesupport_introspection_c -isystem /opt/ros/humble/include/rosidl_typesupport_introspection_cpp -isystem /opt/ros/humble/include/lifecycle_msgs -isystem /opt/ros/humble/include/pluginlib -isystem /opt/ros/humble/include/ament_index_cpp -isystem /opt/ros/humble/include/class_loader -isystem /opt/ros/humble/include/rcpputils -isystem /opt/ros/humble/include/libstatistics_collector -isystem /opt/ros/humble/include/rcl -isystem /opt/ros/humble/include/rcl_interfaces -isystem /opt/ros/humble/include/rcl_logging_interface -isystem /opt/ros/humble/include/rcl_yaml_param_parser -isystem /opt/ros/humble/include/libyaml_vendor -isystem /opt/ros/humble/include/tracetools -isystem /opt/ros/humble/include/statistics_msgs -isystem /opt/ros/humble/include/rosgraph_msgs -isystem /opt/ros/humble/include/rosidl_typesupport_cpp -isystem /opt/ros/humble/include/rosidl_typesupport_c -isystem /opt/ros/humble/include/rcl_lifecycle -O2 -g -DNDEBUG -fPIC -Wall -Wextra -Wpedantic -o CMakeFiles/pmac_hardware_interface.dir/src/ck3m_primitive.cpp.o -c /home/<USER>/code/pmac_robot/src/pmac_hardware_interface/src/ck3m_primitive.cpp", "file": "/home/<USER>/code/pmac_robot/src/pmac_hardware_interface/src/ck3m_primitive.cpp", "output": "CMakeFiles/pmac_hardware_interface.dir/src/ck3m_primitive.cpp.o"}]