{"artifacts": [{"path": "libpmac_hardware_interface.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "_install", "ament_cmake_symlink_install_targets", "install", "target_link_libraries", "ament_target_dependencies", "set_target_properties", "include", "find_package", "add_compile_options", "target_compile_definitions", "target_include_directories"], "files": ["CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_core/cmake/symlink_install/ament_cmake_symlink_install_targets.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/symlink_install/install.cmake", "/opt/ros/humble/share/ament_cmake_target_dependencies/cmake/ament_target_dependencies.cmake", "/opt/ros/humble/share/rclcpp/cmake/rclcppExport.cmake", "/opt/ros/humble/share/rclcpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rclcpp/cmake/rclcppConfig.cmake", "/opt/ros/humble/share/rclcpp_lifecycle/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rclcpp_lifecycle/cmake/rclcpp_lifecycleConfig.cmake", "/home/<USER>/code/ros2_control/install/share/hardware_interface/cmake/ament_cmake_export_dependencies-extras.cmake", "/home/<USER>/code/ros2_control/install/share/hardware_interface/cmake/hardware_interfaceConfig.cmake", "/opt/ros/humble/share/rclcpp_lifecycle/cmake/rclcpp_lifecycleExport.cmake", "/opt/ros/humble/share/rclcpp_lifecycle/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/rcl_yaml_param_parserExport.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/rcl_yaml_param_parserConfig.cmake", "/opt/ros/humble/share/rcl/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rcl/cmake/rclConfig.cmake", "/opt/ros/humble/share/libstatistics_collector/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/libstatistics_collector/cmake/libstatistics_collectorConfig.cmake", "/opt/ros/humble/share/rclcpp/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rcl/cmake/rclExport.cmake", "/opt/ros/humble/share/rcl/cmake/ament_cmake_export_targets-extras.cmake", "/home/<USER>/code/ros2_control/install/share/hardware_interface/cmake/export_hardware_interfaceExport.cmake", "/home/<USER>/code/ros2_control/install/share/hardware_interface/cmake/ament_cmake_export_targets-extras.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/export_control_msgs__rosidl_typesupport_fastrtps_cExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/ament_cmake_export_targets-extras.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/control_msgsConfig.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/export_control_msgs__rosidl_typesupport_fastrtps_cppExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/control_msgs__rosidl_typesupport_introspection_cExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/control_msgs__rosidl_typesupport_introspection_cppExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/control_msgs__rosidl_typesupport_cppExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/export_control_msgs__rosidl_generator_pyExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/control_msgs__rosidl_typesupport_cExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/export_control_msgs__rosidl_generator_cExport.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppExport.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppConfig.cmake", "/opt/ros/humble/share/builtin_interfaces/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/builtin_interfaces/cmake/builtin_interfacesConfig.cmake", "/opt/ros/humble/share/action_msgs/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/action_msgs/cmake/action_msgsConfig.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/pluginlib/cmake/export_pluginlibExport.cmake", "/opt/ros/humble/share/pluginlib/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/pluginlib/cmake/pluginlibConfig.cmake", "/opt/ros/humble/share/class_loader/cmake/class_loaderExport.cmake", "/opt/ros/humble/share/class_loader/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/class_loader/cmake/class_loaderConfig.cmake", "/opt/ros/humble/share/pluginlib/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rcutils/cmake/rcutilsExport.cmake", "/opt/ros/humble/share/rcutils/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcutils/cmake/rcutilsConfig.cmake", "/opt/ros/humble/share/rosidl_runtime_c/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rosidl_runtime_c/cmake/rosidl_runtime_cConfig.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 15, "parent": 0}, {"command": 3, "file": 0, "line": 37, "parent": 0}, {"command": 2, "file": 2, "line": 37, "parent": 2}, {"command": 1, "file": 1, "line": 50, "parent": 3}, {"command": 5, "file": 0, "line": 25, "parent": 0}, {"command": 4, "file": 3, "line": 145, "parent": 5}, {"command": 8, "file": 0, "line": 10, "parent": 0}, {"file": 10, "parent": 7}, {"command": 7, "file": 10, "line": 41, "parent": 8}, {"file": 9, "parent": 9}, {"command": 8, "file": 9, "line": 21, "parent": 10}, {"file": 8, "parent": 11}, {"command": 7, "file": 8, "line": 41, "parent": 12}, {"file": 7, "parent": 13}, {"command": 8, "file": 7, "line": 21, "parent": 14}, {"file": 6, "parent": 15}, {"command": 7, "file": 6, "line": 41, "parent": 16}, {"file": 5, "parent": 17}, {"command": 7, "file": 5, "line": 9, "parent": 18}, {"file": 4, "parent": 19}, {"command": 6, "file": 4, "line": 56, "parent": 20}, {"command": 7, "file": 8, "line": 41, "parent": 12}, {"file": 12, "parent": 22}, {"command": 7, "file": 12, "line": 9, "parent": 23}, {"file": 11, "parent": 24}, {"command": 6, "file": 11, "line": 56, "parent": 25}, {"command": 7, "file": 6, "line": 41, "parent": 16}, {"file": 20, "parent": 27}, {"command": 8, "file": 20, "line": 21, "parent": 28}, {"file": 19, "parent": 29}, {"command": 7, "file": 19, "line": 41, "parent": 30}, {"file": 18, "parent": 31}, {"command": 8, "file": 18, "line": 21, "parent": 32}, {"file": 17, "parent": 33}, {"command": 7, "file": 17, "line": 41, "parent": 34}, {"file": 16, "parent": 35}, {"command": 8, "file": 16, "line": 21, "parent": 36}, {"file": 15, "parent": 37}, {"command": 7, "file": 15, "line": 41, "parent": 38}, {"file": 14, "parent": 39}, {"command": 7, "file": 14, "line": 9, "parent": 40}, {"file": 13, "parent": 41}, {"command": 6, "file": 13, "line": 56, "parent": 42}, {"command": 7, "file": 17, "line": 41, "parent": 34}, {"file": 22, "parent": 44}, {"command": 7, "file": 22, "line": 9, "parent": 45}, {"file": 21, "parent": 46}, {"command": 6, "file": 21, "line": 56, "parent": 47}, {"command": 7, "file": 10, "line": 41, "parent": 8}, {"file": 24, "parent": 49}, {"command": 7, "file": 24, "line": 9, "parent": 50}, {"file": 23, "parent": 51}, {"command": 6, "file": 23, "line": 61, "parent": 52}, {"command": 8, "file": 9, "line": 21, "parent": 10}, {"file": 27, "parent": 54}, {"command": 7, "file": 27, "line": 41, "parent": 55}, {"file": 26, "parent": 56}, {"command": 7, "file": 26, "line": 9, "parent": 57}, {"file": 25, "parent": 58}, {"command": 6, "file": 25, "line": 61, "parent": 59}, {"command": 7, "file": 26, "line": 9, "parent": 57}, {"file": 28, "parent": 61}, {"command": 6, "file": 28, "line": 61, "parent": 62}, {"command": 7, "file": 26, "line": 9, "parent": 57}, {"file": 29, "parent": 64}, {"command": 6, "file": 29, "line": 61, "parent": 65}, {"command": 7, "file": 26, "line": 9, "parent": 57}, {"file": 30, "parent": 67}, {"command": 6, "file": 30, "line": 61, "parent": 68}, {"command": 7, "file": 26, "line": 9, "parent": 57}, {"file": 31, "parent": 70}, {"command": 6, "file": 31, "line": 61, "parent": 71}, {"command": 7, "file": 26, "line": 9, "parent": 57}, {"file": 32, "parent": 73}, {"command": 6, "file": 32, "line": 61, "parent": 74}, {"command": 7, "file": 26, "line": 9, "parent": 57}, {"file": 33, "parent": 76}, {"command": 6, "file": 33, "line": 61, "parent": 77}, {"command": 7, "file": 26, "line": 9, "parent": 57}, {"file": 34, "parent": 79}, {"command": 6, "file": 34, "line": 61, "parent": 80}, {"command": 7, "file": 27, "line": 41, "parent": 55}, {"file": 42, "parent": 82}, {"command": 8, "file": 42, "line": 21, "parent": 83}, {"file": 41, "parent": 84}, {"command": 7, "file": 41, "line": 41, "parent": 85}, {"file": 40, "parent": 86}, {"command": 8, "file": 40, "line": 21, "parent": 87}, {"file": 39, "parent": 88}, {"command": 7, "file": 39, "line": 41, "parent": 89}, {"file": 38, "parent": 90}, {"command": 8, "file": 38, "line": 21, "parent": 91}, {"file": 37, "parent": 92}, {"command": 7, "file": 37, "line": 41, "parent": 93}, {"file": 36, "parent": 94}, {"command": 7, "file": 36, "line": 9, "parent": 95}, {"file": 35, "parent": 96}, {"command": 6, "file": 35, "line": 56, "parent": 97}, {"command": 8, "file": 9, "line": 21, "parent": 10}, {"file": 45, "parent": 99}, {"command": 7, "file": 45, "line": 41, "parent": 100}, {"file": 44, "parent": 101}, {"command": 7, "file": 44, "line": 9, "parent": 102}, {"file": 43, "parent": 103}, {"command": 6, "file": 43, "line": 56, "parent": 104}, {"command": 7, "file": 45, "line": 41, "parent": 100}, {"file": 49, "parent": 106}, {"command": 8, "file": 49, "line": 21, "parent": 107}, {"file": 48, "parent": 108}, {"command": 7, "file": 48, "line": 41, "parent": 109}, {"file": 47, "parent": 110}, {"command": 7, "file": 47, "line": 9, "parent": 111}, {"file": 46, "parent": 112}, {"command": 6, "file": 46, "line": 56, "parent": 113}, {"command": 8, "file": 38, "line": 21, "parent": 91}, {"file": 54, "parent": 115}, {"command": 7, "file": 54, "line": 41, "parent": 116}, {"file": 53, "parent": 117}, {"command": 8, "file": 53, "line": 21, "parent": 118}, {"file": 52, "parent": 119}, {"command": 7, "file": 52, "line": 41, "parent": 120}, {"file": 51, "parent": 121}, {"command": 7, "file": 51, "line": 9, "parent": 122}, {"file": 50, "parent": 123}, {"command": 6, "file": 50, "line": 56, "parent": 124}, {"command": 9, "file": 0, "line": 5, "parent": 0}, {"command": 10, "file": 0, "line": 32, "parent": 0}, {"command": 11, "file": 0, "line": 20, "parent": 0}, {"command": 11, "file": 3, "line": 141, "parent": 5}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-O2 -g -DNDEBUG -fPIC"}, {"backtrace": 126, "fragment": "-Wall"}, {"backtrace": 126, "fragment": "-Wextra"}, {"backtrace": 126, "fragment": "-Wpedantic"}], "defines": [{"backtrace": 6, "define": "DEFAULT_RMW_IMPLEMENTATION=rmw_fastrtps_cpp"}, {"backtrace": 127, "define": "PLUGINLIB__DISABLE_BOOST_FUNCTIONS"}, {"backtrace": 6, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}, {"define": "pmac_hardware_interface_EXPORTS"}], "includes": [{"backtrace": 128, "path": "/home/<USER>/code/pmac_robot/src/pmac_hardware_interface/include"}, {"backtrace": 129, "isSystem": true, "path": "/home/<USER>/code/ros2_control/install/include/hardware_interface"}, {"backtrace": 129, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp"}, {"backtrace": 129, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp_lifecycle"}, {"backtrace": 6, "isSystem": true, "path": "/home/<USER>/code/ros2_control/install/include/control_msgs"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/action_msgs"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/unique_identifier_msgs"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/geometry_msgs"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/sensor_msgs"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/trajectory_msgs"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/fastcdr"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_cpp"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rmw"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_c"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_cpp"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/lifecycle_msgs"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/pluginlib"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/ament_index_cpp"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/class_loader"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rcpputils"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/libstatistics_collector"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rcl"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rcl_interfaces"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rcl_logging_interface"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rcl_yaml_param_parser"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/libyaml_vendor"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/tracetools"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/statistics_msgs"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rosgraph_msgs"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_cpp"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_c"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rcl_lifecycle"}], "language": "CXX", "sourceIndexes": [0]}], "id": "pmac_hardware_interface::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 4, "path": "lib"}, {"backtrace": 4, "path": "lib"}], "prefix": {"path": "/home/<USER>/code/pmac_robot/install"}}, "link": {"commandFragments": [{"fragment": "-shared", "role": "flags"}, {"fragment": "-Wl,-rpath,/home/<USER>/code/ros2_control/install/lib:/opt/ros/humble/lib:", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/code/ros2_control/install/lib/libfake_components.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/code/ros2_control/install/lib/libmock_components.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/code/ros2_control/install/lib/libhardware_interface.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/librclcpp_lifecycle.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/humble/lib/librclcpp.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/liblibstatistics_collector.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 26, "fragment": "/opt/ros/humble/lib/librcl_lifecycle.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/librcl.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/librcl_yaml_param_parser.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/humble/lib/libyaml.so", "role": "libraries"}, {"backtrace": 48, "fragment": "/opt/ros/humble/lib/librmw_implementation.so", "role": "libraries"}, {"backtrace": 48, "fragment": "/opt/ros/humble/lib/librcl_logging_spdlog.so", "role": "libraries"}, {"backtrace": 48, "fragment": "/opt/ros/humble/lib/librcl_logging_interface.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/libtracetools.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 60, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 60, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 60, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 60, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 60, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 60, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 63, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 63, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 63, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 63, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 63, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 63, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 66, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 66, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 66, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 66, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 66, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 66, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 69, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 69, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 69, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 69, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 69, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 69, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 72, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 72, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 72, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 72, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 72, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 72, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 75, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 78, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 81, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 75, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 78, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 81, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 75, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 78, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 81, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 75, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 78, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 81, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 75, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 78, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 81, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 75, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 78, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 81, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 60, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 60, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 60, "fragment": "/opt/ros/humble/lib/libfastcdr.so.1.0.24", "role": "libraries"}, {"backtrace": 63, "fragment": "/opt/ros/humble/lib/librmw.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 69, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 66, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 98, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 81, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 75, "fragment": "/usr/lib/x86_64-linux-gnu/libpython3.10.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/libament_index_cpp.so", "role": "libraries"}, {"backtrace": 105, "fragment": "/opt/ros/humble/lib/libclass_loader.so", "role": "libraries"}, {"backtrace": 114, "fragment": "/usr/lib/x86_64-linux-gnu/libconsole_bridge.so.1.0", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/humble/lib/librcpputils.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"backtrace": 125, "fragment": "-ldl", "role": "libraries"}, {"backtrace": 53, "fragment": "/usr/lib/x86_64-linux-gnu/libtinyxml2.so", "role": "libraries"}], "language": "CXX"}, "name": "pmac_hardware_interface", "nameOnDisk": "libpmac_hardware_interface.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/ck3m_primitive.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}