{"backtraceGraph": {"commands": ["_install", "install", "include", "find_package", "ament_cmake_symlink_install_targets"], "files": ["/opt/ros/humble/share/ament_cmake_core/cmake/symlink_install/install.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/ament_cmake_symlink_install-extras.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/ament_cmake_coreConfig.cmake", "/opt/ros/humble/share/ament_cmake/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/ament_cmake/cmake/ament_cmakeConfig.cmake", "CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_core/cmake/symlink_install/ament_cmake_symlink_install_targets.cmake"], "nodes": [{"file": 5}, {"command": 3, "file": 5, "line": 9, "parent": 0}, {"file": 4, "parent": 1}, {"command": 2, "file": 4, "line": 41, "parent": 2}, {"file": 3, "parent": 3}, {"command": 3, "file": 3, "line": 15, "parent": 4}, {"file": 2, "parent": 5}, {"command": 2, "file": 2, "line": 41, "parent": 6}, {"file": 1, "parent": 7}, {"command": 1, "file": 1, "line": 47, "parent": 8}, {"command": 0, "file": 0, "line": 43, "parent": 9}, {"command": 1, "file": 5, "line": 37, "parent": 0}, {"command": 4, "file": 0, "line": 37, "parent": 11}, {"command": 0, "file": 6, "line": 50, "parent": 12}]}, "installers": [{"backtrace": 10, "component": "Unspecified", "scriptFile": "/home/<USER>/code/pmac_robot/build/pmac_hardware_interface/ament_cmake_symlink_install/ament_cmake_symlink_install.cmake", "type": "script"}, {"backtrace": 13, "component": "Unspecified", "destination": "lib", "paths": ["libpmac_hardware_interface.so"], "targetId": "pmac_hardware_interface::@6890427a1f51a3e7e1df", "targetIndex": 0, "type": "target"}], "paths": {"build": ".", "source": "."}}