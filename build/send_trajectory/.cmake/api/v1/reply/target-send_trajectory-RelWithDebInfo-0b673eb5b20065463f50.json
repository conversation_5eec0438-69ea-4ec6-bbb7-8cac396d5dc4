{"artifacts": [{"path": "send_trajectory"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "ament_target_dependencies", "set_target_properties", "include", "find_package", "add_compile_options", "target_include_directories"], "files": ["CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_target_dependencies/cmake/ament_target_dependencies.cmake", "/opt/ros/humble/share/kdl_parser/cmake/export_kdl_parserExport.cmake", "/opt/ros/humble/share/kdl_parser/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/kdl_parser/cmake/kdl_parserConfig.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/export_control_msgs__rosidl_typesupport_fastrtps_cExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/ament_cmake_export_targets-extras.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/control_msgsConfig.cmake", "/opt/ros/humble/share/trajectory_msgs/cmake/export_trajectory_msgs__rosidl_typesupport_fastrtps_cExport.cmake", "/opt/ros/humble/share/trajectory_msgs/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/trajectory_msgs/cmake/trajectory_msgsConfig.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/export_control_msgs__rosidl_typesupport_fastrtps_cppExport.cmake", "/opt/ros/humble/share/trajectory_msgs/cmake/export_trajectory_msgs__rosidl_typesupport_fastrtps_cppExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/control_msgs__rosidl_typesupport_introspection_cExport.cmake", "/opt/ros/humble/share/trajectory_msgs/cmake/trajectory_msgs__rosidl_typesupport_introspection_cExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/control_msgs__rosidl_typesupport_introspection_cppExport.cmake", "/opt/ros/humble/share/trajectory_msgs/cmake/trajectory_msgs__rosidl_typesupport_introspection_cppExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/control_msgs__rosidl_typesupport_cppExport.cmake", "/opt/ros/humble/share/trajectory_msgs/cmake/trajectory_msgs__rosidl_typesupport_cppExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/export_control_msgs__rosidl_generator_pyExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/control_msgs__rosidl_typesupport_cExport.cmake", "/home/<USER>/code/ros2_control/install/share/control_msgs/cmake/export_control_msgs__rosidl_generator_cExport.cmake", "/opt/ros/humble/share/trajectory_msgs/cmake/export_trajectory_msgs__rosidl_generator_pyExport.cmake", "/opt/ros/humble/share/trajectory_msgs/cmake/trajectory_msgs__rosidl_typesupport_cExport.cmake", "/opt/ros/humble/share/trajectory_msgs/cmake/export_trajectory_msgs__rosidl_generator_cExport.cmake", "/opt/ros/humble/share/rclcpp/cmake/rclcppExport.cmake", "/opt/ros/humble/share/rclcpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rclcpp/cmake/rclcppConfig.cmake", "/opt/ros/humble/share/rcl/cmake/rclExport.cmake", "/opt/ros/humble/share/rcl/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcl/cmake/rclConfig.cmake", "/opt/ros/humble/share/libstatistics_collector/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/libstatistics_collector/cmake/libstatistics_collectorConfig.cmake", "/opt/ros/humble/share/rclcpp/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/rcl_yaml_param_parserExport.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/rcl_yaml_param_parserConfig.cmake", "/opt/ros/humble/share/rcl/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppExport.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppConfig.cmake", "/opt/ros/humble/share/builtin_interfaces/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/builtin_interfaces/cmake/builtin_interfacesConfig.cmake", "/opt/ros/humble/share/trajectory_msgs/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rcutils/cmake/rcutilsExport.cmake", "/opt/ros/humble/share/rcutils/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcutils/cmake/rcutilsConfig.cmake", "/opt/ros/humble/share/kdl_parser/cmake/ament_cmake_export_dependencies-extras.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 22, "parent": 0}, {"command": 2, "file": 0, "line": 24, "parent": 0}, {"command": 1, "file": 1, "line": 145, "parent": 2}, {"command": 5, "file": 0, "line": 16, "parent": 0}, {"file": 4, "parent": 4}, {"command": 4, "file": 4, "line": 41, "parent": 5}, {"file": 3, "parent": 6}, {"command": 4, "file": 3, "line": 9, "parent": 7}, {"file": 2, "parent": 8}, {"command": 3, "file": 2, "line": 56, "parent": 9}, {"command": 5, "file": 0, "line": 19, "parent": 0}, {"file": 7, "parent": 11}, {"command": 4, "file": 7, "line": 41, "parent": 12}, {"file": 6, "parent": 13}, {"command": 4, "file": 6, "line": 9, "parent": 14}, {"file": 5, "parent": 15}, {"command": 3, "file": 5, "line": 61, "parent": 16}, {"command": 5, "file": 0, "line": 17, "parent": 0}, {"file": 10, "parent": 18}, {"command": 4, "file": 10, "line": 41, "parent": 19}, {"file": 9, "parent": 20}, {"command": 4, "file": 9, "line": 9, "parent": 21}, {"file": 8, "parent": 22}, {"command": 3, "file": 8, "line": 56, "parent": 23}, {"command": 4, "file": 6, "line": 9, "parent": 14}, {"file": 11, "parent": 25}, {"command": 3, "file": 11, "line": 61, "parent": 26}, {"command": 4, "file": 9, "line": 9, "parent": 21}, {"file": 12, "parent": 28}, {"command": 3, "file": 12, "line": 56, "parent": 29}, {"command": 4, "file": 6, "line": 9, "parent": 14}, {"file": 13, "parent": 31}, {"command": 3, "file": 13, "line": 61, "parent": 32}, {"command": 4, "file": 9, "line": 9, "parent": 21}, {"file": 14, "parent": 34}, {"command": 3, "file": 14, "line": 56, "parent": 35}, {"command": 4, "file": 6, "line": 9, "parent": 14}, {"file": 15, "parent": 37}, {"command": 3, "file": 15, "line": 61, "parent": 38}, {"command": 4, "file": 9, "line": 9, "parent": 21}, {"file": 16, "parent": 40}, {"command": 3, "file": 16, "line": 56, "parent": 41}, {"command": 4, "file": 6, "line": 9, "parent": 14}, {"file": 17, "parent": 43}, {"command": 3, "file": 17, "line": 61, "parent": 44}, {"command": 4, "file": 9, "line": 9, "parent": 21}, {"file": 18, "parent": 46}, {"command": 3, "file": 18, "line": 56, "parent": 47}, {"command": 4, "file": 6, "line": 9, "parent": 14}, {"file": 19, "parent": 49}, {"command": 3, "file": 19, "line": 61, "parent": 50}, {"command": 4, "file": 6, "line": 9, "parent": 14}, {"file": 20, "parent": 52}, {"command": 3, "file": 20, "line": 61, "parent": 53}, {"command": 4, "file": 6, "line": 9, "parent": 14}, {"file": 21, "parent": 55}, {"command": 3, "file": 21, "line": 61, "parent": 56}, {"command": 4, "file": 9, "line": 9, "parent": 21}, {"file": 22, "parent": 58}, {"command": 3, "file": 22, "line": 56, "parent": 59}, {"command": 4, "file": 9, "line": 9, "parent": 21}, {"file": 23, "parent": 61}, {"command": 3, "file": 23, "line": 56, "parent": 62}, {"command": 4, "file": 9, "line": 9, "parent": 21}, {"file": 24, "parent": 64}, {"command": 3, "file": 24, "line": 56, "parent": 65}, {"command": 5, "file": 0, "line": 18, "parent": 0}, {"file": 27, "parent": 67}, {"command": 4, "file": 27, "line": 41, "parent": 68}, {"file": 26, "parent": 69}, {"command": 4, "file": 26, "line": 9, "parent": 70}, {"file": 25, "parent": 71}, {"command": 3, "file": 25, "line": 56, "parent": 72}, {"command": 4, "file": 27, "line": 41, "parent": 68}, {"file": 33, "parent": 74}, {"command": 5, "file": 33, "line": 21, "parent": 75}, {"file": 32, "parent": 76}, {"command": 4, "file": 32, "line": 41, "parent": 77}, {"file": 31, "parent": 78}, {"command": 5, "file": 31, "line": 21, "parent": 79}, {"file": 30, "parent": 80}, {"command": 4, "file": 30, "line": 41, "parent": 81}, {"file": 29, "parent": 82}, {"command": 4, "file": 29, "line": 9, "parent": 83}, {"file": 28, "parent": 84}, {"command": 3, "file": 28, "line": 56, "parent": 85}, {"command": 4, "file": 30, "line": 41, "parent": 81}, {"file": 37, "parent": 87}, {"command": 5, "file": 37, "line": 21, "parent": 88}, {"file": 36, "parent": 89}, {"command": 4, "file": 36, "line": 41, "parent": 90}, {"file": 35, "parent": 91}, {"command": 4, "file": 35, "line": 9, "parent": 92}, {"file": 34, "parent": 93}, {"command": 3, "file": 34, "line": 56, "parent": 94}, {"command": 4, "file": 10, "line": 41, "parent": 19}, {"file": 43, "parent": 96}, {"command": 5, "file": 43, "line": 21, "parent": 97}, {"file": 42, "parent": 98}, {"command": 4, "file": 42, "line": 41, "parent": 99}, {"file": 41, "parent": 100}, {"command": 5, "file": 41, "line": 21, "parent": 101}, {"file": 40, "parent": 102}, {"command": 4, "file": 40, "line": 41, "parent": 103}, {"file": 39, "parent": 104}, {"command": 4, "file": 39, "line": 9, "parent": 105}, {"file": 38, "parent": 106}, {"command": 3, "file": 38, "line": 56, "parent": 107}, {"command": 4, "file": 4, "line": 41, "parent": 5}, {"file": 47, "parent": 109}, {"command": 5, "file": 47, "line": 21, "parent": 110}, {"file": 46, "parent": 111}, {"command": 4, "file": 46, "line": 41, "parent": 112}, {"file": 45, "parent": 113}, {"command": 4, "file": 45, "line": 9, "parent": 114}, {"file": 44, "parent": 115}, {"command": 3, "file": 44, "line": 56, "parent": 116}, {"command": 6, "file": 0, "line": 5, "parent": 0}, {"command": 7, "file": 1, "line": 141, "parent": 2}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-O2 -g -DNDEBUG"}, {"backtrace": 118, "fragment": "-Wall"}, {"backtrace": 118, "fragment": "-Wextra"}, {"backtrace": 118, "fragment": "-Wpedantic"}], "defines": [{"backtrace": 3, "define": "DEFAULT_RMW_IMPLEMENTATION=rmw_fastrtps_cpp"}, {"backtrace": 3, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}], "includes": [{"backtrace": 119, "isSystem": true, "path": "/home/<USER>/code/ros2_control/install/include/control_msgs"}, {"backtrace": 119, "isSystem": true, "path": "/opt/ros/humble/include/kdl_parser"}, {"backtrace": 119, "isSystem": true, "path": "/opt/ros/humble/include/trajectory_msgs"}, {"backtrace": 119, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp"}, {"backtrace": 3, "isSystem": true, "path": "/usr/include/eigen3"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/urdfdom_headers"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/geometry_msgs"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/fastcdr"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_cpp"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rmw"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_c"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_cpp"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/action_msgs"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/unique_identifier_msgs"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/sensor_msgs"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/ament_index_cpp"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/libstatistics_collector"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rcl"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rcl_interfaces"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rcl_logging_interface"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rcl_yaml_param_parser"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/libyaml_vendor"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/tracetools"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rcpputils"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/statistics_msgs"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rosgraph_msgs"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_cpp"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_c"}], "language": "CXX", "sourceIndexes": [0]}], "id": "send_trajectory::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-O2 -g -DNDEBUG", "role": "flags"}, {"fragment": "-Wl,-rpath,/home/<USER>/code/ros2_control/install/lib:/opt/ros/humble/lib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libkdl_parser.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/librclcpp.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/usr/lib/x86_64-linux-gnu/liborocos-kdl.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 17, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 17, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 17, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 27, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 27, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 27, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 36, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 36, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 39, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 39, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 39, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 45, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 45, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 45, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 48, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 48, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/home/<USER>/code/ros2_control/install/lib/libcontrol_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 51, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 54, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 57, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 51, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 54, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 57, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 51, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 60, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 60, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 54, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 63, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 63, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 57, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 66, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 66, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/liblibstatistics_collector.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/librcl.so", "role": "libraries"}, {"backtrace": 86, "fragment": "/opt/ros/humble/lib/librmw_implementation.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/libament_index_cpp.so", "role": "libraries"}, {"backtrace": 86, "fragment": "/opt/ros/humble/lib/librcl_logging_spdlog.so", "role": "libraries"}, {"backtrace": 86, "fragment": "/opt/ros/humble/lib/librcl_logging_interface.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/librcl_yaml_param_parser.so", "role": "libraries"}, {"backtrace": 95, "fragment": "/opt/ros/humble/lib/libyaml.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libfastcdr.so.1.0.24", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/humble/lib/librmw.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 36, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 36, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 48, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 60, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 60, "fragment": "/usr/lib/x86_64-linux-gnu/libpython3.10.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 63, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 66, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 108, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 66, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/librcpputils.so", "role": "libraries"}, {"backtrace": 66, "fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"backtrace": 117, "fragment": "-ldl", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/libtracetools.so", "role": "libraries"}], "language": "CXX"}, "name": "send_trajectory", "nameOnDisk": "send_trajectory", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/send_trajctory.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}