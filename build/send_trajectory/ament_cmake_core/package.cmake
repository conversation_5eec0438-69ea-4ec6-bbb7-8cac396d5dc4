set(_AMENT_PACKAGE_NAME "send_trajectory")
set(send_trajectory_VERSION "0.0.0")
set(send_trajectory_MAINTAINER "hq.zhao <<PERSON><PERSON><PERSON><PERSON>@gmail.com>")
set(send_trajectory_BUILD_DEPENDS "kdl_parser" "rclcpp" "trajectory_msgs")
set(send_trajectory_BUILDTOOL_DEPENDS "ament_cmake")
set(send_trajectory_BUILD_EXPORT_DEPENDS "kdl_parser" "rclcpp" "trajectory_msgs")
set(send_trajectory_BUILDTOOL_EXPORT_DEPENDS )
set(send_trajectory_EXEC_DEPENDS "ros2launch" "ros2launch" "kdl_parser" "rclcpp" "trajectory_msgs")
set(send_trajectory_TEST_DEPENDS "ament_lint_auto" "ament_lint_common")
set(send_trajectory_GROUP_DEPENDS )
set(send_trajectory_MEMBER_OF_GROUPS )
set(send_trajectory_DEPRECATED "")
set(send_trajectory_EXPORT_TAGS)
list(APPEND send_trajectory_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
